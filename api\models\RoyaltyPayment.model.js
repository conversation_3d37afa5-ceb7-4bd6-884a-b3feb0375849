const mongoose = require('mongoose');

const royaltyPaymentSchema = new mongoose.Schema({
  paymentId: { type: String, unique: true, required: true },
  
  // Payment period
  period: {
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    periodType: {
      type: String,
      enum: ['monthly', 'quarterly', 'semi_annual', 'annual', 'custom'],
      required: true
    }
  },
  
  // Venue/Store information
  venue: {
    storeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Store', required: true },
    name: String,
    licenseId: { type: mongoose.Schema.Types.ObjectId, ref: 'VenueLicense' }
  },
  
  // Organization (SAMRO/SAMPRA)
  organization: {
    type: String,
    enum: ['SAMRO', 'SAMPRA', 'RISA', 'Combined'],
    required: true
  },
  
  // Usage data summary
  usageData: {
    totalTracks: Number,
    totalPlays: Number,
    totalDuration: Number, // in seconds
    uniqueArtists: Number,
    uniqueComposers: Number,
    reportingPeriodDays: Number
  },
  
  // SAMRO-specific calculations
  samroRoyalties: {
    performanceRoyalties: Number,
    mechanicalRoyalties: Number,
    synchronizationRoyalties: Number,
    totalSamroRoyalties: Number,
    
    // Breakdown by rights holders
    rightsHolderBreakdown: [{
      rightsHolderName: String,
      ipiNumber: String,
      role: { type: String, enum: ['composer', 'lyricist', 'arranger', 'publisher'] },
      percentage: Number,
      amount: Number,
      trackCount: Number
    }],
    
    // Rate information used
    rateCategory: String,
    baseRate: Number,
    appliedMultipliers: [{
      type: String,
      value: Number,
      description: String
    }]
  },
  
  // SAMPRA-specific calculations
  sampraRoyalties: {
    needletimeRoyalties: Number,
    digitalRoyalties: Number,
    totalSampraRoyalties: Number,
    
    // Breakdown by recording rights holders
    recordingRightsBreakdown: [{
      artistName: String,
      sampraNumber: String,
      role: { type: String, enum: ['performer', 'producer', 'label'] },
      percentage: Number,
      amount: Number,
      trackCount: Number
    }],
    
    // Rate information used
    rateCategory: String,
    baseRate: Number,
    appliedMultipliers: [{
      type: String,
      value: Number,
      description: String
    }]
  },
  
  // Financial summary
  financialSummary: {
    subtotal: Number,
    administrationFee: Number,
    vatAmount: Number,
    vatRate: { type: Number, default: 15 },
    totalAmount: Number,
    currency: { type: String, default: 'ZAR' },
    
    // Payment breakdown
    samroAmount: Number,
    sampraAmount: Number,
    risaAmount: Number,
    otherFees: Number
  },
  
  // Payment status and tracking
  paymentStatus: {
    type: String,
    enum: ['calculated', 'pending_approval', 'approved', 'invoiced', 'paid', 'overdue', 'disputed', 'cancelled'],
    default: 'calculated'
  },
  
  paymentDetails: {
    invoiceNumber: String,
    invoiceDate: Date,
    dueDate: Date,
    paidDate: Date,
    paymentMethod: {
      type: String,
      enum: ['bank_transfer', 'credit_card', 'debit_order', 'cash', 'cheque', 'other']
    },
    transactionReference: String,
    bankReference: String
  },
  
  // Compliance and verification
  compliance: {
    isCompliant: { type: Boolean, default: false },
    verificationStatus: {
      type: String,
      enum: ['pending', 'verified', 'rejected', 'requires_review'],
      default: 'pending'
    },
    verifiedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    verificationDate: Date,
    complianceIssues: [String],
    complianceScore: { type: Number, min: 0, max: 100 }
  },
  
  // Related reports and documents
  relatedReports: [{
    reportId: { type: mongoose.Schema.Types.ObjectId, ref: 'ComplianceReport' },
    reportType: String,
    generatedDate: Date
  }],
  
  documents: [{
    type: {
      type: String,
      enum: ['invoice', 'receipt', 'statement', 'calculation_sheet', 'correspondence']
    },
    filename: String,
    filePath: String,
    uploadedAt: { type: Date, default: Date.now },
    uploadedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
  }],
  
  // Dispute handling
  disputes: [{
    disputeId: String,
    raisedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    raisedDate: { type: Date, default: Date.now },
    reason: String,
    description: String,
    status: {
      type: String,
      enum: ['open', 'investigating', 'resolved', 'rejected'],
      default: 'open'
    },
    resolution: String,
    resolvedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    resolvedDate: Date
  }],
  
  // Audit trail
  auditTrail: [{
    action: String,
    performedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    timestamp: { type: Date, default: Date.now },
    details: String,
    oldValues: mongoose.Schema.Types.Mixed,
    newValues: mongoose.Schema.Types.Mixed
  }],
  
  // Metadata
  notes: String,
  tags: [String],
  
  // System fields
  calculatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  approvedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Indexes
royaltyPaymentSchema.index({ 'venue.storeId': 1, 'period.startDate': -1 });
royaltyPaymentSchema.index({ organization: 1, paymentStatus: 1 });
royaltyPaymentSchema.index({ 'period.startDate': 1, 'period.endDate': 1 });
royaltyPaymentSchema.index({ paymentStatus: 1, 'paymentDetails.dueDate': 1 });

// Pre-save middleware
royaltyPaymentSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Calculate total amounts if not set
  if (!this.financialSummary.totalAmount) {
    const subtotal = (this.samroRoyalties?.totalSamroRoyalties || 0) + 
                    (this.sampraRoyalties?.totalSampraRoyalties || 0);
    const adminFee = this.financialSummary.administrationFee || 0;
    const vatAmount = (subtotal + adminFee) * (this.financialSummary.vatRate / 100);
    
    this.financialSummary.subtotal = subtotal;
    this.financialSummary.vatAmount = vatAmount;
    this.financialSummary.totalAmount = subtotal + adminFee + vatAmount;
  }
  
  next();
});

// Static method to find overdue payments
royaltyPaymentSchema.statics.findOverduePayments = function() {
  const now = new Date();
  return this.find({
    paymentStatus: { $in: ['invoiced', 'approved'] },
    'paymentDetails.dueDate': { $lt: now }
  });
};

// Static method to calculate royalties for a period
royaltyPaymentSchema.statics.calculateRoyaltiesForPeriod = async function(storeId, startDate, endDate, organization) {
  // This would integrate with the Track model and usage data
  // Implementation would depend on how usage data is stored
  
  const usageQuery = {
    storeId,
    playedDate: { $gte: startDate, $lte: endDate }
  };
  
  // Placeholder for actual calculation logic
  return {
    totalTracks: 0,
    totalPlays: 0,
    totalRoyalties: 0,
    breakdown: []
  };
};

// Instance method to generate invoice
royaltyPaymentSchema.methods.generateInvoice = function() {
  if (!this.paymentDetails.invoiceNumber) {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const sequence = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    
    this.paymentDetails.invoiceNumber = `${this.organization}-${year}${month}-${sequence}`;
    this.paymentDetails.invoiceDate = date;
    
    // Set due date (30 days from invoice date)
    const dueDate = new Date(date);
    dueDate.setDate(dueDate.getDate() + 30);
    this.paymentDetails.dueDate = dueDate;
    
    this.paymentStatus = 'invoiced';
  }
  
  return this.paymentDetails.invoiceNumber;
};

// Instance method to mark as paid
royaltyPaymentSchema.methods.markAsPaid = function(paymentDetails) {
  this.paymentStatus = 'paid';
  this.paymentDetails.paidDate = new Date();
  
  if (paymentDetails) {
    Object.assign(this.paymentDetails, paymentDetails);
  }
  
  // Add audit trail entry
  this.auditTrail.push({
    action: 'Payment Received',
    timestamp: new Date(),
    details: `Payment marked as paid. Amount: ${this.financialSummary.currency} ${this.financialSummary.totalAmount}`
  });
};

module.exports = mongoose.model('RoyaltyPayment', royaltyPaymentSchema);
