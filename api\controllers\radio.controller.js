const RadioStation = require('../models/RadioStation.model');
const Store = require('../models/Store.model');

// Get all radio stations
exports.getAllStations = async (req, res) => {
  try {
    const stations = await RadioStation.find({ isActive: true })
      .populate('createdBy', 'username role')
      .sort({ createdAt: -1 });
    
    res.json(stations);
  } catch (error) {
    console.error('Error fetching radio stations:', error);
    res.status(500).json({ error: 'Failed to fetch radio stations' });
  }
};

// Create a new radio station
exports.createStation = async (req, res) => {
  try {
    const { name, url, genre, country, description } = req.body;
    const userId = req.user.id;

    // Validate required fields
    if (!name || !url || !genre || !country) {
      return res.status(400).json({ 
        error: 'Name, URL, genre, and country are required' 
      });
    }

    // Check if station with same URL already exists
    const existingStation = await RadioStation.findOne({ url });
    if (existingStation) {
      return res.status(400).json({ 
        error: 'A station with this URL already exists' 
      });
    }

    const station = await RadioStation.create({
      name,
      url,
      genre,
      country,
      description,
      createdBy: userId
    });

    const populatedStation = await RadioStation.findById(station._id)
      .populate('createdBy', 'username role');

    res.status(201).json(populatedStation);
  } catch (error) {
    console.error('Error creating radio station:', error);
    res.status(500).json({ error: 'Failed to create radio station' });
  }
};

// Update a radio station
exports.updateStation = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, url, genre, country, description } = req.body;
    const userId = req.user.id;

    // Find the station
    const station = await RadioStation.findById(id);
    if (!station) {
      return res.status(404).json({ error: 'Radio station not found' });
    }

    // Check if user has permission to update (admin or creator)
    if (req.user.role !== 'admin' && station.createdBy.toString() !== userId) {
      return res.status(403).json({ error: 'Not authorized to update this station' });
    }

    // Check if URL is being changed and if it conflicts with another station
    if (url && url !== station.url) {
      const existingStation = await RadioStation.findOne({ url, _id: { $ne: id } });
      if (existingStation) {
        return res.status(400).json({ 
          error: 'A station with this URL already exists' 
        });
      }
    }

    const updatedStation = await RadioStation.findByIdAndUpdate(
      id,
      { name, url, genre, country, description },
      { new: true }
    ).populate('createdBy', 'username role');

    res.json(updatedStation);
  } catch (error) {
    console.error('Error updating radio station:', error);
    res.status(500).json({ error: 'Failed to update radio station' });
  }
};

// Delete a radio station
exports.deleteStation = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const station = await RadioStation.findById(id);
    if (!station) {
      return res.status(404).json({ error: 'Radio station not found' });
    }

    // Check if user has permission to delete (admin or creator)
    if (req.user.role !== 'admin' && station.createdBy.toString() !== userId) {
      return res.status(403).json({ error: 'Not authorized to delete this station' });
    }

    // Soft delete by setting isActive to false
    await RadioStation.findByIdAndUpdate(id, { isActive: false });

    res.json({ message: 'Radio station deleted successfully' });
  } catch (error) {
    console.error('Error deleting radio station:', error);
    res.status(500).json({ error: 'Failed to delete radio station' });
  }
};

// Get stations enabled for a specific store
exports.getStoreStations = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store exists
    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ error: 'Store not found' });
    }

    // Get stations enabled for this store
    const stations = await RadioStation.find({ 
      isActive: true,
      enabledStores: storeId 
    }).populate('createdBy', 'username role');

    res.json(stations);
  } catch (error) {
    console.error('Error fetching store stations:', error);
    res.status(500).json({ error: 'Failed to fetch store stations' });
  }
};

// Set stations for a specific store
exports.setStoreStations = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { stationIds } = req.body;

    // Verify store exists
    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ error: 'Store not found' });
    }

    // Check if user has permission (admin or store user)
    if (req.user.role !== 'admin' && req.user.storeId?.toString() !== storeId) {
      return res.status(403).json({ error: 'Not authorized to modify this store' });
    }

    // Validate that all station IDs exist
    const validStations = await RadioStation.find({ 
      _id: { $in: stationIds }, 
      isActive: true 
    });
    
    if (validStations.length !== stationIds.length) {
      return res.status(400).json({ error: 'Some station IDs are invalid' });
    }

    // Remove this store from all stations first
    await RadioStation.updateMany(
      { enabledStores: storeId },
      { $pull: { enabledStores: storeId } }
    );

    // Add this store to the selected stations
    if (stationIds.length > 0) {
      await RadioStation.updateMany(
        { _id: { $in: stationIds } },
        { $addToSet: { enabledStores: storeId } }
      );
    }

    res.json({ message: 'Store stations updated successfully' });
  } catch (error) {
    console.error('Error setting store stations:', error);
    res.status(500).json({ error: 'Failed to update store stations' });
  }
};
