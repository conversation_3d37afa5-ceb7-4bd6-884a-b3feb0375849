const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  subject: {
    type: String,
    required: true,
    trim: true
  },
  content: {
    type: String,
    required: true
  },
  sender: {
    id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    name: {
      type: String,
      required: true
    },
    role: {
      type: String,
      enum: ['admin', 'store', 'samro_staff', 'sampra_staff', 'compliance_admin'],
      required: true
    }
  },
  recipient: {
    id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    name: {
      type: String,
      required: true
    },
    role: {
      type: String,
      enum: ['admin', 'store', 'samro_staff', 'sampra_staff', 'compliance_admin'],
      required: true
    }
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  status: {
    type: String,
    enum: ['sent', 'delivered', 'read', 'failed'],
    default: 'sent'
  },
  readAt: {
    type: Date
  },
  isDeleted: {
    type: Boolean,
    default: false
  },
  deletedBy: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    deletedAt: {
      type: Date,
      default: Date.now
    }
  }],
  attachments: [{
    filename: String,
    originalName: String,
    mimetype: String,
    size: Number,
    path: String
  }]
}, {
  timestamps: true
});

// Index for efficient querying
messageSchema.index({ 'sender.id': 1, createdAt: -1 });
messageSchema.index({ 'recipient.id': 1, createdAt: -1 });
messageSchema.index({ status: 1 });
messageSchema.index({ priority: 1 });

// Virtual for message type (inbox/sent)
messageSchema.virtual('type').get(function() {
  return this.sender.id.toString() === this.recipient.id.toString() ? 'sent' : 'inbox';
});

module.exports = mongoose.model('Message', messageSchema);
