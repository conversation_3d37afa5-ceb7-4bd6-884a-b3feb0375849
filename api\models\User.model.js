const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true },
  email: { type: String, required: true, unique: true, lowercase: true, trim: true },
  password: { type: String, required: true },
  role: {
    type: String,
    enum: ['admin', 'store', 'samro_staff', 'sampra_staff', 'compliance_admin'],
    default: 'store'
  },
  storeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Store' },
  organization: {
    type: String,
    enum: ['SAMRO', 'SAMPRA', 'RISA', 'Internal'],
    default: 'Internal'
  },
  permissions: [{
    type: String,
    enum: [
      'view_all_reports',
      'export_compliance_data',
      'audit_trail_access',
      'real_time_monitoring',
      'data_verification',
      'cross_organization_access',
      'create_licenses',
      'edit_licenses',
      'delete_licenses'
    ]
  }],
  // Email verification fields
  emailVerified: { type: Boolean, default: false },
  emailVerificationToken: { type: String },
  emailVerificationExpires: { type: Date },
  // Account status
  accountStatus: {
    type: String,
    enum: ['pending_verification', 'active', 'suspended', 'inactive'],
    default: 'pending_verification'
  },
  lastLogin: { type: Date },
  lastActivity: { type: Date, default: Date.now },
  lastIpAddress: { type: String },
  isActive: { type: Boolean, default: true },
  // User settings
  settings: {
    emailNotifications: { type: Boolean, default: true },
    pushNotifications: { type: Boolean, default: true },
    theme: { type: String, enum: ['light', 'dark'], default: 'light' },
    language: { type: String, default: 'en' },
    timezone: { type: String, default: 'Africa/Johannesburg' }
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Update the updatedAt field before saving
userSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('User', userSchema);