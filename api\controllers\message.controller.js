const Message = require('../models/Message.model');
const User = require('../models/User.model');
const Store = require('../models/Store.model');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/attachments';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req, file, cb) => {
  // Allow common file types for attachments
  const allowedTypes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf', 'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain', 'text/csv',
    'application/zip', 'application/x-zip-compressed'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('File type not allowed'), false);
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 5 // Maximum 5 files per message
  },
  fileFilter: fileFilter
});

// Send a new message
exports.sendMessage = async (req, res) => {
  try {
    const { subject, content, recipientId, recipientName, recipientRole, priority } = req.body;
    const senderId = req.user.id;

    // Validate required fields
    if (!subject || !content) {
      return res.status(400).json({ error: 'Subject and content are required' });
    }

    if (!recipientId && !recipientName) {
      return res.status(400).json({ error: 'Recipient ID or name is required' });
    }

    // Get sender details
    const sender = await User.findById(senderId);
    if (!sender) {
      return res.status(404).json({ error: 'Sender not found' });
    }

    let recipient = null;
    let finalRecipientId = recipientId;
    let finalRecipientName = recipientName;
    let finalRecipientRole = recipientRole;

    // If recipientId is provided, validate and get recipient details
    if (recipientId && recipientId !== 'null' && recipientId !== null) {
      recipient = await User.findById(recipientId);
      if (!recipient) {
        return res.status(404).json({ error: 'Recipient not found' });
      }
      finalRecipientName = recipient.username;
      finalRecipientRole = recipient.role;
    } else if (recipientName && recipientRole) {
      // Try to find recipient by name and role for backward compatibility
      recipient = await User.findOne({
        username: recipientName,
        role: recipientRole,
        isActive: true
      });
      if (recipient) {
        finalRecipientId = recipient._id;
      }
    }

    // Validate recipient role permissions
    const canSendTo = validateMessagePermissions(sender.role, finalRecipientRole);
    if (!canSendTo) {
      return res.status(403).json({ error: 'You do not have permission to send messages to this recipient type' });
    }

    // Process attachments if any
    const attachments = [];
    if (req.files && req.files.length > 0) {
      req.files.forEach(file => {
        attachments.push({
          filename: file.filename,
          originalName: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          path: file.path
        });
      });
    }

    // Create message
    const message = new Message({
      subject,
      content,
      sender: {
        id: senderId,
        name: sender.username,
        role: sender.role
      },
      recipient: {
        id: finalRecipientId,
        name: finalRecipientName,
        role: finalRecipientRole
      },
      priority: priority || 'medium',
      status: 'sent',
      attachments
    });

    await message.save();

    res.status(201).json({
      message: 'Message sent successfully',
      data: message
    });
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({ error: 'Failed to send message' });
  }
};

// Get messages for a user (inbox and sent)
exports.getMessages = async (req, res) => {
  try {
    const userId = req.user.id;
    const { type = 'all', page = 1, limit = 20 } = req.query;

    let query = {
      isDeleted: false,
      'deletedBy.userId': { $ne: userId }
    };

    // Filter by message type
    if (type === 'inbox') {
      query['recipient.id'] = userId;
    } else if (type === 'sent') {
      query['sender.id'] = userId;
    } else {
      // All messages (inbox and sent)
      query.$or = [
        { 'recipient.id': userId },
        { 'sender.id': userId }
      ];
    }

    const messages = await Message.find(query)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .lean();

    // Add message type for each message
    const messagesWithType = messages.map(message => ({
      ...message,
      type: message.sender.id.toString() === userId ? 'sent' : 'inbox'
    }));

    const total = await Message.countDocuments(query);

    res.json({
      data: messagesWithType,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({ error: 'Failed to fetch messages' });
  }
};

// Get a specific message
exports.getMessage = async (req, res) => {
  try {
    const messageId = req.params.id;
    const userId = req.user.id;

    const message = await Message.findOne({
      _id: messageId,
      $or: [
        { 'sender.id': userId },
        { 'recipient.id': userId }
      ],
      isDeleted: false,
      'deletedBy.userId': { $ne: userId }
    });

    if (!message) {
      return res.status(404).json({ error: 'Message not found' });
    }

    res.json({ data: message });
  } catch (error) {
    console.error('Error fetching message:', error);
    res.status(500).json({ error: 'Failed to fetch message' });
  }
};

// Mark message as read
exports.markAsRead = async (req, res) => {
  try {
    const messageId = req.params.id;
    const userId = req.user.id;

    const message = await Message.findOneAndUpdate(
      {
        _id: messageId,
        'recipient.id': userId,
        status: { $ne: 'read' }
      },
      {
        status: 'read',
        readAt: new Date()
      },
      { new: true }
    );

    if (!message) {
      return res.status(404).json({ error: 'Message not found or already read' });
    }

    res.json({
      message: 'Message marked as read',
      data: message
    });
  } catch (error) {
    console.error('Error marking message as read:', error);
    res.status(500).json({ error: 'Failed to mark message as read' });
  }
};

// Delete message (soft delete)
exports.deleteMessage = async (req, res) => {
  try {
    const messageId = req.params.id;
    const userId = req.user.id;

    const message = await Message.findOneAndUpdate(
      {
        _id: messageId,
        $or: [
          { 'sender.id': userId },
          { 'recipient.id': userId }
        ]
      },
      {
        $addToSet: {
          deletedBy: {
            userId: userId,
            deletedAt: new Date()
          }
        }
      },
      { new: true }
    );

    if (!message) {
      return res.status(404).json({ error: 'Message not found' });
    }

    res.json({ message: 'Message deleted successfully' });
  } catch (error) {
    console.error('Error deleting message:', error);
    res.status(500).json({ error: 'Failed to delete message' });
  }
};

// Get conversations (grouped messages)
exports.getConversations = async (req, res) => {
  try {
    const userId = req.user.id;

    // This is a simplified version - in production you'd want more sophisticated conversation grouping
    const conversations = await Message.aggregate([
      {
        $match: {
          $or: [
            { 'sender.id': userId },
            { 'recipient.id': userId }
          ],
          isDeleted: false,
          'deletedBy.userId': { $ne: userId }
        }
      },
      {
        $sort: { createdAt: -1 }
      },
      {
        $group: {
          _id: {
            $cond: [
              { $eq: ['$sender.id', userId] },
              '$recipient.id',
              '$sender.id'
            ]
          },
          lastMessage: { $first: '$$ROOT' },
          unreadCount: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$recipient.id', userId] },
                    { $ne: ['$status', 'read'] }
                  ]
                },
                1,
                0
              ]
            }
          }
        }
      }
    ]);

    res.json({ data: conversations });
  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({ error: 'Failed to fetch conversations' });
  }
};

// Download attachment
exports.downloadAttachment = async (req, res) => {
  try {
    const { messageId, filename } = req.params;
    const userId = req.user.id;

    // Find the message and verify user has access
    const message = await Message.findOne({
      _id: messageId,
      $or: [
        { 'sender.id': userId },
        { 'recipient.id': userId }
      ],
      isDeleted: false,
      'deletedBy.userId': { $ne: userId }
    });

    if (!message) {
      return res.status(404).json({ error: 'Message not found' });
    }

    // Find the attachment
    const attachment = message.attachments.find(att => att.filename === filename);
    if (!attachment) {
      return res.status(404).json({ error: 'Attachment not found' });
    }

    // Check if file exists
    if (!fs.existsSync(attachment.path)) {
      return res.status(404).json({ error: 'File not found on server' });
    }

    // Set appropriate headers
    res.setHeader('Content-Disposition', `attachment; filename="${attachment.originalName}"`);
    res.setHeader('Content-Type', attachment.mimetype);

    // Stream the file
    const fileStream = fs.createReadStream(attachment.path);
    fileStream.pipe(res);
  } catch (error) {
    console.error('Error downloading attachment:', error);
    res.status(500).json({ error: 'Failed to download attachment' });
  }
};

// Helper function to validate message permissions
function validateMessagePermissions(senderRole, recipientRole) {
  const permissions = {
    'admin': ['admin', 'store', 'samro_staff', 'sampra_staff', 'compliance_admin'],
    'store': ['admin', 'samro_staff', 'sampra_staff', 'compliance_admin'],
    'samro_staff': ['admin', 'store', 'compliance_admin'],
    'sampra_staff': ['admin', 'store', 'compliance_admin'],
    'compliance_admin': ['admin', 'store', 'samro_staff', 'sampra_staff']
  };

  return permissions[senderRole]?.includes(recipientRole) || false;
}

// Get users for messaging (recipient selection)
exports.getUsersForMessaging = async (req, res) => {
  try {
    const { role } = req.query;
    const currentUserRole = req.user.role;

    // Define which roles can message which other roles
    const messagingPermissions = {
      'admin': ['admin', 'store', 'samro_staff', 'sampra_staff', 'compliance_admin'],
      'store': ['admin', 'samro_staff', 'sampra_staff', 'compliance_admin'],
      'samro_staff': ['admin', 'store', 'compliance_admin'],
      'sampra_staff': ['admin', 'store', 'compliance_admin'],
      'compliance_admin': ['admin', 'store', 'samro_staff', 'sampra_staff']
    };

    // Check if current user can message the requested role
    const allowedRoles = messagingPermissions[currentUserRole] || [];

    let query = { isActive: true };

    if (role && role !== 'all') {
      if (!allowedRoles.includes(role)) {
        return res.status(403).json({ error: 'You do not have permission to message users with this role' });
      }
      query.role = role;
    } else {
      // If no specific role requested, return all users the current user can message
      query.role = { $in: allowedRoles };
    }

    // Exclude the current user from the list
    query._id = { $ne: req.user.id };

    const users = await User.find(query)
      .populate('storeId', 'name location')
      .select('_id username role organization storeId')
      .sort({ username: 1 });

    // Format users for messaging dropdown
    const formattedUsers = users.map(user => ({
      id: user._id,
      name: user.username,
      role: user.role,
      organization: user.organization,
      storeName: user.storeId?.name,
      displayName: getDisplayName(user)
    }));

    res.json({
      data: formattedUsers,
      total: formattedUsers.length
    });
  } catch (error) {
    console.error('Error fetching users for messaging:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
};

// Helper function to create display names for users
function getDisplayName(user) {
  switch (user.role) {
    case 'admin':
      return 'Admin';
    case 'store':
      return user.storeId?.name || `Store User (${user.username})`;
    case 'samro_staff':
      return `SAMRO Staff (${user.username})`;
    case 'sampra_staff':
      return `SAMPRA Staff (${user.username})`;
    case 'compliance_admin':
      return `Compliance Admin (${user.username})`;
    default:
      return user.username;
  }
}

// Export multer middleware for file uploads
exports.uploadMiddleware = upload.array('attachments', 5);
