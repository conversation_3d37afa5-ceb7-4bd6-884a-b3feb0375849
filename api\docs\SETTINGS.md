# System Settings Implementation

## Overview

The TrakSong system now includes a comprehensive settings management system that connects the admin dashboard settings page to a MongoDB database with proper data persistence, validation, and audit logging.

## Features

### ✅ Database Integration
- **Settings Model**: Complete MongoDB schema with validation
- **Data Persistence**: All settings changes are saved to database
- **Default Values**: Automatic initialization with sensible defaults
- **Version Control**: Settings versioning for change tracking

### ✅ API Endpoints
- `GET /admin/settings` - Retrieve current system settings
- `PUT /admin/settings` - Update system settings
- `POST /admin/settings/validate` - Validate settings without saving

### ✅ Frontend Integration
- **Real-time Updates**: Settings load from database on page load
- **Error Handling**: Comprehensive error messages and validation
- **Status Indicators**: Visual feedback for save/load operations
- **Unsaved Changes**: Warning alerts for unsaved modifications

### ✅ Audit & Security
- **Change Logging**: All settings modifications are logged
- **User Tracking**: Records who made changes and when
- **Validation**: Server-side validation for all settings
- **Authentication**: Admin-only access with proper authorization

## Settings Categories

### General Settings
- System Name
- Timezone
- Language (English, Afrikaans, Zulu, Xhosa)
- Date Format
- Time Format

### Audio Settings
- Default Bitrate (128-320 kbps)
- Audio Format (MP3, WAV, FLAC, AAC)
- Volume Limit (50-100%)
- Crossfade Settings

### Security Settings
- Session Timeout (5-480 minutes)
- Password Requirements
- Two-Factor Authentication
- Remote Access Control
- Data Encryption

### Notification Settings
- Email Notifications
- SMS Notifications
- Push Notifications
- System Alerts
- Maintenance Alerts

### Storage Settings
- Maximum File Size (1-500 MB)
- Auto Cleanup
- Cleanup Retention Days
- File Compression

### Backup Settings
- Auto Backup
- Backup Frequency (Hourly, Daily, Weekly, Monthly)
- Backup Retention (1-365 days)
- Cloud Backup

## Database Schema

```javascript
{
  systemName: String (required),
  timezone: String (required),
  language: String (enum: ['en', 'af', 'zu', 'xh']),
  dateFormat: String (enum: ['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD']),
  timeFormat: String (enum: ['12h', '24h']),
  // ... additional fields with validation
  lastModifiedBy: ObjectId (ref: 'User'),
  version: Number (auto-incremented),
  createdAt: Date,
  updatedAt: Date
}
```

## Installation & Setup

### 1. Initialize Settings
```bash
cd api
npm run init:settings
```

### 2. Seed Database (includes settings)
```bash
cd api
npm run seed
```

### 3. Verify Installation
- Start the API server
- Login as admin user
- Navigate to Admin Dashboard > Settings
- Verify settings load and save properly

## API Usage Examples

### Get Settings
```javascript
GET /admin/settings
Authorization: Bearer <admin_token>

Response:
{
  "systemName": "TrakSong Music System",
  "timezone": "Africa/Johannesburg",
  // ... other settings
  "version": 1,
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

### Update Settings
```javascript
PUT /admin/settings
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "systemName": "My Music Store",
  "volumeLimit": 90,
  "emailNotifications": false
}

Response:
{
  "message": "Settings updated successfully",
  "settings": { /* updated settings */ },
  "version": 2,
  "lastModified": "2024-01-15T10:35:00Z"
}
```

## Error Handling

### Validation Errors
- Required field validation
- Enum value validation
- Range validation (min/max values)
- Format validation (e.g., timezone strings)

### Database Errors
- Connection failures
- Constraint violations
- Transaction rollbacks

### Frontend Error Display
- Field-level validation messages
- Global error alerts
- Network error handling
- Retry mechanisms

## Security Considerations

### Access Control
- Admin role required for all settings operations
- JWT token authentication
- Request rate limiting

### Data Validation
- Server-side validation for all inputs
- Sanitization of user inputs
- Type checking and range validation

### Audit Trail
- All changes logged with user ID and timestamp
- IP address and user agent tracking
- Version history maintenance

## Troubleshooting

### Common Issues

1. **Settings not loading**
   - Check database connection
   - Verify admin authentication
   - Run `npm run init:settings`

2. **Save failures**
   - Check validation errors in browser console
   - Verify required fields are provided
   - Check server logs for database errors

3. **Permission denied**
   - Ensure user has admin role
   - Check JWT token validity
   - Verify API endpoint authentication

### Debug Commands
```bash
# Check settings in database
mongo
use music-store
db.settings.find().pretty()

# View server logs
tail -f logs/server.log

# Test API endpoints
curl -H "Authorization: Bearer <token>" http://localhost:5000/admin/settings
```

## Future Enhancements

- [ ] Settings import/export functionality
- [ ] Settings backup and restore
- [ ] Multi-environment settings management
- [ ] Settings change approval workflow
- [ ] Real-time settings synchronization across instances
