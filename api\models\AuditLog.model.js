const mongoose = require('mongoose');

const auditLogSchema = new mongoose.Schema({
  // Basic audit information
  action: {
    type: String,
    required: true,
    enum: [
      // User actions
      'user_login',
      'user_logout',
      'user_created',
      'user_updated',
      'user_deleted',
      'password_changed',
      
      // Compliance actions
      'report_generated',
      'report_created',
      'report_verified',
      'report_edited',
      'report_deleted',
      'report_exported',
      
      // Data actions
      'data_export',
      'data_import',
      'bulk_operation',
      
      // System actions
      'settings_updated',
      'system_backup',
      'system_restore',
      
      // License actions
      'license_created',
      'license_updated',
      'license_deleted',
      
      // Alert actions
      'alert_created',
      'alert_acknowledged',
      'alert_resolved',
      
      // Track actions
      'track_uploaded',
      'track_updated',
      'track_deleted',
      
      // Playlist actions
      'playlist_created',
      'playlist_updated',
      'playlist_deleted',
      
      // Store actions
      'store_created',
      'store_updated',
      'store_deleted'
    ]
  },
  
  // User who performed the action
  performedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Timestamp of the action
  timestamp: {
    type: Date,
    default: Date.now,
    required: true
  },
  
  // Detailed description of what happened
  details: {
    type: String,
    required: true
  },
  
  // Resource information
  resourceType: {
    type: String,
    enum: [
      'User',
      'ComplianceReport',
      'Track',
      'Playlist',
      'Store',
      'License',
      'Alert',
      'Settings',
      'System'
    ]
  },
  
  resourceId: {
    type: String // Can be ObjectId string or custom ID
  },
  
  // Request information
  ipAddress: {
    type: String,
    required: true
  },
  
  userAgent: {
    type: String
  },
  
  // Organization context
  organization: {
    type: String,
    enum: ['SAMRO', 'SAMPRA', 'RISA', 'ADMIN', 'Internal'],
    required: true
  },
  
  // Additional metadata
  metadata: {
    // Before and after values for updates
    oldValues: mongoose.Schema.Types.Mixed,
    newValues: mongoose.Schema.Types.Mixed,
    
    // Additional context
    sessionId: String,
    requestId: String,
    
    // Risk level
    riskLevel: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'low'
    },
    
    // Tags for categorization
    tags: [String],
    
    // Additional notes
    notes: String
  },
  
  // Compliance tracking
  compliance: {
    requiresReview: {
      type: Boolean,
      default: false
    },
    reviewedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reviewedAt: Date,
    reviewNotes: String,
    complianceStatus: {
      type: String,
      enum: ['compliant', 'non_compliant', 'pending_review'],
      default: 'compliant'
    }
  }
}, {
  timestamps: true,
  indexes: [
    { performedBy: 1, timestamp: -1 },
    { action: 1, timestamp: -1 },
    { organization: 1, timestamp: -1 },
    { resourceType: 1, resourceId: 1 },
    { timestamp: -1 },
    { 'metadata.riskLevel': 1, timestamp: -1 },
    { 'compliance.requiresReview': 1, 'compliance.complianceStatus': 1 }
  ]
});

// Static methods for creating audit logs
auditLogSchema.statics.logAction = async function(actionData) {
  try {
    const auditLog = new this(actionData);
    await auditLog.save();
    return auditLog;
  } catch (error) {
    console.error('Failed to create audit log:', error);
    throw error;
  }
};

// Static method for querying audit logs with filters
auditLogSchema.statics.getFilteredLogs = async function(filters = {}) {
  const {
    page = 1,
    limit = 10,
    search,
    action,
    user,
    organization,
    resourceType,
    riskLevel,
    dateFrom,
    dateTo
  } = filters;

  // Build match conditions
  const matchConditions = {};

  if (search) {
    matchConditions.$or = [
      { action: { $regex: search, $options: 'i' } },
      { details: { $regex: search, $options: 'i' } },
      { resourceId: { $regex: search, $options: 'i' } }
    ];
  }

  if (action) matchConditions.action = action;
  if (organization) matchConditions.organization = organization;
  if (resourceType) matchConditions.resourceType = resourceType;
  if (riskLevel) matchConditions['metadata.riskLevel'] = riskLevel;

  if (dateFrom || dateTo) {
    matchConditions.timestamp = {};
    if (dateFrom) matchConditions.timestamp.$gte = new Date(dateFrom);
    if (dateTo) matchConditions.timestamp.$lte = new Date(dateTo);
  }

  // If user filter is provided, we need to lookup by username
  const pipeline = [];

  // Add user lookup
  pipeline.push({
    $lookup: {
      from: 'users',
      localField: 'performedBy',
      foreignField: '_id',
      as: 'userInfo'
    }
  });

  pipeline.push({
    $unwind: { path: '$userInfo', preserveNullAndEmptyArrays: true }
  });

  // Add user filter if specified
  if (user) {
    matchConditions['userInfo.username'] = { $regex: user, $options: 'i' };
  }

  // Add match stage
  if (Object.keys(matchConditions).length > 0) {
    pipeline.push({ $match: matchConditions });
  }

  // Add sorting
  pipeline.push({ $sort: { timestamp: -1 } });

  // Get total count
  const countPipeline = [...pipeline, { $count: 'total' }];
  const countResult = await this.aggregate(countPipeline);
  const total = countResult.length > 0 ? countResult[0].total : 0;

  // Add pagination
  pipeline.push(
    { $skip: (parseInt(page) - 1) * parseInt(limit) },
    { $limit: parseInt(limit) }
  );

  // Project final shape
  pipeline.push({
    $project: {
      _id: 1,
      action: 1,
      performedBy: {
        _id: '$userInfo._id',
        username: '$userInfo.username',
        organization: '$userInfo.organization'
      },
      timestamp: 1,
      details: 1,
      resourceType: 1,
      resourceId: 1,
      ipAddress: 1,
      userAgent: 1,
      organization: 1,
      metadata: 1,
      compliance: 1
    }
  });

  const logs = await this.aggregate(pipeline);

  return {
    logs,
    total,
    totalPages: Math.ceil(total / parseInt(limit)),
    page: parseInt(page),
    limit: parseInt(limit)
  };
};

// Instance method to mark as requiring review
auditLogSchema.methods.markForReview = function(reviewerId, notes) {
  this.compliance.requiresReview = true;
  this.compliance.reviewedBy = reviewerId;
  this.compliance.reviewedAt = new Date();
  this.compliance.reviewNotes = notes;
  this.compliance.complianceStatus = 'pending_review';
  return this.save();
};

module.exports = mongoose.model('AuditLog', auditLogSchema);
