const Track = require('../models/Track.model');
const fs = require('fs');
const path = require('path');

exports.uploadTrack = async (req, res) => {
  const {
    title,
    artist,
    album,
    genre,
    duration,
    isrcCode,
    iswcCode,
    composers,
    publishers,
    recordLabel,
    metadata,
    rights,
    compliance,
    // SAMRO-specific fields
    samroWorkNumber,
    performanceRightsSplits,
    // SAMPRA-specific fields
    sampraArtistNumbers,
    recordingRightsSplits,
    producerInfo,
    masterRecordingInfo
  } = req.body;
  const userId = req.user.id;
  const filePath = `/uploads/${req.file.filename}`;

  try {
    // Parse JSON fields if they're strings
    const parsedComposers = composers ? (typeof composers === 'string' ? JSON.parse(composers) : composers) : [];
    const parsedPublishers = publishers ? (typeof publishers === 'string' ? JSON.parse(publishers) : publishers) : [];
    const parsedRecordLabel = recordLabel ? (typeof recordLabel === 'string' ? JSON.parse(recordLabel) : recordLabel) : {};
    const parsedMetadata = metadata ? (typeof metadata === 'string' ? JSON.parse(metadata) : metadata) : {};
    const parsedRights = rights ? (typeof rights === 'string' ? JSON.parse(rights) : rights) : {};
    const parsedCompliance = compliance ? (typeof compliance === 'string' ? JSON.parse(compliance) : compliance) : {};

    // Parse SAMRO and SAMPRA specific fields
    const parsedPerformanceRightsSplits = performanceRightsSplits ? (typeof performanceRightsSplits === 'string' ? JSON.parse(performanceRightsSplits) : performanceRightsSplits) : [];
    const parsedSampraArtistNumbers = sampraArtistNumbers ? (typeof sampraArtistNumbers === 'string' ? JSON.parse(sampraArtistNumbers) : sampraArtistNumbers) : [];
    const parsedRecordingRightsSplits = recordingRightsSplits ? (typeof recordingRightsSplits === 'string' ? JSON.parse(recordingRightsSplits) : recordingRightsSplits) : [];
    const parsedProducerInfo = producerInfo ? (typeof producerInfo === 'string' ? JSON.parse(producerInfo) : producerInfo) : [];
    const parsedMasterRecordingInfo = masterRecordingInfo ? (typeof masterRecordingInfo === 'string' ? JSON.parse(masterRecordingInfo) : masterRecordingInfo) : {};

    const track = await Track.create({
      trackId: `T-${Date.now()}`,
      title,
      artist,
      album,
      genre,
      duration: parseInt(duration) || 0,
      filePath,
      isrcCode: isrcCode || undefined,
      iswcCode: iswcCode || undefined,
      composers: parsedComposers,
      publishers: parsedPublishers,
      recordLabel: parsedRecordLabel,
      metadata: parsedMetadata,
      rights: parsedRights,
      compliance: parsedCompliance,
      // SAMRO-specific fields
      samroWorkNumber: samroWorkNumber || undefined,
      performanceRightsSplits: parsedPerformanceRightsSplits,
      // SAMPRA-specific fields
      sampraArtistNumbers: parsedSampraArtistNumbers,
      recordingRightsSplits: parsedRecordingRightsSplits,
      producerInfo: parsedProducerInfo,
      masterRecordingInfo: parsedMasterRecordingInfo,
      addedBy: userId
    });

    res.status(201).json(track);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to upload track', details: err.message });
  }
};

// Validation helper functions
const validateIPINumber = (ipiNumber) => {
  // IPI numbers should follow format: I-NNNNNNNNN-N (I- followed by 9 digits, hyphen, and 1 check digit)
  // Also accept legacy format of 11 digits for backward compatibility
  const standardFormat = /^I-\d{9}-\d$/;
  const legacyFormat = /^\d{11}$/;
  return standardFormat.test(ipiNumber) || legacyFormat.test(ipiNumber);
};

const validatePercentageSplits = (splits) => {
  const total = splits.reduce((sum, split) => sum + (split.percentage || 0), 0);
  return total === 100;
};

const validateSAMROCompliance = (track) => {
  const errors = [];

  if (track.compliance?.samroRegistered) {
    if (!track.samroWorkNumber) {
      errors.push('SAMRO Work Number is required for SAMRO registered tracks');
    }

    if (track.composers) {
      track.composers.forEach((composer, index) => {
        if (composer.ipiNumber && !validateIPINumber(composer.ipiNumber)) {
          errors.push(`Invalid IPI number for composer ${index + 1}: ${composer.ipiNumber}`);
        }
      });
    }

    if (track.publishers) {
      track.publishers.forEach((publisher, index) => {
        if (publisher.ipiNumber && !validateIPINumber(publisher.ipiNumber)) {
          errors.push(`Invalid IPI number for publisher ${index + 1}: ${publisher.ipiNumber}`);
        }
      });
    }

    if (track.performanceRightsSplits) {
      track.performanceRightsSplits.forEach((split, index) => {
        if (split.ipiNumber && !validateIPINumber(split.ipiNumber)) {
          errors.push(`Invalid IPI number for performance rights split ${index + 1}: ${split.ipiNumber}`);
        }
      });
    }

    if (track.performanceRightsSplits && track.performanceRightsSplits.length > 0) {
      if (!validatePercentageSplits(track.performanceRightsSplits)) {
        errors.push('Performance rights splits must total 100%');
      }
    }
  }

  return errors;
};

const validateSAMPRACompliance = (track) => {
  const errors = [];

  if (track.compliance?.sampraRegistered) {
    // Check if sampraArtistNumbers exists and has at least one non-empty value
    if (!track.sampraArtistNumbers ||
        track.sampraArtistNumbers.length === 0 ||
        track.sampraArtistNumbers.every(num => !num || num.trim() === '')) {
      errors.push('SAMPRA Artist Numbers are required for SAMPRA registered tracks');
    }

    if (track.recordingRightsSplits && track.recordingRightsSplits.length > 0) {
      if (!validatePercentageSplits(track.recordingRightsSplits)) {
        errors.push('Recording rights splits must total 100%');
      }
    }
  }

  return errors;
};

exports.getAllTracks = async (req, res) => {
  try {
    const tracks = await Track.find().populate('addedBy', 'username');

    // Validate track data integrity
    const validatedTracks = tracks.map(track => {
      const trackObj = track.toObject();
      let validationErrors = [];

      // Ensure required fields are present
      if (!trackObj.title) validationErrors.push('Missing title');
      if (!trackObj.artist) validationErrors.push('Missing artist');
      if (!trackObj.filePath) validationErrors.push('Missing file path');
      if (!trackObj.duration || trackObj.duration <= 0) validationErrors.push('Missing or invalid duration');

      // Validate SAMRO compliance
      const samroErrors = validateSAMROCompliance(trackObj);
      validationErrors = validationErrors.concat(samroErrors);

      // Validate SAMPRA compliance
      const sampraErrors = validateSAMPRACompliance(trackObj);
      validationErrors = validationErrors.concat(sampraErrors);

      // Calculate compliance score
      let complianceScore = 100;
      if (validationErrors.length > 0) {
        complianceScore = Math.max(0, 100 - (validationErrors.length * 10));
      }

      if (validationErrors.length > 0) {
        console.warn(`Track ${trackObj._id} validation issues:`, validationErrors);
      }

      return {
        ...trackObj,
        isValid: validationErrors.length === 0,
        validationErrors,
        complianceScore
      };
    });

    res.json({
      success: true,
      data: validatedTracks,
      metadata: {
        total: validatedTracks.length,
        valid: validatedTracks.filter(t => t.isValid).length,
        invalid: validatedTracks.filter(t => !t.isValid).length,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching tracks:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch tracks',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

// Data Quality Diagnostics endpoint
exports.getDataQualityReport = async (req, res) => {
  try {
    const tracks = await Track.find().populate('addedBy', 'username email');

    const report = {
      summary: {
        total: tracks.length,
        valid: 0,
        invalid: 0,
        warnings: 0
      },
      issues: {
        missingTitle: [],
        missingArtist: [],
        missingFilePath: [],
        invalidDuration: [],
        filePathIssues: []
      },
      complianceIssues: {
        samro: {
          missingWorkNumber: [],
          missingComposerIPI: [],
          missingPerformanceRights: []
        },
        sampra: {
          missingArtistNumbers: [],
          missingRecordingRights: [],
          missingRecordLabel: []
        }
      },
      recommendations: []
    };

    tracks.forEach(track => {
      const trackObj = track.toObject();
      let hasIssues = false;
      let hasWarnings = false;

      // Basic validation
      if (!trackObj.title) {
        report.issues.missingTitle.push({
          id: trackObj._id,
          trackId: trackObj.trackId,
          artist: trackObj.artist || 'Unknown'
        });
        hasIssues = true;
      }

      if (!trackObj.artist) {
        report.issues.missingArtist.push({
          id: trackObj._id,
          trackId: trackObj.trackId,
          title: trackObj.title || 'Unknown'
        });
        hasIssues = true;
      }

      if (!trackObj.filePath) {
        report.issues.missingFilePath.push({
          id: trackObj._id,
          trackId: trackObj.trackId,
          title: trackObj.title || 'Unknown',
          artist: trackObj.artist || 'Unknown'
        });
        hasIssues = true;
      }

      if (!trackObj.duration || trackObj.duration <= 0) {
        report.issues.invalidDuration.push({
          id: trackObj._id,
          trackId: trackObj.trackId,
          title: trackObj.title || 'Unknown',
          artist: trackObj.artist || 'Unknown',
          duration: trackObj.duration
        });
        hasIssues = true;
      }

      // File path validation
      if (trackObj.filePath && !trackObj.filePath.match(/\.(mp3|wav|flac|m4a|aac)$/i)) {
        report.issues.filePathIssues.push({
          id: trackObj._id,
          trackId: trackObj.trackId,
          title: trackObj.title || 'Unknown',
          artist: trackObj.artist || 'Unknown',
          filePath: trackObj.filePath,
          issue: 'Unsupported file extension'
        });
        hasWarnings = true;
      }

      // SAMRO compliance checks - only for SAMRO registered tracks
      if (trackObj.compliance?.samroRegistered) {
        if (!trackObj.samroWorkNumber) {
          report.complianceIssues.samro.missingWorkNumber.push({
            id: trackObj._id,
            trackId: trackObj.trackId,
            title: trackObj.title || 'Unknown',
            artist: trackObj.artist || 'Unknown'
          });
          hasWarnings = true;
        }

        if (!trackObj.composers?.some(c => c.ipiNumber)) {
          report.complianceIssues.samro.missingComposerIPI.push({
            id: trackObj._id,
            trackId: trackObj.trackId,
            title: trackObj.title || 'Unknown',
            artist: trackObj.artist || 'Unknown'
          });
          hasWarnings = true;
        }

        if (!trackObj.performanceRightsSplits?.length) {
          report.complianceIssues.samro.missingPerformanceRights.push({
            id: trackObj._id,
            trackId: trackObj.trackId,
            title: trackObj.title || 'Unknown',
            artist: trackObj.artist || 'Unknown'
          });
          hasWarnings = true;
        }
      }

      // SAMPRA compliance checks - only for SAMPRA registered tracks
      if (trackObj.compliance?.sampraRegistered) {
        if (!trackObj.sampraArtistNumbers?.length ||
            trackObj.sampraArtistNumbers.every(num => !num || num.trim() === '')) {
          report.complianceIssues.sampra.missingArtistNumbers.push({
            id: trackObj._id,
            trackId: trackObj.trackId,
            title: trackObj.title || 'Unknown',
            artist: trackObj.artist || 'Unknown'
          });
          hasWarnings = true;
        }

        if (!trackObj.recordingRightsSplits?.length) {
          report.complianceIssues.sampra.missingRecordingRights.push({
            id: trackObj._id,
            trackId: trackObj.trackId,
            title: trackObj.title || 'Unknown',
            artist: trackObj.artist || 'Unknown'
          });
          hasWarnings = true;
        }

        if (!trackObj.recordLabel?.name) {
          report.complianceIssues.sampra.missingRecordLabel.push({
            id: trackObj._id,
            trackId: trackObj.trackId,
            title: trackObj.title || 'Unknown',
            artist: trackObj.artist || 'Unknown'
          });
          hasWarnings = true;
        }
      }

      // Update summary
      if (hasIssues) {
        report.summary.invalid++;
      } else {
        report.summary.valid++;
      }

      if (hasWarnings) {
        report.summary.warnings++;
      }
    });

    // Generate recommendations
    if (report.issues.missingTitle.length > 0) {
      report.recommendations.push(`${report.issues.missingTitle.length} tracks are missing titles. These tracks cannot be properly identified or played.`);
    }
    if (report.issues.missingArtist.length > 0) {
      report.recommendations.push(`${report.issues.missingArtist.length} tracks are missing artist information. This affects search and organization.`);
    }
    if (report.issues.missingFilePath.length > 0) {
      report.recommendations.push(`${report.issues.missingFilePath.length} tracks are missing file paths. These tracks cannot be played.`);
    }
    if (report.issues.invalidDuration.length > 0) {
      report.recommendations.push(`${report.issues.invalidDuration.length} tracks have invalid duration. This affects playlist timing and reporting.`);
    }

    const totalComplianceIssues = Object.values(report.complianceIssues.samro).reduce((sum, arr) => sum + arr.length, 0) +
                                 Object.values(report.complianceIssues.sampra).reduce((sum, arr) => sum + arr.length, 0);

    if (totalComplianceIssues > 0) {
      report.recommendations.push(`${totalComplianceIssues} compliance issues detected. These may affect royalty reporting and legal compliance.`);
    }

    res.json({
      success: true,
      data: report,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error generating data quality report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate data quality report',
      error: error.message
    });
  }
};

// Bulk fix data quality issues
exports.fixDataQualityIssues = async (req, res) => {
  try {
    const { issueType, trackIds, fixData } = req.body;

    if (!issueType || !trackIds || !Array.isArray(trackIds)) {
      return res.status(400).json({
        success: false,
        message: 'Issue type and track IDs are required'
      });
    }

    const results = {
      success: 0,
      failed: 0,
      errors: []
    };

    for (const trackId of trackIds) {
      try {
        const updateData = {};

        switch (issueType) {
          case 'missingTitle':
            if (fixData.title) {
              updateData.title = fixData.title;
            }
            break;
          case 'missingArtist':
            if (fixData.artist) {
              updateData.artist = fixData.artist;
            }
            break;
          case 'invalidDuration':
            if (fixData.duration && fixData.duration > 0) {
              updateData.duration = fixData.duration;
            }
            break;
          case 'missingFilePath':
            if (fixData.filePath) {
              updateData.filePath = fixData.filePath;
            }
            break;
          default:
            throw new Error(`Unsupported issue type: ${issueType}`);
        }

        if (Object.keys(updateData).length > 0) {
          updateData.updatedAt = new Date();
          await Track.findByIdAndUpdate(trackId, updateData);
          results.success++;
        } else {
          results.failed++;
          results.errors.push(`No valid fix data provided for track ${trackId}`);
        }
      } catch (error) {
        results.failed++;
        results.errors.push(`Failed to fix track ${trackId}: ${error.message}`);
      }
    }

    res.json({
      success: true,
      message: `Fixed ${results.success} tracks, ${results.failed} failed`,
      data: results
    });

  } catch (error) {
    console.error('Error fixing data quality issues:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fix data quality issues',
      error: error.message
    });
  }
};