const mongoose = require('mongoose');

const complianceAlertSchema = new mongoose.Schema({
  alertId: { type: String, unique: true, required: true },
  
  // Alert classification
  type: {
    type: String,
    enum: [
      'missing_metadata',
      'license_expiry',
      'payment_overdue',
      'compliance_violation',
      'data_quality',
      'verification_required',
      'audit_due',
      'rate_change',
      'system_error',
      'manual_review'
    ],
    required: true
  },
  
  category: {
    type: String,
    enum: ['SAMRO', 'SAMPRA', 'RISA', 'General', 'System'],
    required: true
  },
  
  severity: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    required: true
  },
  
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  
  // Alert content
  title: { type: String, required: true },
  message: { type: String, required: true },
  description: String,
  
  // Affected entities
  affectedEntities: {
    tracks: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Track' }],
    stores: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Store' }],
    licenses: [{ type: mongoose.Schema.Types.ObjectId, ref: 'VenueLicense' }],
    payments: [{ type: mongoose.Schema.Types.ObjectId, ref: 'RoyaltyPayment' }],
    reports: [{ type: mongoose.Schema.Types.ObjectId, ref: 'ComplianceReport' }],
    users: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }]
  },
  
  // Alert status and lifecycle
  status: {
    type: String,
    enum: ['active', 'acknowledged', 'in_progress', 'resolved', 'dismissed', 'escalated'],
    default: 'active'
  },
  
  isActive: { type: Boolean, default: true },
  isRecurring: { type: Boolean, default: false },
  
  // Timing and scheduling
  triggeredAt: { type: Date, default: Date.now },
  acknowledgedAt: Date,
  resolvedAt: Date,
  dismissedAt: Date,
  
  // Auto-resolution settings
  autoResolve: {
    enabled: { type: Boolean, default: false },
    condition: String, // Condition for auto-resolution
    checkInterval: Number // Minutes between checks
  },
  
  // Escalation rules
  escalation: {
    enabled: { type: Boolean, default: false },
    escalateAfter: Number, // Hours before escalation
    escalatedAt: Date,
    escalatedTo: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    escalationLevel: { type: Number, default: 0 }
  },
  
  // Assignment and responsibility
  assignedTo: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  assignedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  assignedAt: Date,
  
  // Actions and resolution
  suggestedActions: [{
    action: String,
    description: String,
    automated: { type: Boolean, default: false },
    priority: { type: String, enum: ['low', 'medium', 'high'], default: 'medium' }
  }],
  
  actionsTaken: [{
    action: String,
    performedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    performedAt: { type: Date, default: Date.now },
    result: String,
    notes: String
  }],
  
  resolution: {
    method: {
      type: String,
      enum: ['automatic', 'manual', 'system_fix', 'data_update', 'process_change', 'dismissed']
    },
    description: String,
    resolvedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    resolvedAt: Date,
    preventionMeasures: String
  },
  
  // Notification settings
  notifications: {
    email: { type: Boolean, default: true },
    sms: { type: Boolean, default: false },
    inApp: { type: Boolean, default: true },
    webhook: { type: Boolean, default: false },
    
    // Notification recipients
    recipients: [{
      userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      role: String,
      notificationMethods: [String]
    }],
    
    // Notification history
    sentNotifications: [{
      method: String,
      recipient: String,
      sentAt: { type: Date, default: Date.now },
      status: { type: String, enum: ['sent', 'delivered', 'failed'], default: 'sent' }
    }]
  },
  
  // Data and context
  contextData: {
    // Flexible field for storing alert-specific data
    metadata: mongoose.Schema.Types.Mixed,
    
    // Related compliance data
    complianceScore: Number,
    riskLevel: { type: String, enum: ['low', 'medium', 'high', 'critical'] },
    
    // Financial impact
    financialImpact: {
      estimatedCost: Number,
      currency: { type: String, default: 'ZAR' },
      impactDescription: String
    },
    
    // Regulatory impact
    regulatoryImpact: {
      organization: String, // SAMRO, SAMPRA, etc.
      regulation: String,
      penaltyRisk: String,
      complianceDeadline: Date
    }
  },
  
  // Recurrence and patterns
  recurrence: {
    pattern: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'quarterly', 'custom']
    },
    interval: Number,
    nextOccurrence: Date,
    lastOccurrence: Date,
    occurrenceCount: { type: Number, default: 0 }
  },
  
  // Related alerts and dependencies
  relatedAlerts: [{ type: mongoose.Schema.Types.ObjectId, ref: 'ComplianceAlert' }],
  parentAlert: { type: mongoose.Schema.Types.ObjectId, ref: 'ComplianceAlert' },
  childAlerts: [{ type: mongoose.Schema.Types.ObjectId, ref: 'ComplianceAlert' }],
  
  // Comments and communication
  comments: [{
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    comment: String,
    timestamp: { type: Date, default: Date.now },
    isInternal: { type: Boolean, default: true }
  }],
  
  // Tags and categorization
  tags: [String],
  customFields: mongoose.Schema.Types.Mixed,
  
  // System fields
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Indexes for efficient querying
complianceAlertSchema.index({ type: 1, category: 1, status: 1 });
complianceAlertSchema.index({ severity: 1, priority: 1, isActive: 1 });
complianceAlertSchema.index({ triggeredAt: -1 });
complianceAlertSchema.index({ assignedTo: 1, status: 1 });
complianceAlertSchema.index({ 'escalation.escalateAfter': 1, 'escalation.enabled': 1 });

// Pre-save middleware
complianceAlertSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Auto-generate alertId if not provided
  if (!this.alertId) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    this.alertId = `ALERT-${timestamp}-${random}`.toUpperCase();
  }
  
  // Update status timestamps
  if (this.isModified('status')) {
    const now = new Date();
    switch (this.status) {
      case 'acknowledged':
        if (!this.acknowledgedAt) this.acknowledgedAt = now;
        break;
      case 'resolved':
        if (!this.resolvedAt) this.resolvedAt = now;
        this.isActive = false;
        break;
      case 'dismissed':
        if (!this.dismissedAt) this.dismissedAt = now;
        this.isActive = false;
        break;
    }
  }
  
  next();
});

// Static method to find active alerts by severity
complianceAlertSchema.statics.findBySeverity = function(severity, limit = 50) {
  return this.find({
    severity,
    isActive: true,
    status: { $in: ['active', 'acknowledged', 'in_progress'] }
  })
  .sort({ triggeredAt: -1 })
  .limit(limit)
  .populate('assignedTo', 'username email')
  .populate('affectedEntities.stores', 'name location');
};

// Static method to find alerts requiring escalation
complianceAlertSchema.statics.findRequiringEscalation = function() {
  const now = new Date();
  return this.find({
    'escalation.enabled': true,
    'escalation.escalatedAt': { $exists: false },
    isActive: true,
    status: { $in: ['active', 'acknowledged'] },
    $expr: {
      $lt: [
        { $add: ['$triggeredAt', { $multiply: ['$escalation.escalateAfter', 60 * 60 * 1000] }] },
        now
      ]
    }
  });
};

// Instance method to acknowledge alert
complianceAlertSchema.methods.acknowledge = function(userId) {
  this.status = 'acknowledged';
  this.acknowledgedAt = new Date();
  this.updatedBy = userId;
  
  this.actionsTaken.push({
    action: 'Alert Acknowledged',
    performedBy: userId,
    result: 'Alert acknowledged by user'
  });
};

// Instance method to resolve alert
complianceAlertSchema.methods.resolve = function(userId, resolution) {
  this.status = 'resolved';
  this.resolvedAt = new Date();
  this.updatedBy = userId;
  this.isActive = false;
  
  if (resolution) {
    this.resolution = {
      ...resolution,
      resolvedBy: userId,
      resolvedAt: new Date()
    };
  }
  
  this.actionsTaken.push({
    action: 'Alert Resolved',
    performedBy: userId,
    result: resolution?.description || 'Alert resolved'
  });
};

// Instance method to escalate alert
complianceAlertSchema.methods.escalate = function(escalatedTo) {
  this.status = 'escalated';
  this.escalation.escalatedAt = new Date();
  this.escalation.escalationLevel += 1;
  
  if (escalatedTo) {
    this.escalation.escalatedTo = Array.isArray(escalatedTo) ? escalatedTo : [escalatedTo];
  }
  
  this.actionsTaken.push({
    action: 'Alert Escalated',
    result: `Alert escalated to level ${this.escalation.escalationLevel}`
  });
};

module.exports = mongoose.model('ComplianceAlert', complianceAlertSchema);
