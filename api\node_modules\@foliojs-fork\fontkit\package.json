{"name": "@foliojs-fork/fontkit", "version": "1.9.2", "description": "An advanced font engine for Node and the browser", "keywords": ["opentype", "font", "typography", "subset", "emoji", "glyph", "layout"], "scripts": {"test": "mocha", "prepublish": "run-s clean trie:** rollup", "trie:data": "babel-node src/opentype/shapers/generate-data.js", "trie:use": "babel-node src/opentype/shapers/gen-use.js", "trie:indic": "babel-node src/opentype/shapers/gen-indic.js", "trie:copy": "shx cp src/opentype/shapers/*.trie ./", "rollup": "rollup -c", "clean": "shx rm -f index.js base.js data.trie indic.trie use.trie src/opentype/shapers/data.trie src/opentype/shapers/use.trie src/opentype/shapers/use.json src/opentype/shapers/indic.trie src/opentype/shapers/indic.json", "coverage": "cross-env BABEL_ENV=cover nyc mocha"}, "main": "index.js", "jsnext:main": "src/index.js", "files": ["src", "base.js", "base.js.map", "index.js", "index.js.map", "base.es5.js", "base.es5.js.map", "index.es5.js", "index.es5.js.map", "data.trie", "use.trie", "indic.trie"], "author": "<PERSON> Go<PERSON>t <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/foliojs-fork/fontkit.git"}, "dependencies": {"brotli": "^1.2.0", "clone": "^1.0.4", "deep-equal": "^1.0.0", "dfa": "^1.2.0", "@foliojs-fork/restructure": "^2.0.2", "tiny-inflate": "^1.0.2", "unicode-properties": "^1.2.2", "unicode-trie": "^2.0.0"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/node": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/register": "^7.0.0", "babel-plugin-istanbul": "^5.1.1", "brfs": "^2.0.0", "browserify-optional": "^1.0.1", "codepoints": "^1.2.0", "concat-stream": "^1.6.2", "cross-env": "^5.0.1", "esdoc": "^0.4.8", "esdoc-es7-plugin": "0.0.3", "iconv-lite": "^0.4.24", "mocha": "^2.0.1", "npm-run-all": "^4.0.2", "nyc": "^10.3.2", "rollup": "^1.5.0", "rollup-plugin-babel": "^4.0.1", "rollup-plugin-json": "^3.1.0", "rollup-plugin-local-resolve": "^1.0.7", "shx": "^0.2.2"}, "browserify": {"transform": ["brfs", "browserify-optional"]}}