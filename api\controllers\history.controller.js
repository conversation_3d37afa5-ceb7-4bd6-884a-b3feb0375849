const PlayHistory = require('../models/PlayHistory.model');
const Track = require('../models/Track.model');
const Store = require('../models/Store.model');
const { authenticate } = require('../middleware/auth.middleware');

exports.logPlay = async (req, res) => {
  try {
    const {
      storeId,
      deviceId,
      trackId,
      playlistId,
      startTime,
      endTime,
      metadata = {},
      location = {},
      compliance = {}
    } = req.body;

    // Validate required fields
    if (!storeId || !deviceId || !trackId || !startTime) {
      return res.status(400).json({
        error: 'Missing required fields: storeId, deviceId, trackId, startTime'
      });
    }

    // Validate that store and track exist
    const [store, track] = await Promise.all([
      Store.findById(storeId),
      Track.findById(trackId)
    ]);

    if (!store) {
      return res.status(404).json({ error: 'Store not found' });
    }

    if (!track) {
      return res.status(404).json({ error: 'Track not found' });
    }

    const startDate = new Date(startTime);
    const endDate = endTime ? new Date(endTime) : new Date();
    const durationPlayed = Math.max(0, (endDate - startDate) / 1000);

    // Create comprehensive play history entry
    const playHistoryData = {
      storeId,
      deviceId,
      trackId,
      playlistId,
      startTime: startDate,
      endTime: endDate,
      playedDate: startDate,
      durationPlayed,
      totalTrackDuration: track.duration,
      completionPercentage: track.duration > 0 ? Math.min(100, (durationPlayed / track.duration) * 100) : 0,

      // Location data
      location: {
        city: location.city || store.address?.city || 'Unknown',
        province: location.province || store.address?.province || 'Unknown',
        country: location.country || 'South Africa',
        coordinates: location.coordinates || null,
        timezone: store.timezone || 'Africa/Johannesburg'
      },

      // Enhanced metadata
      metadata: {
        timezone: store.timezone || 'Africa/Johannesburg',
        scheduledTime: metadata.scheduledTime,
        scheduledDay: metadata.scheduledDay,
        playlistName: metadata.playlistName,
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip || req.connection.remoteAddress,
        deviceType: metadata.deviceType || 'unknown',
        volume: metadata.volume,
        audioQuality: metadata.audioQuality,
        sourceType: metadata.sourceType || 'music_player',
        radioStationId: metadata.radioStationId,
        radioStationName: metadata.radioStationName,
        switchedFrom: metadata.switchedFrom,
        switchedTo: metadata.switchedTo
      },

      // Compliance information
      compliance: {
        reportedToSAMRO: compliance.reportedToSAMRO || false,
        reportedToSAMPRA: compliance.reportedToSAMPRA || false,
        reportedToRISA: compliance.reportedToRISA || false
      },

      // Audit information
      auditInfo: {
        sessionId: metadata.sessionId || `session_${Date.now()}`,
        playSequence: metadata.playSequence || 0,
        repeatCount: metadata.repeatCount || 0,
        skipReason: metadata.skipReason,
        technicalIssues: metadata.technicalIssues || []
      }
    };

    const playHistory = await PlayHistory.create(playHistoryData);

    res.status(201).json({
      success: true,
      message: 'Play logged successfully',
      data: playHistory
    });

  } catch (error) {
    console.error('Error logging play:', error);
    res.status(500).json({
      error: 'Failed to log play',
      details: error.message
    });
  }
};

exports.getHistory = async (req, res) => {
  try {
    const {
      storeId,
      trackId,
      track,
      store,
      startDate,
      endDate,
      limit = 50,
      offset = 0,
      sortBy = 'playedDate',
      sortOrder = 'desc'
    } = req.query;

    const query = {};
    const sort = {};

    // Build query
    if (storeId) query.storeId = storeId;
    if (trackId) query.trackId = trackId;

    // Date range filter
    if (startDate && endDate) {
      query.playedDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    } else if (startDate) {
      query.playedDate = { $gte: new Date(startDate) };
    } else if (endDate) {
      query.playedDate = { $lte: new Date(endDate) };
    }

    // Build sort
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with proper population
    const [logs, totalCount] = await Promise.all([
      PlayHistory.find(query)
        .populate('trackId', 'title artist album genre duration isrcCode iswcCode')
        .populate('storeId', 'name address timezone')
        .populate('playlistId', 'name')
        .sort(sort)
        .limit(parseInt(limit))
        .skip(parseInt(offset)),
      PlayHistory.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: logs,
      pagination: {
        total: totalCount,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: (parseInt(offset) + parseInt(limit)) < totalCount
      }
    });

  } catch (error) {
    console.error('Error fetching history:', error);
    res.status(500).json({
      error: 'Failed to fetch history',
      details: error.message
    });
  }
};

exports.syncOfflineLogs = async (req, res) => {
  try {
    let { logs } = req.body;

    // Handle different request formats
    if (!logs && Array.isArray(req.body)) {
      logs = req.body;
    }

    if (!logs) {
      return res.status(400).json({
        error: 'No logs provided. Expected { logs: [...] } or array of logs.'
      });
    }

    if (!Array.isArray(logs)) {
      return res.status(400).json({
        error: 'Invalid data format. Expected array of logs.'
      });
    }

    if (logs.length === 0) {
      return res.json({
        success: true,
        message: 'No logs to sync',
        synced: 0
      });
    }

    // Validate and process each log
    const validLogs = [];
    const errors = [];

    for (let i = 0; i < logs.length; i++) {
      const log = logs[i];

      try {
        // Validate required fields
        if (!log.storeId || !log.deviceId || !log.trackId || !log.startTime) {
          const missingFields = [];
          if (!log.storeId) missingFields.push('storeId');
          if (!log.deviceId) missingFields.push('deviceId');
          if (!log.trackId) missingFields.push('trackId');
          if (!log.startTime) missingFields.push('startTime');

          errors.push({
            index: i,
            error: `Missing required fields: ${missingFields.join(', ')}`,
            log: log
          });
          continue;
        }

        // Verify store and track exist
        const [store, track] = await Promise.all([
          Store.findById(log.storeId).catch(() => null),
          Track.findById(log.trackId).catch(() => null)
        ]);

        if (!store) {
          errors.push({
            index: i,
            error: `Store not found: ${log.storeId}`,
            log: log
          });
          continue;
        }

        if (!track) {
          errors.push({
            index: i,
            error: `Track not found: ${log.trackId}`,
            log: log
          });
          continue;
        }

        // Process the log data
        const totalTrackDuration = log.totalTrackDuration || track.duration || 0;
        const durationPlayed = log.durationPlayed || 0;

        const processedLog = {
          ...log,
          playedDate: log.playedDate || log.startTime,
          totalTrackDuration,
          completionPercentage: totalTrackDuration > 0 ?
            Math.min(100, (durationPlayed / totalTrackDuration) * 100) : 0,

          // Ensure metadata structure
          metadata: {
            timezone: store.timezone || 'Africa/Johannesburg',
            playlistName: log.metadata?.playlistName || 'Unknown',
            sourceType: log.metadata?.sourceType || 'music_player',
            ...log.metadata
          },

          // Ensure location structure
          location: {
            city: store.address?.city || 'Unknown',
            province: store.address?.province || 'Unknown',
            country: 'South Africa',
            timezone: store.timezone || 'Africa/Johannesburg',
            ...log.location
          }
        };

        validLogs.push(processedLog);

      } catch (error) {
        errors.push({
          index: i,
          error: error.message,
          log: log
        });
      }
    }

    // Insert valid logs
    let syncedCount = 0;
    if (validLogs.length > 0) {
      const result = await PlayHistory.insertMany(validLogs, { ordered: false });
      syncedCount = result.length;
    }

    res.json({
      success: true,
      message: `Successfully synced ${syncedCount} of ${logs.length} logs`,
      synced: syncedCount,
      total: logs.length,
      errors: errors.length > 0 ? errors : undefined
    });

  } catch (error) {
    console.error('Error syncing offline logs:', error);
    res.status(500).json({
      error: 'Failed to sync offline logs',
      details: error.message
    });
  }
};