import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Box,
  AppBar,
  Toolbar,
  IconButton,
  Typography,
  useTheme,
  useMediaQuery,
  Grid,
  Card,
  CardContent,
  Chip,
  Alert
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import VerifiedIcon from '@mui/icons-material/Verified';
import AssessmentIcon from '@mui/icons-material/Assessment';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import SecurityIcon from '@mui/icons-material/Security';

import MaterialSidebar from '../components/MaterialSidebar';
import ComplianceReports from '../components/ComplianceReports';
import ComplianceAnalytics from '../components/ComplianceAnalytics';
import ComplianceDataCollection from '../components/ComplianceDataCollection';
import ComplianceAuditTrail from '../components/ComplianceAuditTrail';
import ComplianceAlerts from '../components/ComplianceAlerts';
import VenueLicenseManagement from '../components/VenueLicenseManagement';
import ComplianceValidation from '../components/ComplianceValidation';
import MaterialMessages from '../components/MaterialMessages';
import MaterialAccountManagement from '../components/MaterialAccountManagement';
import { useAuth } from '../context/AuthContext';
import { complianceService, samroService, sampraService } from '../services/api';

const ComplianceDashboard = () => {
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'overview');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));
  const { user, isSAMRO, isSAMPRA, isComplianceAdmin } = useAuth();

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      let response;

      // Use organization-specific dashboard endpoints
      if (isSAMRO) {
        console.log('Fetching SAMRO dashboard data...');
        response = await samroService.getDashboard();
      } else if (isSAMPRA) {
        console.log('Fetching SAMPRA dashboard data...');
        response = await sampraService.getDashboard();
      } else {
        console.log('Fetching generic compliance dashboard data...');
        response = await complianceService.getDashboard();
      }

      console.log('Dashboard data received:', response.data);
      setDashboardData(response.data);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Show user-friendly error message
      setDashboardData(null);
    } finally {
      setLoading(false);
    }
  };

  const getOrganizationColor = () => {
    if (isSAMRO) return 'primary';
    if (isSAMPRA) return 'secondary';
    return 'success';
  };

  const getOrganizationName = () => {
    if (isSAMRO) return 'SAMRO';
    if (isSAMPRA) return 'SAMPRA';
    if (isComplianceAdmin) return 'Compliance Admin';
    return user?.organization || 'Compliance';
  };

  const renderOverview = () => (
    <Box>
      {/* Welcome Section */}
      <Card sx={{ mb: 3, bgcolor: `${getOrganizationColor()}.main`, color: 'white' }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <SecurityIcon sx={{ fontSize: 40 }} />
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700 }}>
                {getOrganizationName()} Compliance Dashboard
              </Typography>
              <Typography variant="h6" sx={{ opacity: 0.9 }}>
                Music Rights Data Collection & Monitoring
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      {dashboardData && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <AssessmentIcon color="primary" sx={{ fontSize: 32 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      {dashboardData.overview?.totalTracks || dashboardData.overview?.totalReports || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {isSAMRO || isSAMPRA ? 'Total Tracks' : 'Total Reports (30 days)'}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <VerifiedIcon color="success" sx={{ fontSize: 32 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                      {isSAMRO ? dashboardData.overview?.samroRegisteredTracks || 0 :
                       isSAMPRA ? dashboardData.overview?.sampraRegisteredTracks || 0 :
                       `${dashboardData.overview?.complianceRate || 0}%`}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {isSAMRO ? 'SAMRO Registered' :
                       isSAMPRA ? 'SAMPRA Registered' :
                       'Compliance Rate'}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <TrendingUpIcon color="info" sx={{ fontSize: 32 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      {(isSAMRO || isSAMPRA) ?
                        (dashboardData.overview?.complianceRate || 0) + '%' :
                        (dashboardData.playStats?.totalPlays || 0).toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {(isSAMRO || isSAMPRA) ? 'Compliance Rate' : 'Total Plays (7 days)'}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <SecurityIcon color="warning" sx={{ fontSize: 32 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      {dashboardData.overview?.pendingVerification || dashboardData.overview?.pendingReports || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {(isSAMRO || isSAMPRA) ? 'Pending Verification' : 'Pending Reviews'}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Organization-specific Information */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                Recent Compliance Reports
              </Typography>
              {dashboardData?.recentReports?.length > 0 ? (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {dashboardData.recentReports.slice(0, 5).map((report) => (
                    <Box
                      key={report._id}
                      sx={{
                        p: 2,
                        border: 1,
                        borderColor: 'divider',
                        borderRadius: 1,
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                      }}
                    >
                      <Box>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                          {report.reportId}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {report.reportType} • {new Date(report.createdAt).toLocaleDateString()}
                        </Typography>
                      </Box>
                      <Chip
                        label={report.compliance.verificationStatus}
                        color={
                          report.compliance.verificationStatus === 'verified' ? 'success' :
                          report.compliance.verificationStatus === 'pending' ? 'warning' : 'default'
                        }
                        size="small"
                      />
                    </Box>
                  ))}
                </Box>
              ) : (
                <Typography color="text.secondary">No recent reports available</Typography>
              )}
            </CardContent>
          </Card>

          {/* Organization-specific Alerts and Activity */}
          {(isSAMRO || isSAMPRA) && dashboardData && (
            <>
              {dashboardData.activeAlerts && dashboardData.activeAlerts.length > 0 && (
                <Card sx={{ mt: 2 }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                      Active {getOrganizationName()} Alerts
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      {dashboardData.activeAlerts.slice(0, 3).map((alert) => (
                        <Alert
                          key={alert._id}
                          severity={alert.severity || 'info'}
                          sx={{ fontSize: '0.875rem' }}
                        >
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {alert.title || alert.message}
                          </Typography>
                          {alert.description && (
                            <Typography variant="caption" display="block">
                              {alert.description}
                            </Typography>
                          )}
                        </Alert>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              )}

              {dashboardData.recentActivity && dashboardData.recentActivity.length > 0 && (
                <Card sx={{ mt: 2 }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                      Recent {getOrganizationName()} Activity
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      {dashboardData.recentActivity.slice(0, 5).map((activity) => (
                        <Box
                          key={activity._id}
                          sx={{
                            p: 1.5,
                            border: 1,
                            borderColor: 'divider',
                            borderRadius: 1,
                            bgcolor: 'background.default'
                          }}
                        >
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {activity.title} - {activity.artist}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Updated: {new Date(activity.updatedAt).toLocaleDateString()}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </Grid>

        <Grid item xs={12} md={4}>
          {/* This section was removed - SAMRO/SAMPRA Metrics */}
        </Grid>
      </Grid>
    </Box>
  );

  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      width: '100%',
      position: 'relative'
    }}>
      {/* Sidebar */}
      <MaterialSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

      {/* Main Content */}
      <Box
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          minWidth: 0, // Prevent flex item from overflowing
          overflow: 'hidden', // Prevent content from overflowing
          width: '100%',
          maxWidth: '100%'
        }}
      >
        {/* Mobile Header */}
        {isMobile && (
          <AppBar position="static" sx={{ bgcolor: 'background.paper' }}>
            <Toolbar>
              <IconButton
                edge="start"
                color="inherit"
                onClick={() => setSidebarOpen(true)}
                sx={{ mr: 2 }}
              >
                <MenuIcon />
              </IconButton>
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                {getOrganizationName()} Dashboard
              </Typography>
            </Toolbar>
          </AppBar>
        )}

        {/* Content Area */}
        <Box sx={{ p: 3, flexGrow: 1, minWidth: 0, overflow: 'auto' }}>
          {/* Desktop Header */}
          

          {/* Tab Content */}
          <Box sx={{ mt: 2 }}>
            {activeTab === 'overview' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                {renderOverview()}
              </motion.div>
            )}

            {activeTab === 'reports' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <ComplianceReports />
              </motion.div>
            )}

            {activeTab === 'analytics' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <ComplianceAnalytics />
              </motion.div>
            )}

            {activeTab === 'collection' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <ComplianceDataCollection />
              </motion.div>
            )}

            {activeTab === 'audit' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <ComplianceAuditTrail />
              </motion.div>
            )}

            {activeTab === 'alerts' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <ComplianceAlerts />
              </motion.div>
            )}

            {activeTab === 'licenses' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <VenueLicenseManagement />
              </motion.div>
            )}



            {activeTab === 'validation' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <ComplianceValidation />
              </motion.div>
            )}

            {activeTab === 'messages' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialMessages />
              </motion.div>
            )}

            {activeTab === 'account' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialAccountManagement />
              </motion.div>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ComplianceDashboard;
