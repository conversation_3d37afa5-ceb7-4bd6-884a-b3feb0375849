const bcrypt = require('bcryptjs');
const User = require('../models/User.model');
const Store = require('../models/Store.model');
const AuditLog = require('../models/AuditLog.model');
const { auditActions } = require('../middleware/audit.middleware');

// Get current user profile
exports.getProfile = async (req, res) => {
  try {
    const user = await User.findById(req.user.id)
      .select('-password')
      .populate('storeId', 'name address city province postalCode phone');

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      id: user._id,
      username: user.username,
      email: user.email,
      role: user.role,
      organization: user.organization,
      permissions: user.permissions,
      emailVerified: user.emailVerified,
      accountStatus: user.accountStatus,
      lastLogin: user.lastLogin,
      lastActivity: user.lastActivity,
      createdAt: user.createdAt,
      store: user.storeId
    });
  } catch (error) {
    console.error('Error getting profile:', error);
    res.status(500).json({ error: 'Failed to get profile' });
  }
};

// Update user profile
exports.updateProfile = async (req, res) => {
  try {
    const { username, email } = req.body;
    const userId = req.user.id;

    // Validate input
    if (!username || !email) {
      return res.status(400).json({ error: 'Username and email are required' });
    }

    // Check if username is already taken by another user
    const existingUser = await User.findOne({ 
      username, 
      _id: { $ne: userId } 
    });

    if (existingUser) {
      return res.status(400).json({ error: 'Username already taken' });
    }

    // Check if email is already taken by another user
    const existingEmail = await User.findOne({ 
      email, 
      _id: { $ne: userId } 
    });

    if (existingEmail) {
      return res.status(400).json({ error: 'Email already taken' });
    }

    // Update user
    const user = await User.findById(userId);
    const oldData = {
      username: user.username,
      email: user.email
    };

    user.username = username;
    user.email = email;
    user.updatedAt = new Date();

    // If email changed, mark as unverified
    if (oldData.email !== email) {
      user.emailVerified = false;
    }

    await user.save();

    // Log the profile update
    try {
      await auditActions.profileUpdate(
        userId,
        req.ip || req.connection.remoteAddress || 'unknown',
        req.get('User-Agent') || 'unknown',
        user.organization || 'ADMIN',
        { oldData, newData: { username, email } }
      );
    } catch (auditError) {
      console.error('Failed to log profile update audit:', auditError);
    }

    res.json({ 
      message: 'Profile updated successfully',
      emailVerificationRequired: oldData.email !== email
    });
  } catch (error) {
    console.error('Error updating profile:', error);
    res.status(500).json({ error: 'Failed to update profile' });
  }
};

// Change password
exports.changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    // Validate input
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: 'Current password and new password are required' });
    }

    if (newPassword.length < 8) {
      return res.status(400).json({ error: 'New password must be at least 8 characters long' });
    }

    // Get user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({ error: 'Current password is incorrect' });
    }

    // Hash new password
    const saltRounds = 10;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update password
    user.password = hashedNewPassword;
    user.updatedAt = new Date();
    await user.save();

    // Log the password change
    try {
      await auditActions.passwordChange(
        userId,
        req.ip || req.connection.remoteAddress || 'unknown',
        req.get('User-Agent') || 'unknown',
        user.organization || 'ADMIN'
      );
    } catch (auditError) {
      console.error('Failed to log password change audit:', auditError);
    }

    res.json({ message: 'Password changed successfully' });
  } catch (error) {
    console.error('Error changing password:', error);
    res.status(500).json({ error: 'Failed to change password' });
  }
};

// Get account activity
exports.getActivity = async (req, res) => {
  try {
    const userId = req.user.id;
    const limit = parseInt(req.query.limit) || 20;
    const page = parseInt(req.query.page) || 1;
    const skip = (page - 1) * limit;

    // Get audit logs for the user
    const activities = await AuditLog.find({ userId })
      .sort({ timestamp: -1 })
      .limit(limit)
      .skip(skip)
      .select('action details timestamp ipAddress userAgent');

    // Format activities for frontend
    const formattedActivities = activities.map(activity => ({
      action: activity.action,
      details: activity.details,
      timestamp: activity.timestamp,
      ipAddress: activity.ipAddress,
      userAgent: activity.userAgent
    }));

    res.json(formattedActivities);
  } catch (error) {
    console.error('Error getting account activity:', error);
    res.status(500).json({ error: 'Failed to get account activity' });
  }
};

// Get store information (for store users)
exports.getStoreInfo = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user || !user.storeId) {
      return res.status(404).json({ error: 'Store not found' });
    }

    const store = await Store.findById(user.storeId);
    if (!store) {
      return res.status(404).json({ error: 'Store not found' });
    }

    res.json({
      id: store._id,
      name: store.name,
      email: store.email,
      address: store.address,
      city: store.city,
      province: store.province,
      country: store.country,
      postalCode: store.postalCode,
      phone: store.phone,
      timezone: store.timezone,
      businessType: store.businessType,
      businessRegistrationNumber: store.businessRegistrationNumber,
      vatNumber: store.vatNumber,
      licenses: store.licenses,
      openHours: store.openHours,
      isActive: store.isActive,
      createdAt: store.createdAt
    });
  } catch (error) {
    console.error('Error getting store info:', error);
    res.status(500).json({ error: 'Failed to get store information' });
  }
};

// Update store information (for store users - creates update request)
exports.updateStoreInfo = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user || !user.storeId) {
      return res.status(404).json({ error: 'Store not found' });
    }

    const { name, address, city, province, postalCode, phone, timezone, openHours } = req.body;

    // For now, store users can update certain fields directly
    // In a production system, you might want to create approval requests for some changes
    const updateData = {
      name,
      address,
      city,
      province,
      postalCode,
      phone,
      timezone,
      openHours,
      updatedAt: new Date()
    };

    const store = await Store.findByIdAndUpdate(
      user.storeId,
      updateData,
      { new: true, runValidators: true }
    );

    if (!store) {
      return res.status(404).json({ error: 'Store not found' });
    }

    res.json({
      message: 'Store information updated successfully',
      store: {
        id: store._id,
        name: store.name,
        address: store.address,
        city: store.city,
        province: store.province,
        postalCode: store.postalCode,
        phone: store.phone,
        timezone: store.timezone,
        openHours: store.openHours
      }
    });
  } catch (error) {
    console.error('Error updating store info:', error);
    res.status(500).json({ error: 'Failed to update store information' });
  }
};

// Get account settings
exports.getSettings = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('settings');
    
    // Default settings if none exist
    const defaultSettings = {
      emailNotifications: true,
      pushNotifications: true,
      theme: 'light',
      language: 'en',
      timezone: 'Africa/Johannesburg'
    };

    res.json(user?.settings || defaultSettings);
  } catch (error) {
    console.error('Error getting settings:', error);
    res.status(500).json({ error: 'Failed to get settings' });
  }
};

// Update account settings
exports.updateSettings = async (req, res) => {
  try {
    const userId = req.user.id;
    const settings = req.body;

    await User.findByIdAndUpdate(userId, { 
      settings,
      updatedAt: new Date()
    });

    res.json({ message: 'Settings updated successfully' });
  } catch (error) {
    console.error('Error updating settings:', error);
    res.status(500).json({ error: 'Failed to update settings' });
  }
};

// Request email verification
exports.requestEmailVerification = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (user.emailVerified) {
      return res.status(400).json({ error: 'Email already verified' });
    }

    // TODO: Implement email verification logic
    // For now, just return success
    res.json({ message: 'Verification email sent' });
  } catch (error) {
    console.error('Error requesting email verification:', error);
    res.status(500).json({ error: 'Failed to send verification email' });
  }
};

// Verify email
exports.verifyEmail = async (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      return res.status(400).json({ error: 'Verification token required' });
    }

    // TODO: Implement email verification logic
    // For now, just return success
    res.json({ message: 'Email verified successfully' });
  } catch (error) {
    console.error('Error verifying email:', error);
    res.status(500).json({ error: 'Failed to verify email' });
  }
};
