const PlayHistory = require('../models/PlayHistory.model');
const { format } = require('date-fns-tz');
const { generateCSVReport, generatePDFReport } = require('../services/report.service');

exports.generateDailyReport = async (req, res) => {
  const { date } = req.query;
  const tz = req.query.tz || 'Africa/Johannesburg';

  const startOfDay = format(new Date(date), 'yyyy-MM-dd 00:00:00', { timeZone: tz });
  const endOfDay = format(new Date(date), 'yyyy-MM-dd 23:59:59', { timeZone: tz });

  const records = await PlayHistory.find({
    playedDate: { $gte: new Date(startOfDay), $lte: new Date(endOfDay) }
  })
  .populate('trackId', 'title artist album genre')
  .populate('storeId', 'name');

  res.json(records);
};

exports.exportDailyToCSV = async (req, res) => {
  const { date } = req.query;
  const logs = await PlayHistory.find({ playedDate: date });
  const reportPath = await generateCSVReport(logs, `daily-report-${date}`);
  res.download(reportPath);
};

exports.exportDailyToPDF = async (req, res) => {
  const { date } = req.query;
  const logs = await PlayHistory.find({ playedDate: date });
  const reportPath = await generatePDFReport(logs, `daily-report-${date}`);
  res.download(reportPath);
};