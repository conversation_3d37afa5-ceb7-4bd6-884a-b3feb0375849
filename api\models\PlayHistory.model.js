const mongoose = require('mongoose');

const playHistorySchema = new mongoose.Schema({
  storeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Store', required: true },
  deviceId: { type: String, required: true },
  trackId: { type: mongoose.Schema.Types.ObjectId, ref: 'Track', required: true },
  playlistId: { type: mongoose.Schema.Types.ObjectId, ref: 'Playlist' },

  // Precise timing information
  startTime: { type: Date, required: true },
  endTime: Date,
  playedDate: { type: Date, default: Date.now },
  durationPlayed: { type: Number, default: 0 }, // Actual duration played in seconds
  totalTrackDuration: { type: Number, required: true }, // Total track duration in seconds

  // Playback completion status
  playbackStatus: {
    type: String,
    enum: ['completed', 'skipped', 'interrupted', 'partial'],
    default: 'completed'
  },
  completionPercentage: { type: Number, min: 0, max: 100, default: 100 },

  // Location and device information
  location: {
    storeAddress: String,
    city: String,
    province: String,
    country: { type: String, default: 'ZA' },
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },

  // Enhanced metadata for compliance reporting
  metadata: {
    timezone: { type: String, default: 'Africa/Johannesburg' },
    scheduledTime: String,
    scheduledDay: String,
    playlistName: String,
    userAgent: String,
    ipAddress: String,
    deviceType: String,
    volume: Number, // Volume level when played
    audioQuality: String, // Audio quality/bitrate
    sourceType: {
      type: String,
      enum: ['music_player', 'radio_stream'],
      default: 'music_player'
    },
    radioStationId: { type: mongoose.Schema.Types.ObjectId, ref: 'RadioStation' },
    radioStationName: String,
    switchedFrom: {
      type: String,
      enum: ['music_player', 'radio_stream', 'silence']
    },
    switchedTo: {
      type: String,
      enum: ['music_player', 'radio_stream', 'silence']
    }
  },

  // Compliance information
  compliance: {
    reportedToSAMRO: { type: Boolean, default: false },
    reportedToSAMPRA: { type: Boolean, default: false },
    reportedToRISA: { type: Boolean, default: false },
    reportingDate: Date
  },

  // Audit trail
  auditInfo: {
    sessionId: String,
    playSequence: Number, // Order in playlist
    repeatCount: { type: Number, default: 0 },
    skipReason: String, // If skipped, reason why
    technicalIssues: [String], // Any technical issues during playback
    // New fields for SAMRO/SAMPRA compliance
    playbackSource: {
      type: String,
      enum: ['music_player', 'radio_stream', 'silence', 'unknown'],
      default: 'music_player'
    },
    systemUsed: {
      type: String,
      enum: ['manual', 'automated', 'scheduled', 'unknown'],
      default: 'unknown'
    },
    triggerType: {
      type: String,
      enum: ['user_action', 'playlist_auto', 'schedule_auto', 'system_auto', 'unknown'],
      default: 'unknown'
    },
    isManualSelection: { type: Boolean, default: false },
    isScheduledPlayback: { type: Boolean, default: false },
    isAutomatedPlayback: { type: Boolean, default: true }
  }
}, {
  timestamps: true
});

// Indexes for efficient querying and reporting
playHistorySchema.index({ storeId: 1, playedDate: -1 });
playHistorySchema.index({ trackId: 1, playedDate: -1 });
playHistorySchema.index({ playedDate: -1 });
playHistorySchema.index({ 'compliance.reportedToSAMRO': 1 });
playHistorySchema.index({ 'compliance.reportedToSAMPRA': 1 });
playHistorySchema.index({ 'location.city': 1, 'location.province': 1 });

// Calculate completion percentage before saving
playHistorySchema.pre('save', function(next) {
  if (this.durationPlayed && this.totalTrackDuration) {
    this.completionPercentage = Math.min(100, (this.durationPlayed / this.totalTrackDuration) * 100);
  }
  next();
});

module.exports = mongoose.model('PlayHistory', playHistorySchema);