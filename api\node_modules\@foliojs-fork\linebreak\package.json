{"name": "@foliojs-fork/linebreak", "version": "1.1.2", "description": "An implementation of the Unicode Line Breaking Algorithm (UAX #14)", "repository": {"type": "git", "url": "https://github.com/foliojs-fork/linebreaker.git"}, "keywords": ["unicode", "text", "wrapping"], "author": "<PERSON> Go<PERSON>t <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/foliojs-fork/linebreaker/issues"}, "homepage": "https://github.com/foliojs-fork/linebreaker", "dependencies": {"base64-js": "1.3.1", "unicode-trie": "^2.0.0"}, "devDependencies": {"brfs": "^2.0.2", "mocha": "^7.0.1", "request": "^2.88.0"}, "scripts": {"test": "mocha --reporter landing"}, "main": "src/linebreaker", "browserify": {"transform": ["brfs"]}}