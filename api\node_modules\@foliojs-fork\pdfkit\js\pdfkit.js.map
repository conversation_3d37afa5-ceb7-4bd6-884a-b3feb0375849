{"version": 3, "file": "pdfkit.js", "sources": ["../lib/abstract_reference.js", "../lib/tree.js", "../lib/spotcolor.js", "../lib/object.js", "../lib/reference.js", "../lib/page.js", "../lib/name_tree.js", "../lib/saslprep/lib/util.js", "../lib/saslprep/lib/code-points.js", "../lib/saslprep/index.js", "../lib/security.js", "../lib/gradient.js", "../lib/pattern.js", "../lib/mixins/color.js", "../lib/path.js", "../lib/mixins/vector.js", "../lib/font/afm.js", "../lib/font.js", "../lib/font/standard.js", "../lib/font/embedded.js", "../lib/font_factory.js", "../lib/mixins/fonts.js", "../lib/line_wrapper.js", "../lib/mixins/text.js", "../lib/image/jpeg.js", "../lib/image/png.js", "../lib/image.js", "../lib/mixins/images.js", "../lib/mixins/annotations.js", "../lib/outline.js", "../lib/mixins/outline.js", "../lib/structure_content.js", "../lib/structure_element.js", "../lib/number_tree.js", "../lib/mixins/markings.js", "../lib/mixins/acroform.js", "../lib/mixins/attachments.js", "../lib/mixins/pdfa.js", "../lib/mixins/pdfua.js", "../lib/mixins/subsets.js", "../lib/metadata.js", "../lib/mixins/metadata.js", "../lib/document.js"], "sourcesContent": ["/*\r\nPDFAbstractReference - abstract class for PDF reference\r\n*/\r\n\r\nclass PDFAbstractReference {\r\n  toString() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n}\r\n\r\nexport default PDFAbstractReference;\r\n", "/*\r\nPDFTree - abstract base class for name and number tree objects\r\n*/\r\n\r\nimport PDFObject from './object';\r\n\r\nclass PDFTree {\r\n  constructor(options = {}) {\r\n    this._items = {};\r\n    // disable /Limits output for this tree\r\n    this.limits =\r\n      typeof options.limits === 'boolean' ? options.limits : true;\r\n  }\r\n\r\n  add(key, val) {\r\n    return (this._items[key] = val);\r\n  }\r\n\r\n  get(key) {\r\n    return this._items[key];\r\n  }\r\n\r\n  toString() {\r\n    // Needs to be sorted by key\r\n    const sortedKeys = Object.keys(this._items).sort((a, b) =>\r\n      this._compareKeys(a, b)\r\n    );\r\n\r\n    const out = ['<<'];\r\n    if (this.limits && sortedKeys.length > 1) {\r\n      const first = sortedKeys[0],\r\n        last = sortedKeys[sortedKeys.length - 1];\r\n      out.push(\r\n        `  /Limits ${PDFObject.convert([this._dataForKey(first), this._dataForKey(last)])}`\r\n      );\r\n    }\r\n    out.push(`  /${this._keysName()} [`);\r\n    for (let key of sortedKeys) {\r\n      out.push(\r\n        `    ${PDFObject.convert(this._dataFor<PERSON>ey(key))} ${PDFObject.convert(\r\n          this._items[key]\r\n        )}`\r\n      );\r\n    }\r\n    out.push(']');\r\n    out.push('>>');\r\n    return out.join('\\n');\r\n  }\r\n\r\n  _compareKeys(/*a, b*/) {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  _keysName() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  _dataForKey(/*k*/) {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n}\r\n\r\nexport default PDFTree;\r\n", "export default class SpotColor {\r\n  constructor(doc, name, C, M, Y, K) {\r\n    this.id = 'CS' + Object.keys(doc.spotColors).length;\r\n    this.name = name;\r\n    this.values = [C, M, Y, K];\r\n    this.ref = doc.ref([\r\n      'Separation',\r\n      this.name,\r\n      'DeviceCMYK',\r\n      {\r\n        Range: [0, 1, 0, 1, 0, 1, 0, 1],\r\n        C0: [0, 0, 0, 0],\r\n        C1: this.values.map(value => value / 100),\r\n        FunctionType: 2,\r\n        Domain: [0, 1],\r\n        N: 1\r\n      }\r\n    ]);\r\n    this.ref.end();\r\n  }\r\n\r\n  toString() {\r\n    return `${this.ref.id} 0 R`;\r\n  }\r\n}\r\n", "/*\r\nPDFObject - converts JavaScript types into their corresponding PDF types.\r\nBy <PERSON>\r\n*/\r\n\r\nimport PDFAbstractReference from './abstract_reference';\r\nimport PDFTree from './tree';\r\nimport SpotColor from './spotcolor';\r\n\r\nconst pad = (str, length) => (Array(length + 1).join('0') + str).slice(-length);\r\n\r\nconst escapableRe = /[\\n\\r\\t\\b\\f()\\\\]/g;\r\nconst escapable = {\r\n  '\\n': '\\\\n',\r\n  '\\r': '\\\\r',\r\n  '\\t': '\\\\t',\r\n  '\\b': '\\\\b',\r\n  '\\f': '\\\\f',\r\n  '\\\\': '\\\\\\\\',\r\n  '(': '\\\\(',\r\n  ')': '\\\\)'\r\n};\r\n\r\n// Convert little endian UTF-16 to big endian\r\nconst swapBytes = function(buff) {\r\n  const l = buff.length;\r\n  if (l & 0x01) {\r\n    throw new Error('Buffer length must be even');\r\n  } else {\r\n    for (let i = 0, end = l - 1; i < end; i += 2) {\r\n      const a = buff[i];\r\n      buff[i] = buff[i + 1];\r\n      buff[i + 1] = a;\r\n    }\r\n  }\r\n\r\n  return buff;\r\n};\r\n\r\nclass PDFObject {\r\n  static convert(object, encryptFn = null) {\r\n    // String literals are converted to the PDF name type\r\n    if (typeof object === 'string') {\r\n      return `/${object}`;\r\n\r\n      // String objects are converted to PDF strings (UTF-16)\r\n    } else if (object instanceof String) {\r\n      let string = object;\r\n      // Detect if this is a unicode string\r\n      let isUnicode = false;\r\n      for (let i = 0, end = string.length; i < end; i++) {\r\n        if (string.charCodeAt(i) > 0x7f) {\r\n          isUnicode = true;\r\n          break;\r\n        }\r\n      }\r\n\r\n      // If so, encode it as big endian UTF-16\r\n      let stringBuffer;\r\n      if (isUnicode) {\r\n        stringBuffer = swapBytes(Buffer.from(`\\ufeff${string}`, 'utf16le'));\r\n      } else {\r\n        stringBuffer = Buffer.from(string.valueOf(), 'ascii');\r\n      }\r\n\r\n      // Encrypt the string when necessary\r\n      if (encryptFn) {\r\n        string = encryptFn(stringBuffer).toString('binary');\r\n      } else {\r\n        string = stringBuffer.toString('binary');\r\n      }\r\n\r\n      // Escape characters as required by the spec\r\n      string = string.replace(escapableRe, c => escapable[c]);\r\n\r\n      return `(${string})`;\r\n\r\n      // Buffers are converted to PDF hex strings\r\n    } else if (Buffer.isBuffer(object)) {\r\n      return `<${object.toString('hex')}>`;\r\n    } else if (\r\n      object instanceof PDFAbstractReference ||\r\n      object instanceof PDFTree ||\r\n      object instanceof SpotColor\r\n    ) {\r\n      return object.toString();\r\n    } else if (object instanceof Date) {\r\n      let string =\r\n        `D:${pad(object.getUTCFullYear(), 4)}` +\r\n        pad(object.getUTCMonth() + 1, 2) +\r\n        pad(object.getUTCDate(), 2) +\r\n        pad(object.getUTCHours(), 2) +\r\n        pad(object.getUTCMinutes(), 2) +\r\n        pad(object.getUTCSeconds(), 2) +\r\n        'Z';\r\n\r\n      // Encrypt the string when necessary\r\n      if (encryptFn) {\r\n        string = encryptFn(Buffer.from(string, 'ascii')).toString('binary');\r\n\r\n        // Escape characters as required by the spec\r\n        string = string.replace(escapableRe, c => escapable[c]);\r\n      }\r\n\r\n      return `(${string})`;\r\n    } else if (Array.isArray(object)) {\r\n      const items = object.map(e => PDFObject.convert(e, encryptFn)).join(' ');\r\n      return `[${items}]`;\r\n    } else if ({}.toString.call(object) === '[object Object]') {\r\n      const out = ['<<'];\r\n      for (let key in object) {\r\n        const val = object[key];\r\n        out.push(`/${key} ${PDFObject.convert(val, encryptFn)}`);\r\n      }\r\n\r\n      out.push('>>');\r\n      return out.join('\\n');\r\n    } else if (typeof object === 'number') {\r\n      return PDFObject.number(object);\r\n    } else {\r\n      return `${object}`;\r\n    }\r\n  }\r\n\r\n  static number(n) {\r\n    if (n > -1e21 && n < 1e21) {\r\n      return Math.round(n * 1e6) / 1e6;\r\n    }\r\n\r\n    throw new Error(`unsupported number: ${n}`);\r\n  }\r\n}\r\n\r\nexport default PDFObject;\r\n", "/*\r\nPDFReference - represents a reference to another object in the PDF object heirarchy\r\nBy <PERSON>\r\n*/\r\n\r\nimport zlib from 'zlib';\r\nimport PDFAbstractReference from './abstract_reference';\r\nimport PDFObject from './object';\r\n\r\nclass PDFReference extends PDFAbstractReference {\r\n  constructor(document, id, data = {}) {\r\n    super();\r\n    this.document = document;\r\n    this.id = id;\r\n    this.data = data;\r\n    this.gen = 0;\r\n    this.compress = this.document.compress && !this.data.Filter;\r\n    this.uncompressedLength = 0;\r\n    this.buffer = [];\r\n  }\r\n\r\n  write(chunk) {\r\n    if (!Buffer.isBuffer(chunk)) {\r\n      chunk = Buffer.from(chunk + '\\n', 'binary');\r\n    }\r\n\r\n    this.uncompressedLength += chunk.length;\r\n    if (this.data.Length == null) {\r\n      this.data.Length = 0;\r\n    }\r\n    this.buffer.push(chunk);\r\n    this.data.Length += chunk.length;\r\n    if (this.compress) {\r\n      return (this.data.Filter = 'FlateDecode');\r\n    }\r\n  }\r\n\r\n  end(chunk) {\r\n    if (chunk) {\r\n      this.write(chunk);\r\n    }\r\n    return this.finalize();\r\n  }\r\n\r\n  finalize() {\r\n    this.offset = this.document._offset;\r\n\r\n    const encryptFn = this.document._security\r\n      ? this.document._security.getEncryptFn(this.id, this.gen)\r\n      : null;\r\n\r\n    if (this.buffer.length) {\r\n      this.buffer = Buffer.concat(this.buffer);\r\n      if (this.compress) {\r\n        this.buffer = zlib.deflateSync(this.buffer);\r\n      }\r\n\r\n      if (encryptFn) {\r\n        this.buffer = encryptFn(this.buffer);\r\n      }\r\n\r\n      this.data.Length = this.buffer.length;\r\n    }\r\n\r\n    this.document._write(`${this.id} ${this.gen} obj`);\r\n    this.document._write(PDFObject.convert(this.data, encryptFn));\r\n\r\n    if (this.buffer.length) {\r\n      this.document._write('stream');\r\n      this.document._write(this.buffer);\r\n\r\n      this.buffer = []; // free up memory\r\n      this.document._write('\\nendstream');\r\n    }\r\n\r\n    this.document._write('endobj');\r\n    this.document._refEnd(this);\r\n  }\r\n  toString() {\r\n    return `${this.id} ${this.gen} R`;\r\n  }\r\n}\r\n\r\nexport default PDFReference;\r\n", "/*\r\nPDFPage - represents a single page in the PDF document\r\nBy <PERSON>\r\n*/\r\n\r\nconst DEFAULT_MARGINS = {\r\n  top: 72,\r\n  left: 72,\r\n  bottom: 72,\r\n  right: 72\r\n};\r\n\r\nconst SIZES = {\r\n  '4A0': [4767.87, 6740.79],\r\n  '2A0': [3370.39, 4767.87],\r\n  A0: [2383.94, 3370.39],\r\n  A1: [1683.78, 2383.94],\r\n  A2: [1190.55, 1683.78],\r\n  A3: [841.89, 1190.55],\r\n  A4: [595.28, 841.89],\r\n  A5: [419.53, 595.28],\r\n  A6: [297.64, 419.53],\r\n  A7: [209.76, 297.64],\r\n  A8: [147.4, 209.76],\r\n  A9: [104.88, 147.4],\r\n  A10: [73.7, 104.88],\r\n  B0: [2834.65, 4008.19],\r\n  B1: [2004.09, 2834.65],\r\n  B2: [1417.32, 2004.09],\r\n  B3: [1000.63, 1417.32],\r\n  B4: [708.66, 1000.63],\r\n  B5: [498.9, 708.66],\r\n  B6: [354.33, 498.9],\r\n  B7: [249.45, 354.33],\r\n  B8: [175.75, 249.45],\r\n  B9: [124.72, 175.75],\r\n  B10: [87.87, 124.72],\r\n  C0: [2599.37, 3676.54],\r\n  C1: [1836.85, 2599.37],\r\n  C2: [1298.27, 1836.85],\r\n  C3: [918.43, 1298.27],\r\n  C4: [649.13, 918.43],\r\n  C5: [459.21, 649.13],\r\n  C6: [323.15, 459.21],\r\n  C7: [229.61, 323.15],\r\n  C8: [161.57, 229.61],\r\n  C9: [113.39, 161.57],\r\n  C10: [79.37, 113.39],\r\n  RA0: [2437.8, 3458.27],\r\n  RA1: [1729.13, 2437.8],\r\n  RA2: [1218.9, 1729.13],\r\n  RA3: [864.57, 1218.9],\r\n  RA4: [609.45, 864.57],\r\n  SRA0: [2551.18, 3628.35],\r\n  SRA1: [1814.17, 2551.18],\r\n  SRA2: [1275.59, 1814.17],\r\n  SRA3: [907.09, 1275.59],\r\n  SRA4: [637.8, 907.09],\r\n  EXECUTIVE: [521.86, 756.0],\r\n  FOLIO: [612.0, 936.0],\r\n  LEGAL: [612.0, 1008.0],\r\n  LETTER: [612.0, 792.0],\r\n  TABLOID: [792.0, 1224.0]\r\n};\r\n\r\nclass PDFPage {\r\n  constructor(document, options = {}) {\r\n    this.document = document;\r\n    this.size = options.size || 'letter';\r\n    this.layout = options.layout || 'portrait';\r\n\r\n    // process margins\r\n    if (typeof options.margin === 'number') {\r\n      this.margins = {\r\n        top: options.margin,\r\n        left: options.margin,\r\n        bottom: options.margin,\r\n        right: options.margin\r\n      };\r\n\r\n      // default to 1 inch margins\r\n    } else {\r\n      this.margins = options.margins || DEFAULT_MARGINS;\r\n    }\r\n\r\n    // calculate page dimensions\r\n    const dimensions = Array.isArray(this.size)\r\n      ? this.size\r\n      : SIZES[this.size.toUpperCase()];\r\n    this.width = dimensions[this.layout === 'portrait' ? 0 : 1];\r\n    this.height = dimensions[this.layout === 'portrait' ? 1 : 0];\r\n\r\n    this.content = this.document.ref();\r\n\r\n    // Initialize the Font, XObject, and ExtGState dictionaries\r\n    this.resources = this.document.ref({\r\n      ProcSet: ['PDF', 'Text', 'ImageB', 'ImageC', 'ImageI']\r\n    });\r\n\r\n    // The page dictionary\r\n    this.dictionary = this.document.ref({\r\n      Type: 'Page',\r\n      Parent: this.document._root.data.Pages,\r\n      MediaBox: [0, 0, this.width, this.height],\r\n      Contents: this.content,\r\n      Resources: this.resources\r\n    });\r\n\r\n    this.markings = [];\r\n  }\r\n\r\n  // Lazily create these objects\r\n  get fonts() {\r\n    const data = this.resources.data;\r\n    return data.Font != null ? data.Font : (data.Font = {});\r\n  }\r\n\r\n  get xobjects() {\r\n    const data = this.resources.data;\r\n    return data.XObject != null ? data.XObject : (data.XObject = {});\r\n  }\r\n\r\n  get ext_gstates() {\r\n    const data = this.resources.data;\r\n    return data.ExtGState != null ? data.ExtGState : (data.ExtGState = {});\r\n  }\r\n\r\n  get patterns() {\r\n    const data = this.resources.data;\r\n    return data.Pattern != null ? data.Pattern : (data.Pattern = {});\r\n  }\r\n\r\n  get colorSpaces() {\r\n    const data = this.resources.data;\r\n    return data.ColorSpace || (data.ColorSpace = {});\r\n  }\r\n\r\n  get annotations() {\r\n    const data = this.dictionary.data;\r\n    return data.Annots != null ? data.Annots : (data.Annots = []);\r\n  }\r\n\r\n  get structParentTreeKey() {\r\n    const data = this.dictionary.data;\r\n    return data.StructParents != null\r\n      ? data.StructParents\r\n      : (data.StructParents = this.document.createStructParentTreeNextKey());\r\n  }\r\n\r\n  maxY() {\r\n    return this.height - this.margins.bottom;\r\n  }\r\n\r\n  write(chunk) {\r\n    return this.content.write(chunk);\r\n  }\r\n\r\n  // Set tab order if document is tagged for accessibility.\r\n  _setTabOrder() {\r\n    if (!this.dictionary.Tabs && this.document.hasMarkInfoDictionary()) {\r\n      this.dictionary.data.Tabs = 'S';\r\n    }\r\n  }\r\n\r\n  end() {\r\n    this._setTabOrder();\r\n    this.dictionary.end();\r\n    this.resources.data.ColorSpace = this.resources.data.ColorSpace || {};\r\n    for (let color of Object.values(this.document.spotColors)) {\r\n      this.resources.data.ColorSpace[color.id] = color;\r\n    }\r\n    this.resources.end();\r\n    return this.content.end();\r\n  }\r\n}\r\n\r\nexport default PDFPage;\r\n", "/*\r\nPDFNameTree - represents a name tree object\r\n*/\r\n\r\nimport PDFTree from \"./tree\";\r\n\r\nclass PDFNameTree extends PDFTree {\r\n  _compareKeys(a, b) {\r\n    return a.localeCompare(b);\r\n  }\r\n\r\n  _keysName() {\r\n    return \"Names\";\r\n  }\r\n\r\n  _dataForKey(k) {\r\n    return new String(k);\r\n  }\r\n}\r\n\r\nexport default PDFNameTree;\r\n", "/**\r\n * Check if value is in a range group.\r\n * @param {number} value\r\n * @param {number[]} rangeGroup\r\n * @returns {boolean}\r\n */\r\nfunction inRange(value, rangeGroup) {\r\n  if (value < rangeGroup[0]) return false;\r\n  let startRange = 0;\r\n  let endRange = rangeGroup.length / 2;\r\n  while (startRange <= endRange) {\r\n    const middleRange = Math.floor((startRange + endRange) / 2);\r\n\r\n    // actual array index\r\n    const arrayIndex = middleRange * 2;\r\n\r\n    // Check if value is in range pointed by actual index\r\n    if (\r\n      value >= rangeGroup[arrayIndex] &&\r\n      value <= rangeGroup[arrayIndex + 1]\r\n    ) {\r\n      return true;\r\n    }\r\n\r\n    if (value > rangeGroup[arrayIndex + 1]) {\r\n      // Search Right Side Of Array\r\n      startRange = middleRange + 1;\r\n    } else {\r\n      // Search Left Side Of Array\r\n      endRange = middleRange - 1;\r\n    }\r\n  }\r\n  return false;\r\n}\r\n\r\nexport { inRange };\r\n", "import { inRange } from './util';\r\n\r\n// prettier-ignore-start\r\n/**\r\n * A.1 Unassigned code points in Unicode 3.2\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-A.1\r\n */\r\nconst unassigned_code_points = [\r\n  0x0221,\r\n  0x0221,\r\n  0x0234,\r\n  0x024f,\r\n  0x02ae,\r\n  0x02af,\r\n  0x02ef,\r\n  0x02ff,\r\n  0x0350,\r\n  0x035f,\r\n  0x0370,\r\n  0x0373,\r\n  0x0376,\r\n  0x0379,\r\n  0x037b,\r\n  0x037d,\r\n  0x037f,\r\n  0x0383,\r\n  0x038b,\r\n  0x038b,\r\n  0x038d,\r\n  0x038d,\r\n  0x03a2,\r\n  0x03a2,\r\n  0x03cf,\r\n  0x03cf,\r\n  0x03f7,\r\n  0x03ff,\r\n  0x0487,\r\n  0x0487,\r\n  0x04cf,\r\n  0x04cf,\r\n  0x04f6,\r\n  0x04f7,\r\n  0x04fa,\r\n  0x04ff,\r\n  0x0510,\r\n  0x0530,\r\n  0x0557,\r\n  0x0558,\r\n  0x0560,\r\n  0x0560,\r\n  0x0588,\r\n  0x0588,\r\n  0x058b,\r\n  0x0590,\r\n  0x05a2,\r\n  0x05a2,\r\n  0x05ba,\r\n  0x05ba,\r\n  0x05c5,\r\n  0x05cf,\r\n  0x05eb,\r\n  0x05ef,\r\n  0x05f5,\r\n  0x060b,\r\n  0x060d,\r\n  0x061a,\r\n  0x061c,\r\n  0x061e,\r\n  0x0620,\r\n  0x0620,\r\n  0x063b,\r\n  0x063f,\r\n  0x0656,\r\n  0x065f,\r\n  0x06ee,\r\n  0x06ef,\r\n  0x06ff,\r\n  0x06ff,\r\n  0x070e,\r\n  0x070e,\r\n  0x072d,\r\n  0x072f,\r\n  0x074b,\r\n  0x077f,\r\n  0x07b2,\r\n  0x0900,\r\n  0x0904,\r\n  0x0904,\r\n  0x093a,\r\n  0x093b,\r\n  0x094e,\r\n  0x094f,\r\n  0x0955,\r\n  0x0957,\r\n  0x0971,\r\n  0x0980,\r\n  0x0984,\r\n  0x0984,\r\n  0x098d,\r\n  0x098e,\r\n  0x0991,\r\n  0x0992,\r\n  0x09a9,\r\n  0x09a9,\r\n  0x09b1,\r\n  0x09b1,\r\n  0x09b3,\r\n  0x09b5,\r\n  0x09ba,\r\n  0x09bb,\r\n  0x09bd,\r\n  0x09bd,\r\n  0x09c5,\r\n  0x09c6,\r\n  0x09c9,\r\n  0x09ca,\r\n  0x09ce,\r\n  0x09d6,\r\n  0x09d8,\r\n  0x09db,\r\n  0x09de,\r\n  0x09de,\r\n  0x09e4,\r\n  0x09e5,\r\n  0x09fb,\r\n  0x0a01,\r\n  0x0a03,\r\n  0x0a04,\r\n  0x0a0b,\r\n  0x0a0e,\r\n  0x0a11,\r\n  0x0a12,\r\n  0x0a29,\r\n  0x0a29,\r\n  0x0a31,\r\n  0x0a31,\r\n  0x0a34,\r\n  0x0a34,\r\n  0x0a37,\r\n  0x0a37,\r\n  0x0a3a,\r\n  0x0a3b,\r\n  0x0a3d,\r\n  0x0a3d,\r\n  0x0a43,\r\n  0x0a46,\r\n  0x0a49,\r\n  0x0a4a,\r\n  0x0a4e,\r\n  0x0a58,\r\n  0x0a5d,\r\n  0x0a5d,\r\n  0x0a5f,\r\n  0x0a65,\r\n  0x0a75,\r\n  0x0a80,\r\n  0x0a84,\r\n  0x0a84,\r\n  0x0a8c,\r\n  0x0a8c,\r\n  0x0a8e,\r\n  0x0a8e,\r\n  0x0a92,\r\n  0x0a92,\r\n  0x0aa9,\r\n  0x0aa9,\r\n  0x0ab1,\r\n  0x0ab1,\r\n  0x0ab4,\r\n  0x0ab4,\r\n  0x0aba,\r\n  0x0abb,\r\n  0x0ac6,\r\n  0x0ac6,\r\n  0x0aca,\r\n  0x0aca,\r\n  0x0ace,\r\n  0x0acf,\r\n  0x0ad1,\r\n  0x0adf,\r\n  0x0ae1,\r\n  0x0ae5,\r\n  0x0af0,\r\n  0x0b00,\r\n  0x0b04,\r\n  0x0b04,\r\n  0x0b0d,\r\n  0x0b0e,\r\n  0x0b11,\r\n  0x0b12,\r\n  0x0b29,\r\n  0x0b29,\r\n  0x0b31,\r\n  0x0b31,\r\n  0x0b34,\r\n  0x0b35,\r\n  0x0b3a,\r\n  0x0b3b,\r\n  0x0b44,\r\n  0x0b46,\r\n  0x0b49,\r\n  0x0b4a,\r\n  0x0b4e,\r\n  0x0b55,\r\n  0x0b58,\r\n  0x0b5b,\r\n  0x0b5e,\r\n  0x0b5e,\r\n  0x0b62,\r\n  0x0b65,\r\n  0x0b71,\r\n  0x0b81,\r\n  0x0b84,\r\n  0x0b84,\r\n  0x0b8b,\r\n  0x0b8d,\r\n  0x0b91,\r\n  0x0b91,\r\n  0x0b96,\r\n  0x0b98,\r\n  0x0b9b,\r\n  0x0b9b,\r\n  0x0b9d,\r\n  0x0b9d,\r\n  0x0ba0,\r\n  0x0ba2,\r\n  0x0ba5,\r\n  0x0ba7,\r\n  0x0bab,\r\n  0x0bad,\r\n  0x0bb6,\r\n  0x0bb6,\r\n  0x0bba,\r\n  0x0bbd,\r\n  0x0bc3,\r\n  0x0bc5,\r\n  0x0bc9,\r\n  0x0bc9,\r\n  0x0bce,\r\n  0x0bd6,\r\n  0x0bd8,\r\n  0x0be6,\r\n  0x0bf3,\r\n  0x0c00,\r\n  0x0c04,\r\n  0x0c04,\r\n  0x0c0d,\r\n  0x0c0d,\r\n  0x0c11,\r\n  0x0c11,\r\n  0x0c29,\r\n  0x0c29,\r\n  0x0c34,\r\n  0x0c34,\r\n  0x0c3a,\r\n  0x0c3d,\r\n  0x0c45,\r\n  0x0c45,\r\n  0x0c49,\r\n  0x0c49,\r\n  0x0c4e,\r\n  0x0c54,\r\n  0x0c57,\r\n  0x0c5f,\r\n  0x0c62,\r\n  0x0c65,\r\n  0x0c70,\r\n  0x0c81,\r\n  0x0c84,\r\n  0x0c84,\r\n  0x0c8d,\r\n  0x0c8d,\r\n  0x0c91,\r\n  0x0c91,\r\n  0x0ca9,\r\n  0x0ca9,\r\n  0x0cb4,\r\n  0x0cb4,\r\n  0x0cba,\r\n  0x0cbd,\r\n  0x0cc5,\r\n  0x0cc5,\r\n  0x0cc9,\r\n  0x0cc9,\r\n  0x0cce,\r\n  0x0cd4,\r\n  0x0cd7,\r\n  0x0cdd,\r\n  0x0cdf,\r\n  0x0cdf,\r\n  0x0ce2,\r\n  0x0ce5,\r\n  0x0cf0,\r\n  0x0d01,\r\n  0x0d04,\r\n  0x0d04,\r\n  0x0d0d,\r\n  0x0d0d,\r\n  0x0d11,\r\n  0x0d11,\r\n  0x0d29,\r\n  0x0d29,\r\n  0x0d3a,\r\n  0x0d3d,\r\n  0x0d44,\r\n  0x0d45,\r\n  0x0d49,\r\n  0x0d49,\r\n  0x0d4e,\r\n  0x0d56,\r\n  0x0d58,\r\n  0x0d5f,\r\n  0x0d62,\r\n  0x0d65,\r\n  0x0d70,\r\n  0x0d81,\r\n  0x0d84,\r\n  0x0d84,\r\n  0x0d97,\r\n  0x0d99,\r\n  0x0db2,\r\n  0x0db2,\r\n  0x0dbc,\r\n  0x0dbc,\r\n  0x0dbe,\r\n  0x0dbf,\r\n  0x0dc7,\r\n  0x0dc9,\r\n  0x0dcb,\r\n  0x0dce,\r\n  0x0dd5,\r\n  0x0dd5,\r\n  0x0dd7,\r\n  0x0dd7,\r\n  0x0de0,\r\n  0x0df1,\r\n  0x0df5,\r\n  0x0e00,\r\n  0x0e3b,\r\n  0x0e3e,\r\n  0x0e5c,\r\n  0x0e80,\r\n  0x0e83,\r\n  0x0e83,\r\n  0x0e85,\r\n  0x0e86,\r\n  0x0e89,\r\n  0x0e89,\r\n  0x0e8b,\r\n  0x0e8c,\r\n  0x0e8e,\r\n  0x0e93,\r\n  0x0e98,\r\n  0x0e98,\r\n  0x0ea0,\r\n  0x0ea0,\r\n  0x0ea4,\r\n  0x0ea4,\r\n  0x0ea6,\r\n  0x0ea6,\r\n  0x0ea8,\r\n  0x0ea9,\r\n  0x0eac,\r\n  0x0eac,\r\n  0x0eba,\r\n  0x0eba,\r\n  0x0ebe,\r\n  0x0ebf,\r\n  0x0ec5,\r\n  0x0ec5,\r\n  0x0ec7,\r\n  0x0ec7,\r\n  0x0ece,\r\n  0x0ecf,\r\n  0x0eda,\r\n  0x0edb,\r\n  0x0ede,\r\n  0x0eff,\r\n  0x0f48,\r\n  0x0f48,\r\n  0x0f6b,\r\n  0x0f70,\r\n  0x0f8c,\r\n  0x0f8f,\r\n  0x0f98,\r\n  0x0f98,\r\n  0x0fbd,\r\n  0x0fbd,\r\n  0x0fcd,\r\n  0x0fce,\r\n  0x0fd0,\r\n  0x0fff,\r\n  0x1022,\r\n  0x1022,\r\n  0x1028,\r\n  0x1028,\r\n  0x102b,\r\n  0x102b,\r\n  0x1033,\r\n  0x1035,\r\n  0x103a,\r\n  0x103f,\r\n  0x105a,\r\n  0x109f,\r\n  0x10c6,\r\n  0x10cf,\r\n  0x10f9,\r\n  0x10fa,\r\n  0x10fc,\r\n  0x10ff,\r\n  0x115a,\r\n  0x115e,\r\n  0x11a3,\r\n  0x11a7,\r\n  0x11fa,\r\n  0x11ff,\r\n  0x1207,\r\n  0x1207,\r\n  0x1247,\r\n  0x1247,\r\n  0x1249,\r\n  0x1249,\r\n  0x124e,\r\n  0x124f,\r\n  0x1257,\r\n  0x1257,\r\n  0x1259,\r\n  0x1259,\r\n  0x125e,\r\n  0x125f,\r\n  0x1287,\r\n  0x1287,\r\n  0x1289,\r\n  0x1289,\r\n  0x128e,\r\n  0x128f,\r\n  0x12af,\r\n  0x12af,\r\n  0x12b1,\r\n  0x12b1,\r\n  0x12b6,\r\n  0x12b7,\r\n  0x12bf,\r\n  0x12bf,\r\n  0x12c1,\r\n  0x12c1,\r\n  0x12c6,\r\n  0x12c7,\r\n  0x12cf,\r\n  0x12cf,\r\n  0x12d7,\r\n  0x12d7,\r\n  0x12ef,\r\n  0x12ef,\r\n  0x130f,\r\n  0x130f,\r\n  0x1311,\r\n  0x1311,\r\n  0x1316,\r\n  0x1317,\r\n  0x131f,\r\n  0x131f,\r\n  0x1347,\r\n  0x1347,\r\n  0x135b,\r\n  0x1360,\r\n  0x137d,\r\n  0x139f,\r\n  0x13f5,\r\n  0x1400,\r\n  0x1677,\r\n  0x167f,\r\n  0x169d,\r\n  0x169f,\r\n  0x16f1,\r\n  0x16ff,\r\n  0x170d,\r\n  0x170d,\r\n  0x1715,\r\n  0x171f,\r\n  0x1737,\r\n  0x173f,\r\n  0x1754,\r\n  0x175f,\r\n  0x176d,\r\n  0x176d,\r\n  0x1771,\r\n  0x1771,\r\n  0x1774,\r\n  0x177f,\r\n  0x17dd,\r\n  0x17df,\r\n  0x17ea,\r\n  0x17ff,\r\n  0x180f,\r\n  0x180f,\r\n  0x181a,\r\n  0x181f,\r\n  0x1878,\r\n  0x187f,\r\n  0x18aa,\r\n  0x1dff,\r\n  0x1e9c,\r\n  0x1e9f,\r\n  0x1efa,\r\n  0x1eff,\r\n  0x1f16,\r\n  0x1f17,\r\n  0x1f1e,\r\n  0x1f1f,\r\n  0x1f46,\r\n  0x1f47,\r\n  0x1f4e,\r\n  0x1f4f,\r\n  0x1f58,\r\n  0x1f58,\r\n  0x1f5a,\r\n  0x1f5a,\r\n  0x1f5c,\r\n  0x1f5c,\r\n  0x1f5e,\r\n  0x1f5e,\r\n  0x1f7e,\r\n  0x1f7f,\r\n  0x1fb5,\r\n  0x1fb5,\r\n  0x1fc5,\r\n  0x1fc5,\r\n  0x1fd4,\r\n  0x1fd5,\r\n  0x1fdc,\r\n  0x1fdc,\r\n  0x1ff0,\r\n  0x1ff1,\r\n  0x1ff5,\r\n  0x1ff5,\r\n  0x1fff,\r\n  0x1fff,\r\n  0x2053,\r\n  0x2056,\r\n  0x2058,\r\n  0x205e,\r\n  0x2064,\r\n  0x2069,\r\n  0x2072,\r\n  0x2073,\r\n  0x208f,\r\n  0x209f,\r\n  0x20b2,\r\n  0x20cf,\r\n  0x20eb,\r\n  0x20ff,\r\n  0x213b,\r\n  0x213c,\r\n  0x214c,\r\n  0x2152,\r\n  0x2184,\r\n  0x218f,\r\n  0x23cf,\r\n  0x23ff,\r\n  0x2427,\r\n  0x243f,\r\n  0x244b,\r\n  0x245f,\r\n  0x24ff,\r\n  0x24ff,\r\n  0x2614,\r\n  0x2615,\r\n  0x2618,\r\n  0x2618,\r\n  0x267e,\r\n  0x267f,\r\n  0x268a,\r\n  0x2700,\r\n  0x2705,\r\n  0x2705,\r\n  0x270a,\r\n  0x270b,\r\n  0x2728,\r\n  0x2728,\r\n  0x274c,\r\n  0x274c,\r\n  0x274e,\r\n  0x274e,\r\n  0x2753,\r\n  0x2755,\r\n  0x2757,\r\n  0x2757,\r\n  0x275f,\r\n  0x2760,\r\n  0x2795,\r\n  0x2797,\r\n  0x27b0,\r\n  0x27b0,\r\n  0x27bf,\r\n  0x27cf,\r\n  0x27ec,\r\n  0x27ef,\r\n  0x2b00,\r\n  0x2e7f,\r\n  0x2e9a,\r\n  0x2e9a,\r\n  0x2ef4,\r\n  0x2eff,\r\n  0x2fd6,\r\n  0x2fef,\r\n  0x2ffc,\r\n  0x2fff,\r\n  0x3040,\r\n  0x3040,\r\n  0x3097,\r\n  0x3098,\r\n  0x3100,\r\n  0x3104,\r\n  0x312d,\r\n  0x3130,\r\n  0x318f,\r\n  0x318f,\r\n  0x31b8,\r\n  0x31ef,\r\n  0x321d,\r\n  0x321f,\r\n  0x3244,\r\n  0x3250,\r\n  0x327c,\r\n  0x327e,\r\n  0x32cc,\r\n  0x32cf,\r\n  0x32ff,\r\n  0x32ff,\r\n  0x3377,\r\n  0x337a,\r\n  0x33de,\r\n  0x33df,\r\n  0x33ff,\r\n  0x33ff,\r\n  0x4db6,\r\n  0x4dff,\r\n  0x9fa6,\r\n  0x9fff,\r\n  0xa48d,\r\n  0xa48f,\r\n  0xa4c7,\r\n  0xabff,\r\n  0xd7a4,\r\n  0xd7ff,\r\n  0xfa2e,\r\n  0xfa2f,\r\n  0xfa6b,\r\n  0xfaff,\r\n  0xfb07,\r\n  0xfb12,\r\n  0xfb18,\r\n  0xfb1c,\r\n  0xfb37,\r\n  0xfb37,\r\n  0xfb3d,\r\n  0xfb3d,\r\n  0xfb3f,\r\n  0xfb3f,\r\n  0xfb42,\r\n  0xfb42,\r\n  0xfb45,\r\n  0xfb45,\r\n  0xfbb2,\r\n  0xfbd2,\r\n  0xfd40,\r\n  0xfd4f,\r\n  0xfd90,\r\n  0xfd91,\r\n  0xfdc8,\r\n  0xfdcf,\r\n  0xfdfd,\r\n  0xfdff,\r\n  0xfe10,\r\n  0xfe1f,\r\n  0xfe24,\r\n  0xfe2f,\r\n  0xfe47,\r\n  0xfe48,\r\n  0xfe53,\r\n  0xfe53,\r\n  0xfe67,\r\n  0xfe67,\r\n  0xfe6c,\r\n  0xfe6f,\r\n  0xfe75,\r\n  0xfe75,\r\n  0xfefd,\r\n  0xfefe,\r\n  0xff00,\r\n  0xff00,\r\n  0xffbf,\r\n  0xffc1,\r\n  0xffc8,\r\n  0xffc9,\r\n  0xffd0,\r\n  0xffd1,\r\n  0xffd8,\r\n  0xffd9,\r\n  0xffdd,\r\n  0xffdf,\r\n  0xffe7,\r\n  0xffe7,\r\n  0xffef,\r\n  0xfff8,\r\n  0x10000,\r\n  0x102ff,\r\n  0x1031f,\r\n  0x1031f,\r\n  0x10324,\r\n  0x1032f,\r\n  0x1034b,\r\n  0x103ff,\r\n  0x10426,\r\n  0x10427,\r\n  0x1044e,\r\n  0x1cfff,\r\n  0x1d0f6,\r\n  0x1d0ff,\r\n  0x1d127,\r\n  0x1d129,\r\n  0x1d1de,\r\n  0x1d3ff,\r\n  0x1d455,\r\n  0x1d455,\r\n  0x1d49d,\r\n  0x1d49d,\r\n  0x1d4a0,\r\n  0x1d4a1,\r\n  0x1d4a3,\r\n  0x1d4a4,\r\n  0x1d4a7,\r\n  0x1d4a8,\r\n  0x1d4ad,\r\n  0x1d4ad,\r\n  0x1d4ba,\r\n  0x1d4ba,\r\n  0x1d4bc,\r\n  0x1d4bc,\r\n  0x1d4c1,\r\n  0x1d4c1,\r\n  0x1d4c4,\r\n  0x1d4c4,\r\n  0x1d506,\r\n  0x1d506,\r\n  0x1d50b,\r\n  0x1d50c,\r\n  0x1d515,\r\n  0x1d515,\r\n  0x1d51d,\r\n  0x1d51d,\r\n  0x1d53a,\r\n  0x1d53a,\r\n  0x1d53f,\r\n  0x1d53f,\r\n  0x1d545,\r\n  0x1d545,\r\n  0x1d547,\r\n  0x1d549,\r\n  0x1d551,\r\n  0x1d551,\r\n  0x1d6a4,\r\n  0x1d6a7,\r\n  0x1d7ca,\r\n  0x1d7cd,\r\n  0x1d800,\r\n  0x1fffd,\r\n  0x2a6d7,\r\n  0x2f7ff,\r\n  0x2fa1e,\r\n  0x2fffd,\r\n  0x30000,\r\n  0x3fffd,\r\n  0x40000,\r\n  0x4fffd,\r\n  0x50000,\r\n  0x5fffd,\r\n  0x60000,\r\n  0x6fffd,\r\n  0x70000,\r\n  0x7fffd,\r\n  0x80000,\r\n  0x8fffd,\r\n  0x90000,\r\n  0x9fffd,\r\n  0xa0000,\r\n  0xafffd,\r\n  0xb0000,\r\n  0xbfffd,\r\n  0xc0000,\r\n  0xcfffd,\r\n  0xd0000,\r\n  0xdfffd,\r\n  0xe0000,\r\n  0xe0000,\r\n  0xe0002,\r\n  0xe001f,\r\n  0xe0080,\r\n  0xefffd\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isUnassignedCodePoint = character =>\r\n  inRange(character, unassigned_code_points);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * B.1 Commonly mapped to nothing\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-B.1\r\n */\r\nconst commonly_mapped_to_nothing = [\r\n  0x00ad,\r\n  0x00ad,\r\n  0x034f,\r\n  0x034f,\r\n  0x1806,\r\n  0x1806,\r\n  0x180b,\r\n  0x180b,\r\n  0x180c,\r\n  0x180c,\r\n  0x180d,\r\n  0x180d,\r\n  0x200b,\r\n  0x200b,\r\n  0x200c,\r\n  0x200c,\r\n  0x200d,\r\n  0x200d,\r\n  0x2060,\r\n  0x2060,\r\n  0xfe00,\r\n  0xfe00,\r\n  0xfe01,\r\n  0xfe01,\r\n  0xfe02,\r\n  0xfe02,\r\n  0xfe03,\r\n  0xfe03,\r\n  0xfe04,\r\n  0xfe04,\r\n  0xfe05,\r\n  0xfe05,\r\n  0xfe06,\r\n  0xfe06,\r\n  0xfe07,\r\n  0xfe07,\r\n  0xfe08,\r\n  0xfe08,\r\n  0xfe09,\r\n  0xfe09,\r\n  0xfe0a,\r\n  0xfe0a,\r\n  0xfe0b,\r\n  0xfe0b,\r\n  0xfe0c,\r\n  0xfe0c,\r\n  0xfe0d,\r\n  0xfe0d,\r\n  0xfe0e,\r\n  0xfe0e,\r\n  0xfe0f,\r\n  0xfe0f,\r\n  0xfeff,\r\n  0xfeff\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isCommonlyMappedToNothing = character =>\r\n  inRange(character, commonly_mapped_to_nothing);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * C.1.2 Non-ASCII space characters\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-C.1.2\r\n */\r\nconst non_ASCII_space_characters = [\r\n  0x00a0,\r\n  0x00a0 /* NO-BREAK SPACE */,\r\n  0x1680,\r\n  0x1680 /* OGHAM SPACE MARK */,\r\n  0x2000,\r\n  0x2000 /* EN QUAD */,\r\n  0x2001,\r\n  0x2001 /* EM QUAD */,\r\n  0x2002,\r\n  0x2002 /* EN SPACE */,\r\n  0x2003,\r\n  0x2003 /* EM SPACE */,\r\n  0x2004,\r\n  0x2004 /* THREE-PER-EM SPACE */,\r\n  0x2005,\r\n  0x2005 /* FOUR-PER-EM SPACE */,\r\n  0x2006,\r\n  0x2006 /* SIX-PER-EM SPACE */,\r\n  0x2007,\r\n  0x2007 /* FIGURE SPACE */,\r\n  0x2008,\r\n  0x2008 /* PUNCTUATION SPACE */,\r\n  0x2009,\r\n  0x2009 /* THIN SPACE */,\r\n  0x200a,\r\n  0x200a /* HAIR SPACE */,\r\n  0x200b,\r\n  0x200b /* ZERO WIDTH SPACE */,\r\n  0x202f,\r\n  0x202f /* NARROW NO-BREAK SPACE */,\r\n  0x205f,\r\n  0x205f /* MEDIUM MATHEMATICAL SPACE */,\r\n  0x3000,\r\n  0x3000 /* IDEOGRAPHIC SPACE */\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isNonASCIISpaceCharacter = character =>\r\n  inRange(character, non_ASCII_space_characters);\r\n\r\n// prettier-ignore-start\r\nconst non_ASCII_controls_characters = [\r\n  /**\r\n   * C.2.2 Non-ASCII control characters\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.2.2\r\n   */\r\n  0x0080,\r\n  0x009f /* [CONTROL CHARACTERS] */,\r\n  0x06dd,\r\n  0x06dd /* ARABIC END OF AYAH */,\r\n  0x070f,\r\n  0x070f /* SYRIAC ABBREVIATION MARK */,\r\n  0x180e,\r\n  0x180e /* MONGOLIAN VOWEL SEPARATOR */,\r\n  0x200c,\r\n  0x200c /* ZERO WIDTH NON-JOINER */,\r\n  0x200d,\r\n  0x200d /* ZERO WIDTH JOINER */,\r\n  0x2028,\r\n  0x2028 /* LINE SEPARATOR */,\r\n  0x2029,\r\n  0x2029 /* PARAGRAPH SEPARATOR */,\r\n  0x2060,\r\n  0x2060 /* WORD JOINER */,\r\n  0x2061,\r\n  0x2061 /* FUNCTION APPLICATION */,\r\n  0x2062,\r\n  0x2062 /* INVISIBLE TIMES */,\r\n  0x2063,\r\n  0x2063 /* INVISIBLE SEPARATOR */,\r\n  0x206a,\r\n  0x206f /* [CONTROL CHARACTERS] */,\r\n  0xfeff,\r\n  0xfeff /* ZERO WIDTH NO-BREAK SPACE */,\r\n  0xfff9,\r\n  0xfffc /* [CONTROL CHARACTERS] */,\r\n  0x1d173,\r\n  0x1d17a /* [MUSICAL CONTROL CHARACTERS] */\r\n];\r\n\r\nconst non_character_codepoints = [\r\n  /**\r\n   * C.4 Non-character code points\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.4\r\n   */\r\n  0xfdd0,\r\n  0xfdef /* [NONCHARACTER CODE POINTS] */,\r\n  0xfffe,\r\n  0xffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x1fffe,\r\n  0x1ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x2fffe,\r\n  0x2ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x3fffe,\r\n  0x3ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x4fffe,\r\n  0x4ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x5fffe,\r\n  0x5ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x6fffe,\r\n  0x6ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x7fffe,\r\n  0x7ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x8fffe,\r\n  0x8ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x9fffe,\r\n  0x9ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xafffe,\r\n  0xaffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xbfffe,\r\n  0xbffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xcfffe,\r\n  0xcffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xdfffe,\r\n  0xdffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xefffe,\r\n  0xeffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x10fffe,\r\n  0x10ffff /* [NONCHARACTER CODE POINTS] */\r\n];\r\n\r\n/**\r\n * 2.3.  Prohibited Output\r\n */\r\nconst prohibited_characters = [\r\n  /**\r\n   * C.2.1 ASCII control characters\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.2.1\r\n   */\r\n  0,\r\n  0x001f /* [CONTROL CHARACTERS] */,\r\n  0x007f,\r\n  0x007f /* DELETE */,\r\n\r\n  /**\r\n   * C.8 Change display properties or are deprecated\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.8\r\n   */\r\n  0x0340,\r\n  0x0340 /* COMBINING GRAVE TONE MARK */,\r\n  0x0341,\r\n  0x0341 /* COMBINING ACUTE TONE MARK */,\r\n  0x200e,\r\n  0x200e /* LEFT-TO-RIGHT MARK */,\r\n  0x200f,\r\n  0x200f /* RIGHT-TO-LEFT MARK */,\r\n  0x202a,\r\n  0x202a /* LEFT-TO-RIGHT EMBEDDING */,\r\n  0x202b,\r\n  0x202b /* RIGHT-TO-LEFT EMBEDDING */,\r\n  0x202c,\r\n  0x202c /* POP DIRECTIONAL FORMATTING */,\r\n  0x202d,\r\n  0x202d /* LEFT-TO-RIGHT OVERRIDE */,\r\n  0x202e,\r\n  0x202e /* RIGHT-TO-LEFT OVERRIDE */,\r\n  0x206a,\r\n  0x206a /* INHIBIT SYMMETRIC SWAPPING */,\r\n  0x206b,\r\n  0x206b /* ACTIVATE SYMMETRIC SWAPPING */,\r\n  0x206c,\r\n  0x206c /* INHIBIT ARABIC FORM SHAPING */,\r\n  0x206d,\r\n  0x206d /* ACTIVATE ARABIC FORM SHAPING */,\r\n  0x206e,\r\n  0x206e /* NATIONAL DIGIT SHAPES */,\r\n  0x206f,\r\n  0x206f /* NOMINAL DIGIT SHAPES */,\r\n\r\n  /**\r\n   * C.7 Inappropriate for canonical representation\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.7\r\n   */\r\n  0x2ff0,\r\n  0x2ffb /* [IDEOGRAPHIC DESCRIPTION CHARACTERS] */,\r\n\r\n  /**\r\n   * C.5 Surrogate codes\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.5\r\n   */\r\n  0xd800,\r\n  0xdfff,\r\n\r\n  /**\r\n   * C.3 Private use\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.3\r\n   */\r\n  0xe000,\r\n  0xf8ff /* [PRIVATE USE, PLANE 0] */,\r\n\r\n  /**\r\n   * C.6 Inappropriate for plain text\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.6\r\n   */\r\n  0xfff9,\r\n  0xfff9 /* INTERLINEAR ANNOTATION ANCHOR */,\r\n  0xfffa,\r\n  0xfffa /* INTERLINEAR ANNOTATION SEPARATOR */,\r\n  0xfffb,\r\n  0xfffb /* INTERLINEAR ANNOTATION TERMINATOR */,\r\n  0xfffc,\r\n  0xfffc /* OBJECT REPLACEMENT CHARACTER */,\r\n  0xfffd,\r\n  0xfffd /* REPLACEMENT CHARACTER */,\r\n\r\n  /**\r\n   * C.9 Tagging characters\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.9\r\n   */\r\n  0xe0001,\r\n  0xe0001 /* LANGUAGE TAG */,\r\n  0xe0020,\r\n  0xe007f /* [TAGGING CHARACTERS] */,\r\n\r\n  /**\r\n   * C.3 Private use\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.3\r\n   */\r\n\r\n  0xf0000,\r\n  0xffffd /* [PRIVATE USE, PLANE 15] */,\r\n  0x100000,\r\n  0x10fffd /* [PRIVATE USE, PLANE 16] */\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isProhibitedCharacter = character =>\r\n  inRange(character, non_ASCII_space_characters) ||\r\n  inRange(character, prohibited_characters) ||\r\n  inRange(character, non_ASCII_controls_characters) ||\r\n  inRange(character, non_character_codepoints);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * D.1 Characters with bidirectional property \"R\" or \"AL\"\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-D.1\r\n */\r\nconst bidirectional_r_al = [\r\n  0x05be,\r\n  0x05be,\r\n  0x05c0,\r\n  0x05c0,\r\n  0x05c3,\r\n  0x05c3,\r\n  0x05d0,\r\n  0x05ea,\r\n  0x05f0,\r\n  0x05f4,\r\n  0x061b,\r\n  0x061b,\r\n  0x061f,\r\n  0x061f,\r\n  0x0621,\r\n  0x063a,\r\n  0x0640,\r\n  0x064a,\r\n  0x066d,\r\n  0x066f,\r\n  0x0671,\r\n  0x06d5,\r\n  0x06dd,\r\n  0x06dd,\r\n  0x06e5,\r\n  0x06e6,\r\n  0x06fa,\r\n  0x06fe,\r\n  0x0700,\r\n  0x070d,\r\n  0x0710,\r\n  0x0710,\r\n  0x0712,\r\n  0x072c,\r\n  0x0780,\r\n  0x07a5,\r\n  0x07b1,\r\n  0x07b1,\r\n  0x200f,\r\n  0x200f,\r\n  0xfb1d,\r\n  0xfb1d,\r\n  0xfb1f,\r\n  0xfb28,\r\n  0xfb2a,\r\n  0xfb36,\r\n  0xfb38,\r\n  0xfb3c,\r\n  0xfb3e,\r\n  0xfb3e,\r\n  0xfb40,\r\n  0xfb41,\r\n  0xfb43,\r\n  0xfb44,\r\n  0xfb46,\r\n  0xfbb1,\r\n  0xfbd3,\r\n  0xfd3d,\r\n  0xfd50,\r\n  0xfd8f,\r\n  0xfd92,\r\n  0xfdc7,\r\n  0xfdf0,\r\n  0xfdfc,\r\n  0xfe70,\r\n  0xfe74,\r\n  0xfe76,\r\n  0xfefc\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isBidirectionalRAL = character => inRange(character, bidirectional_r_al);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * D.2 Characters with bidirectional property \"L\"\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-D.2\r\n */\r\nconst bidirectional_l = [\r\n  0x0041,\r\n  0x005a,\r\n  0x0061,\r\n  0x007a,\r\n  0x00aa,\r\n  0x00aa,\r\n  0x00b5,\r\n  0x00b5,\r\n  0x00ba,\r\n  0x00ba,\r\n  0x00c0,\r\n  0x00d6,\r\n  0x00d8,\r\n  0x00f6,\r\n  0x00f8,\r\n  0x0220,\r\n  0x0222,\r\n  0x0233,\r\n  0x0250,\r\n  0x02ad,\r\n  0x02b0,\r\n  0x02b8,\r\n  0x02bb,\r\n  0x02c1,\r\n  0x02d0,\r\n  0x02d1,\r\n  0x02e0,\r\n  0x02e4,\r\n  0x02ee,\r\n  0x02ee,\r\n  0x037a,\r\n  0x037a,\r\n  0x0386,\r\n  0x0386,\r\n  0x0388,\r\n  0x038a,\r\n  0x038c,\r\n  0x038c,\r\n  0x038e,\r\n  0x03a1,\r\n  0x03a3,\r\n  0x03ce,\r\n  0x03d0,\r\n  0x03f5,\r\n  0x0400,\r\n  0x0482,\r\n  0x048a,\r\n  0x04ce,\r\n  0x04d0,\r\n  0x04f5,\r\n  0x04f8,\r\n  0x04f9,\r\n  0x0500,\r\n  0x050f,\r\n  0x0531,\r\n  0x0556,\r\n  0x0559,\r\n  0x055f,\r\n  0x0561,\r\n  0x0587,\r\n  0x0589,\r\n  0x0589,\r\n  0x0903,\r\n  0x0903,\r\n  0x0905,\r\n  0x0939,\r\n  0x093d,\r\n  0x0940,\r\n  0x0949,\r\n  0x094c,\r\n  0x0950,\r\n  0x0950,\r\n  0x0958,\r\n  0x0961,\r\n  0x0964,\r\n  0x0970,\r\n  0x0982,\r\n  0x0983,\r\n  0x0985,\r\n  0x098c,\r\n  0x098f,\r\n  0x0990,\r\n  0x0993,\r\n  0x09a8,\r\n  0x09aa,\r\n  0x09b0,\r\n  0x09b2,\r\n  0x09b2,\r\n  0x09b6,\r\n  0x09b9,\r\n  0x09be,\r\n  0x09c0,\r\n  0x09c7,\r\n  0x09c8,\r\n  0x09cb,\r\n  0x09cc,\r\n  0x09d7,\r\n  0x09d7,\r\n  0x09dc,\r\n  0x09dd,\r\n  0x09df,\r\n  0x09e1,\r\n  0x09e6,\r\n  0x09f1,\r\n  0x09f4,\r\n  0x09fa,\r\n  0x0a05,\r\n  0x0a0a,\r\n  0x0a0f,\r\n  0x0a10,\r\n  0x0a13,\r\n  0x0a28,\r\n  0x0a2a,\r\n  0x0a30,\r\n  0x0a32,\r\n  0x0a33,\r\n  0x0a35,\r\n  0x0a36,\r\n  0x0a38,\r\n  0x0a39,\r\n  0x0a3e,\r\n  0x0a40,\r\n  0x0a59,\r\n  0x0a5c,\r\n  0x0a5e,\r\n  0x0a5e,\r\n  0x0a66,\r\n  0x0a6f,\r\n  0x0a72,\r\n  0x0a74,\r\n  0x0a83,\r\n  0x0a83,\r\n  0x0a85,\r\n  0x0a8b,\r\n  0x0a8d,\r\n  0x0a8d,\r\n  0x0a8f,\r\n  0x0a91,\r\n  0x0a93,\r\n  0x0aa8,\r\n  0x0aaa,\r\n  0x0ab0,\r\n  0x0ab2,\r\n  0x0ab3,\r\n  0x0ab5,\r\n  0x0ab9,\r\n  0x0abd,\r\n  0x0ac0,\r\n  0x0ac9,\r\n  0x0ac9,\r\n  0x0acb,\r\n  0x0acc,\r\n  0x0ad0,\r\n  0x0ad0,\r\n  0x0ae0,\r\n  0x0ae0,\r\n  0x0ae6,\r\n  0x0aef,\r\n  0x0b02,\r\n  0x0b03,\r\n  0x0b05,\r\n  0x0b0c,\r\n  0x0b0f,\r\n  0x0b10,\r\n  0x0b13,\r\n  0x0b28,\r\n  0x0b2a,\r\n  0x0b30,\r\n  0x0b32,\r\n  0x0b33,\r\n  0x0b36,\r\n  0x0b39,\r\n  0x0b3d,\r\n  0x0b3e,\r\n  0x0b40,\r\n  0x0b40,\r\n  0x0b47,\r\n  0x0b48,\r\n  0x0b4b,\r\n  0x0b4c,\r\n  0x0b57,\r\n  0x0b57,\r\n  0x0b5c,\r\n  0x0b5d,\r\n  0x0b5f,\r\n  0x0b61,\r\n  0x0b66,\r\n  0x0b70,\r\n  0x0b83,\r\n  0x0b83,\r\n  0x0b85,\r\n  0x0b8a,\r\n  0x0b8e,\r\n  0x0b90,\r\n  0x0b92,\r\n  0x0b95,\r\n  0x0b99,\r\n  0x0b9a,\r\n  0x0b9c,\r\n  0x0b9c,\r\n  0x0b9e,\r\n  0x0b9f,\r\n  0x0ba3,\r\n  0x0ba4,\r\n  0x0ba8,\r\n  0x0baa,\r\n  0x0bae,\r\n  0x0bb5,\r\n  0x0bb7,\r\n  0x0bb9,\r\n  0x0bbe,\r\n  0x0bbf,\r\n  0x0bc1,\r\n  0x0bc2,\r\n  0x0bc6,\r\n  0x0bc8,\r\n  0x0bca,\r\n  0x0bcc,\r\n  0x0bd7,\r\n  0x0bd7,\r\n  0x0be7,\r\n  0x0bf2,\r\n  0x0c01,\r\n  0x0c03,\r\n  0x0c05,\r\n  0x0c0c,\r\n  0x0c0e,\r\n  0x0c10,\r\n  0x0c12,\r\n  0x0c28,\r\n  0x0c2a,\r\n  0x0c33,\r\n  0x0c35,\r\n  0x0c39,\r\n  0x0c41,\r\n  0x0c44,\r\n  0x0c60,\r\n  0x0c61,\r\n  0x0c66,\r\n  0x0c6f,\r\n  0x0c82,\r\n  0x0c83,\r\n  0x0c85,\r\n  0x0c8c,\r\n  0x0c8e,\r\n  0x0c90,\r\n  0x0c92,\r\n  0x0ca8,\r\n  0x0caa,\r\n  0x0cb3,\r\n  0x0cb5,\r\n  0x0cb9,\r\n  0x0cbe,\r\n  0x0cbe,\r\n  0x0cc0,\r\n  0x0cc4,\r\n  0x0cc7,\r\n  0x0cc8,\r\n  0x0cca,\r\n  0x0ccb,\r\n  0x0cd5,\r\n  0x0cd6,\r\n  0x0cde,\r\n  0x0cde,\r\n  0x0ce0,\r\n  0x0ce1,\r\n  0x0ce6,\r\n  0x0cef,\r\n  0x0d02,\r\n  0x0d03,\r\n  0x0d05,\r\n  0x0d0c,\r\n  0x0d0e,\r\n  0x0d10,\r\n  0x0d12,\r\n  0x0d28,\r\n  0x0d2a,\r\n  0x0d39,\r\n  0x0d3e,\r\n  0x0d40,\r\n  0x0d46,\r\n  0x0d48,\r\n  0x0d4a,\r\n  0x0d4c,\r\n  0x0d57,\r\n  0x0d57,\r\n  0x0d60,\r\n  0x0d61,\r\n  0x0d66,\r\n  0x0d6f,\r\n  0x0d82,\r\n  0x0d83,\r\n  0x0d85,\r\n  0x0d96,\r\n  0x0d9a,\r\n  0x0db1,\r\n  0x0db3,\r\n  0x0dbb,\r\n  0x0dbd,\r\n  0x0dbd,\r\n  0x0dc0,\r\n  0x0dc6,\r\n  0x0dcf,\r\n  0x0dd1,\r\n  0x0dd8,\r\n  0x0ddf,\r\n  0x0df2,\r\n  0x0df4,\r\n  0x0e01,\r\n  0x0e30,\r\n  0x0e32,\r\n  0x0e33,\r\n  0x0e40,\r\n  0x0e46,\r\n  0x0e4f,\r\n  0x0e5b,\r\n  0x0e81,\r\n  0x0e82,\r\n  0x0e84,\r\n  0x0e84,\r\n  0x0e87,\r\n  0x0e88,\r\n  0x0e8a,\r\n  0x0e8a,\r\n  0x0e8d,\r\n  0x0e8d,\r\n  0x0e94,\r\n  0x0e97,\r\n  0x0e99,\r\n  0x0e9f,\r\n  0x0ea1,\r\n  0x0ea3,\r\n  0x0ea5,\r\n  0x0ea5,\r\n  0x0ea7,\r\n  0x0ea7,\r\n  0x0eaa,\r\n  0x0eab,\r\n  0x0ead,\r\n  0x0eb0,\r\n  0x0eb2,\r\n  0x0eb3,\r\n  0x0ebd,\r\n  0x0ebd,\r\n  0x0ec0,\r\n  0x0ec4,\r\n  0x0ec6,\r\n  0x0ec6,\r\n  0x0ed0,\r\n  0x0ed9,\r\n  0x0edc,\r\n  0x0edd,\r\n  0x0f00,\r\n  0x0f17,\r\n  0x0f1a,\r\n  0x0f34,\r\n  0x0f36,\r\n  0x0f36,\r\n  0x0f38,\r\n  0x0f38,\r\n  0x0f3e,\r\n  0x0f47,\r\n  0x0f49,\r\n  0x0f6a,\r\n  0x0f7f,\r\n  0x0f7f,\r\n  0x0f85,\r\n  0x0f85,\r\n  0x0f88,\r\n  0x0f8b,\r\n  0x0fbe,\r\n  0x0fc5,\r\n  0x0fc7,\r\n  0x0fcc,\r\n  0x0fcf,\r\n  0x0fcf,\r\n  0x1000,\r\n  0x1021,\r\n  0x1023,\r\n  0x1027,\r\n  0x1029,\r\n  0x102a,\r\n  0x102c,\r\n  0x102c,\r\n  0x1031,\r\n  0x1031,\r\n  0x1038,\r\n  0x1038,\r\n  0x1040,\r\n  0x1057,\r\n  0x10a0,\r\n  0x10c5,\r\n  0x10d0,\r\n  0x10f8,\r\n  0x10fb,\r\n  0x10fb,\r\n  0x1100,\r\n  0x1159,\r\n  0x115f,\r\n  0x11a2,\r\n  0x11a8,\r\n  0x11f9,\r\n  0x1200,\r\n  0x1206,\r\n  0x1208,\r\n  0x1246,\r\n  0x1248,\r\n  0x1248,\r\n  0x124a,\r\n  0x124d,\r\n  0x1250,\r\n  0x1256,\r\n  0x1258,\r\n  0x1258,\r\n  0x125a,\r\n  0x125d,\r\n  0x1260,\r\n  0x1286,\r\n  0x1288,\r\n  0x1288,\r\n  0x128a,\r\n  0x128d,\r\n  0x1290,\r\n  0x12ae,\r\n  0x12b0,\r\n  0x12b0,\r\n  0x12b2,\r\n  0x12b5,\r\n  0x12b8,\r\n  0x12be,\r\n  0x12c0,\r\n  0x12c0,\r\n  0x12c2,\r\n  0x12c5,\r\n  0x12c8,\r\n  0x12ce,\r\n  0x12d0,\r\n  0x12d6,\r\n  0x12d8,\r\n  0x12ee,\r\n  0x12f0,\r\n  0x130e,\r\n  0x1310,\r\n  0x1310,\r\n  0x1312,\r\n  0x1315,\r\n  0x1318,\r\n  0x131e,\r\n  0x1320,\r\n  0x1346,\r\n  0x1348,\r\n  0x135a,\r\n  0x1361,\r\n  0x137c,\r\n  0x13a0,\r\n  0x13f4,\r\n  0x1401,\r\n  0x1676,\r\n  0x1681,\r\n  0x169a,\r\n  0x16a0,\r\n  0x16f0,\r\n  0x1700,\r\n  0x170c,\r\n  0x170e,\r\n  0x1711,\r\n  0x1720,\r\n  0x1731,\r\n  0x1735,\r\n  0x1736,\r\n  0x1740,\r\n  0x1751,\r\n  0x1760,\r\n  0x176c,\r\n  0x176e,\r\n  0x1770,\r\n  0x1780,\r\n  0x17b6,\r\n  0x17be,\r\n  0x17c5,\r\n  0x17c7,\r\n  0x17c8,\r\n  0x17d4,\r\n  0x17da,\r\n  0x17dc,\r\n  0x17dc,\r\n  0x17e0,\r\n  0x17e9,\r\n  0x1810,\r\n  0x1819,\r\n  0x1820,\r\n  0x1877,\r\n  0x1880,\r\n  0x18a8,\r\n  0x1e00,\r\n  0x1e9b,\r\n  0x1ea0,\r\n  0x1ef9,\r\n  0x1f00,\r\n  0x1f15,\r\n  0x1f18,\r\n  0x1f1d,\r\n  0x1f20,\r\n  0x1f45,\r\n  0x1f48,\r\n  0x1f4d,\r\n  0x1f50,\r\n  0x1f57,\r\n  0x1f59,\r\n  0x1f59,\r\n  0x1f5b,\r\n  0x1f5b,\r\n  0x1f5d,\r\n  0x1f5d,\r\n  0x1f5f,\r\n  0x1f7d,\r\n  0x1f80,\r\n  0x1fb4,\r\n  0x1fb6,\r\n  0x1fbc,\r\n  0x1fbe,\r\n  0x1fbe,\r\n  0x1fc2,\r\n  0x1fc4,\r\n  0x1fc6,\r\n  0x1fcc,\r\n  0x1fd0,\r\n  0x1fd3,\r\n  0x1fd6,\r\n  0x1fdb,\r\n  0x1fe0,\r\n  0x1fec,\r\n  0x1ff2,\r\n  0x1ff4,\r\n  0x1ff6,\r\n  0x1ffc,\r\n  0x200e,\r\n  0x200e,\r\n  0x2071,\r\n  0x2071,\r\n  0x207f,\r\n  0x207f,\r\n  0x2102,\r\n  0x2102,\r\n  0x2107,\r\n  0x2107,\r\n  0x210a,\r\n  0x2113,\r\n  0x2115,\r\n  0x2115,\r\n  0x2119,\r\n  0x211d,\r\n  0x2124,\r\n  0x2124,\r\n  0x2126,\r\n  0x2126,\r\n  0x2128,\r\n  0x2128,\r\n  0x212a,\r\n  0x212d,\r\n  0x212f,\r\n  0x2131,\r\n  0x2133,\r\n  0x2139,\r\n  0x213d,\r\n  0x213f,\r\n  0x2145,\r\n  0x2149,\r\n  0x2160,\r\n  0x2183,\r\n  0x2336,\r\n  0x237a,\r\n  0x2395,\r\n  0x2395,\r\n  0x249c,\r\n  0x24e9,\r\n  0x3005,\r\n  0x3007,\r\n  0x3021,\r\n  0x3029,\r\n  0x3031,\r\n  0x3035,\r\n  0x3038,\r\n  0x303c,\r\n  0x3041,\r\n  0x3096,\r\n  0x309d,\r\n  0x309f,\r\n  0x30a1,\r\n  0x30fa,\r\n  0x30fc,\r\n  0x30ff,\r\n  0x3105,\r\n  0x312c,\r\n  0x3131,\r\n  0x318e,\r\n  0x3190,\r\n  0x31b7,\r\n  0x31f0,\r\n  0x321c,\r\n  0x3220,\r\n  0x3243,\r\n  0x3260,\r\n  0x327b,\r\n  0x327f,\r\n  0x32b0,\r\n  0x32c0,\r\n  0x32cb,\r\n  0x32d0,\r\n  0x32fe,\r\n  0x3300,\r\n  0x3376,\r\n  0x337b,\r\n  0x33dd,\r\n  0x33e0,\r\n  0x33fe,\r\n  0x3400,\r\n  0x4db5,\r\n  0x4e00,\r\n  0x9fa5,\r\n  0xa000,\r\n  0xa48c,\r\n  0xac00,\r\n  0xd7a3,\r\n  0xd800,\r\n  0xfa2d,\r\n  0xfa30,\r\n  0xfa6a,\r\n  0xfb00,\r\n  0xfb06,\r\n  0xfb13,\r\n  0xfb17,\r\n  0xff21,\r\n  0xff3a,\r\n  0xff41,\r\n  0xff5a,\r\n  0xff66,\r\n  0xffbe,\r\n  0xffc2,\r\n  0xffc7,\r\n  0xffca,\r\n  0xffcf,\r\n  0xffd2,\r\n  0xffd7,\r\n  0xffda,\r\n  0xffdc,\r\n  0x10300,\r\n  0x1031e,\r\n  0x10320,\r\n  0x10323,\r\n  0x10330,\r\n  0x1034a,\r\n  0x10400,\r\n  0x10425,\r\n  0x10428,\r\n  0x1044d,\r\n  0x1d000,\r\n  0x1d0f5,\r\n  0x1d100,\r\n  0x1d126,\r\n  0x1d12a,\r\n  0x1d166,\r\n  0x1d16a,\r\n  0x1d172,\r\n  0x1d183,\r\n  0x1d184,\r\n  0x1d18c,\r\n  0x1d1a9,\r\n  0x1d1ae,\r\n  0x1d1dd,\r\n  0x1d400,\r\n  0x1d454,\r\n  0x1d456,\r\n  0x1d49c,\r\n  0x1d49e,\r\n  0x1d49f,\r\n  0x1d4a2,\r\n  0x1d4a2,\r\n  0x1d4a5,\r\n  0x1d4a6,\r\n  0x1d4a9,\r\n  0x1d4ac,\r\n  0x1d4ae,\r\n  0x1d4b9,\r\n  0x1d4bb,\r\n  0x1d4bb,\r\n  0x1d4bd,\r\n  0x1d4c0,\r\n  0x1d4c2,\r\n  0x1d4c3,\r\n  0x1d4c5,\r\n  0x1d505,\r\n  0x1d507,\r\n  0x1d50a,\r\n  0x1d50d,\r\n  0x1d514,\r\n  0x1d516,\r\n  0x1d51c,\r\n  0x1d51e,\r\n  0x1d539,\r\n  0x1d53b,\r\n  0x1d53e,\r\n  0x1d540,\r\n  0x1d544,\r\n  0x1d546,\r\n  0x1d546,\r\n  0x1d54a,\r\n  0x1d550,\r\n  0x1d552,\r\n  0x1d6a3,\r\n  0x1d6a8,\r\n  0x1d7c9,\r\n  0x20000,\r\n  0x2a6d6,\r\n  0x2f800,\r\n  0x2fa1d,\r\n  0xf0000,\r\n  0xffffd,\r\n  0x100000,\r\n  0x10fffd\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isBidirectionalL = character => inRange(character, bidirectional_l);\r\n\r\nexport {\r\n  isUnassignedCodePoint,\r\n  isCommonlyMappedToNothing,\r\n  isNonASCIISpaceCharacter,\r\n  isProhibitedCharacter,\r\n  isBidirectionalRAL,\r\n  isBidirectionalL\r\n};\r\n", "import {\r\n  isUnassignedCodePoint,\r\n  isC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  isNonASCI<PERSON><PERSON><PERSON>haracter,\r\n  isProhibited<PERSON>haracter,\r\n  isBidirectionalRAL,\r\n  isBidirectionalL\r\n} from './lib/code-points';\r\n\r\n// 2.1.  Mapping\r\n\r\n/**\r\n * non-ASCII space characters [StringPrep, C.1.2] that can be\r\n * mapped to SPACE (U+0020)\r\n */\r\nconst mapping2space = isNonASCIISpaceCharacter;\r\n\r\n/**\r\n * the \"commonly mapped to nothing\" characters [StringPrep, B.1]\r\n * that can be mapped to nothing.\r\n */\r\nconst mapping2nothing = isCommonlyMappedToNothing;\r\n\r\n// utils\r\nconst getCodePoint = character => character.codePointAt(0);\r\nconst first = x => x[0];\r\nconst last = x => x[x.length - 1];\r\n\r\n/**\r\n * Convert provided string into an array of Unicode Code Points.\r\n * Based on https://stackoverflow.com/a/21409165/1556249\r\n * and https://www.npmjs.com/package/code-point-at.\r\n * @param {string} input\r\n * @returns {number[]}\r\n */\r\nfunction toCodePoints(input) {\r\n  const codepoints = [];\r\n  const size = input.length;\r\n\r\n  for (let i = 0; i < size; i += 1) {\r\n    const before = input.charCodeAt(i);\r\n\r\n    if (before >= 0xd800 && before <= 0xdbff && size > i + 1) {\r\n      const next = input.charCodeAt(i + 1);\r\n\r\n      if (next >= 0xdc00 && next <= 0xdfff) {\r\n        codepoints.push((before - 0xd800) * 0x400 + next - 0xdc00 + 0x10000);\r\n        i += 1;\r\n        continue;\r\n      }\r\n    }\r\n\r\n    codepoints.push(before);\r\n  }\r\n\r\n  return codepoints;\r\n}\r\n\r\n/**\r\n * SASLprep.\r\n * @param {string} input\r\n * @param {Object} opts\r\n * @param {boolean} opts.allowUnassigned\r\n * @returns {string}\r\n */\r\nfunction saslprep(input, opts = {}) {\r\n  if (typeof input !== 'string') {\r\n    throw new TypeError('Expected string.');\r\n  }\r\n\r\n  if (input.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  // 1. Map\r\n  const mapped_input = toCodePoints(input)\r\n    // 1.1 mapping to space\r\n    .map(character => (mapping2space(character) ? 0x20 : character))\r\n    // 1.2 mapping to nothing\r\n    .filter(character => !mapping2nothing(character));\r\n\r\n  // 2. Normalize\r\n  const normalized_input = String.fromCodePoint\r\n    .apply(null, mapped_input)\r\n    .normalize('NFKC');\r\n\r\n  const normalized_map = toCodePoints(normalized_input);\r\n\r\n  // 3. Prohibit\r\n  const hasProhibited = normalized_map.some(isProhibitedCharacter);\r\n\r\n  if (hasProhibited) {\r\n    throw new Error(\r\n      'Prohibited character, see https://tools.ietf.org/html/rfc4013#section-2.3'\r\n    );\r\n  }\r\n\r\n  // Unassigned Code Points\r\n  if (opts.allowUnassigned !== true) {\r\n    const hasUnassigned = normalized_map.some(isUnassignedCodePoint);\r\n\r\n    if (hasUnassigned) {\r\n      throw new Error(\r\n        'Unassigned code point, see https://tools.ietf.org/html/rfc4013#section-2.5'\r\n      );\r\n    }\r\n  }\r\n\r\n  // 4. check bidi\r\n\r\n  const hasBidiRAL = normalized_map.some(isBidirectionalRAL);\r\n\r\n  const hasBidiL = normalized_map.some(isBidirectionalL);\r\n\r\n  // 4.1 If a string contains any RandALCat character, the string MUST NOT\r\n  // contain any LCat character.\r\n  if (hasBidiRAL && hasBidiL) {\r\n    throw new Error(\r\n      'String must not contain RandALCat and LCat at the same time,' +\r\n        ' see https://tools.ietf.org/html/rfc3454#section-6'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * 4.2 If a string contains any RandALCat character, a RandALCat\r\n   * character MUST be the first character of the string, and a\r\n   * RandALCat character MUST be the last character of the string.\r\n   */\r\n\r\n  const isFirstBidiRAL = isBidirectionalRAL(\r\n    getCodePoint(first(normalized_input))\r\n  );\r\n  const isLastBidiRAL = isBidirectionalRAL(\r\n    getCodePoint(last(normalized_input))\r\n  );\r\n\r\n  if (hasBidiRAL && !(isFirstBidiRAL && isLastBidiRAL)) {\r\n    throw new Error(\r\n      'Bidirectional RandALCat character must be the first and the last' +\r\n        ' character of the string, see https://tools.ietf.org/html/rfc3454#section-6'\r\n    );\r\n  }\r\n\r\n  return normalized_input;\r\n}\r\n\r\nexport default saslprep;\r\n", "/*\r\n   PDFSecurity - represents PDF security settings\r\n   By <PERSON> <<EMAIL>>\r\n */\r\n\r\nimport CryptoJS from 'crypto-js';\r\nimport saslprep from './saslprep/index';\r\n\r\nclass PDFSecurity {\r\n  static generateFileID(info = {}) {\r\n    let infoStr = `${info.CreationDate.getTime()}\\n`;\r\n\r\n    for (let key in info) {\r\n      // eslint-disable-next-line no-prototype-builtins\r\n      if (!info.hasOwnProperty(key)) {\r\n        continue;\r\n      }\r\n      infoStr += `${key}: ${info[key].valueOf()}\\n`;\r\n    }\r\n\r\n    return wordArrayToBuffer(CryptoJS.MD5(infoStr));\r\n  }\r\n\r\n  static generateRandomWordArray(bytes) {\r\n    return CryptoJS.lib.WordArray.random(bytes);\r\n  }\r\n\r\n  static create(document, options = {}) {\r\n    if (!options.ownerPassword && !options.userPassword) {\r\n      return null;\r\n    }\r\n    return new PDFSecurity(document, options);\r\n  }\r\n\r\n  constructor(document, options = {}) {\r\n    if (!options.ownerPassword && !options.userPassword) {\r\n      throw new Error('None of owner password and user password is defined.');\r\n    }\r\n\r\n    this.document = document;\r\n    this._setupEncryption(options);\r\n  }\r\n\r\n  _setupEncryption(options) {\r\n    switch (options.pdfVersion) {\r\n      case '1.4':\r\n      case '1.5':\r\n        this.version = 2;\r\n        break;\r\n      case '1.6':\r\n      case '1.7':\r\n        this.version = 4;\r\n        break;\r\n      case '1.7ext3':\r\n        this.version = 5;\r\n        break;\r\n      default:\r\n        this.version = 1;\r\n        break;\r\n    }\r\n\r\n    const encDict = {\r\n      Filter: 'Standard'\r\n    };\r\n\r\n    switch (this.version) {\r\n      case 1:\r\n      case 2:\r\n      case 4:\r\n        this._setupEncryptionV1V2V4(this.version, encDict, options);\r\n        break;\r\n      case 5:\r\n        this._setupEncryptionV5(encDict, options);\r\n        break;\r\n    }\r\n\r\n    this.dictionary = this.document.ref(encDict);\r\n  }\r\n\r\n  _setupEncryptionV1V2V4(v, encDict, options) {\r\n    let r, permissions;\r\n    switch (v) {\r\n      case 1:\r\n        r = 2;\r\n        this.keyBits = 40;\r\n        permissions = getPermissionsR2(options.permissions);\r\n        break;\r\n      case 2:\r\n        r = 3;\r\n        this.keyBits = 128;\r\n        permissions = getPermissionsR3(options.permissions);\r\n        break;\r\n      case 4:\r\n        r = 4;\r\n        this.keyBits = 128;\r\n        permissions = getPermissionsR3(options.permissions);\r\n        break;\r\n    }\r\n\r\n    const paddedUserPassword = processPasswordR2R3R4(options.userPassword);\r\n    const paddedOwnerPassword = options.ownerPassword\r\n      ? processPasswordR2R3R4(options.ownerPassword)\r\n      : paddedUserPassword;\r\n\r\n    const ownerPasswordEntry = getOwnerPasswordR2R3R4(\r\n      r,\r\n      this.keyBits,\r\n      paddedUserPassword,\r\n      paddedOwnerPassword\r\n    );\r\n    this.encryptionKey = getEncryptionKeyR2R3R4(\r\n      r,\r\n      this.keyBits,\r\n      this.document._id,\r\n      paddedUserPassword,\r\n      ownerPasswordEntry,\r\n      permissions\r\n    );\r\n    let userPasswordEntry;\r\n    if (r === 2) {\r\n      userPasswordEntry = getUserPasswordR2(this.encryptionKey);\r\n    } else {\r\n      userPasswordEntry = getUserPasswordR3R4(\r\n        this.document._id,\r\n        this.encryptionKey\r\n      );\r\n    }\r\n\r\n    encDict.V = v;\r\n    if (v >= 2) {\r\n      encDict.Length = this.keyBits;\r\n    }\r\n    if (v === 4) {\r\n      encDict.CF = {\r\n        StdCF: {\r\n          AuthEvent: 'DocOpen',\r\n          CFM: 'AESV2',\r\n          Length: this.keyBits / 8\r\n        }\r\n      };\r\n      encDict.StmF = 'StdCF';\r\n      encDict.StrF = 'StdCF';\r\n    }\r\n    encDict.R = r;\r\n    encDict.O = wordArrayToBuffer(ownerPasswordEntry);\r\n    encDict.U = wordArrayToBuffer(userPasswordEntry);\r\n    encDict.P = permissions;\r\n  }\r\n\r\n  _setupEncryptionV5(encDict, options) {\r\n    this.keyBits = 256;\r\n    const permissions = getPermissionsR3(options.permissions);\r\n\r\n    const processedUserPassword = processPasswordR5(options.userPassword);\r\n    const processedOwnerPassword = options.ownerPassword\r\n      ? processPasswordR5(options.ownerPassword)\r\n      : processedUserPassword;\r\n\r\n    this.encryptionKey = getEncryptionKeyR5(\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n    const userPasswordEntry = getUserPasswordR5(\r\n      processedUserPassword,\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n    const userKeySalt = CryptoJS.lib.WordArray.create(\r\n      userPasswordEntry.words.slice(10, 12),\r\n      8\r\n    );\r\n    const userEncryptionKeyEntry = getUserEncryptionKeyR5(\r\n      processedUserPassword,\r\n      userKeySalt,\r\n      this.encryptionKey\r\n    );\r\n    const ownerPasswordEntry = getOwnerPasswordR5(\r\n      processedOwnerPassword,\r\n      userPasswordEntry,\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n    const ownerKeySalt = CryptoJS.lib.WordArray.create(\r\n      ownerPasswordEntry.words.slice(10, 12),\r\n      8\r\n    );\r\n    const ownerEncryptionKeyEntry = getOwnerEncryptionKeyR5(\r\n      processedOwnerPassword,\r\n      ownerKeySalt,\r\n      userPasswordEntry,\r\n      this.encryptionKey\r\n    );\r\n    const permsEntry = getEncryptedPermissionsR5(\r\n      permissions,\r\n      this.encryptionKey,\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n\r\n    encDict.V = 5;\r\n    encDict.Length = this.keyBits;\r\n    encDict.CF = {\r\n      StdCF: {\r\n        AuthEvent: 'DocOpen',\r\n        CFM: 'AESV3',\r\n        Length: this.keyBits / 8\r\n      }\r\n    };\r\n    encDict.StmF = 'StdCF';\r\n    encDict.StrF = 'StdCF';\r\n    encDict.R = 5;\r\n    encDict.O = wordArrayToBuffer(ownerPasswordEntry);\r\n    encDict.OE = wordArrayToBuffer(ownerEncryptionKeyEntry);\r\n    encDict.U = wordArrayToBuffer(userPasswordEntry);\r\n    encDict.UE = wordArrayToBuffer(userEncryptionKeyEntry);\r\n    encDict.P = permissions;\r\n    encDict.Perms = wordArrayToBuffer(permsEntry);\r\n  }\r\n\r\n  getEncryptFn(obj, gen) {\r\n    let digest;\r\n    if (this.version < 5) {\r\n      digest = this.encryptionKey\r\n        .clone()\r\n        .concat(\r\n          CryptoJS.lib.WordArray.create(\r\n            [\r\n              ((obj & 0xff) << 24) |\r\n                ((obj & 0xff00) << 8) |\r\n                ((obj >> 8) & 0xff00) |\r\n                (gen & 0xff),\r\n              (gen & 0xff00) << 16\r\n            ],\r\n            5\r\n          )\r\n        );\r\n    }\r\n\r\n    if (this.version === 1 || this.version === 2) {\r\n      let key = CryptoJS.MD5(digest);\r\n      key.sigBytes = Math.min(16, this.keyBits / 8 + 5);\r\n      return buffer =>\r\n        wordArrayToBuffer(\r\n          CryptoJS.RC4.encrypt(CryptoJS.lib.WordArray.create(buffer), key)\r\n            .ciphertext\r\n        );\r\n    }\r\n\r\n    let key;\r\n    if (this.version === 4) {\r\n      key = CryptoJS.MD5(\r\n        digest.concat(CryptoJS.lib.WordArray.create([0x73416c54], 4))\r\n      );\r\n    } else {\r\n      key = this.encryptionKey;\r\n    }\r\n\r\n    const iv = PDFSecurity.generateRandomWordArray(16);\r\n    const options = {\r\n      mode: CryptoJS.mode.CBC,\r\n      padding: CryptoJS.pad.Pkcs7,\r\n      iv\r\n    };\r\n\r\n    return buffer =>\r\n      wordArrayToBuffer(\r\n        iv\r\n          .clone()\r\n          .concat(\r\n            CryptoJS.AES.encrypt(\r\n              CryptoJS.lib.WordArray.create(buffer),\r\n              key,\r\n              options\r\n            ).ciphertext\r\n          )\r\n      );\r\n  }\r\n\r\n  end() {\r\n    this.dictionary.end();\r\n  }\r\n}\r\n\r\nfunction getPermissionsR2(permissionObject = {}) {\r\n  let permissions = 0xffffffc0 >> 0;\r\n  if (permissionObject.printing) {\r\n    permissions |= 0b000000000100;\r\n  }\r\n  if (permissionObject.modifying) {\r\n    permissions |= 0b000000001000;\r\n  }\r\n  if (permissionObject.copying) {\r\n    permissions |= 0b000000010000;\r\n  }\r\n  if (permissionObject.annotating) {\r\n    permissions |= 0b000000100000;\r\n  }\r\n  return permissions;\r\n}\r\n\r\nfunction getPermissionsR3(permissionObject = {}) {\r\n  let permissions = 0xfffff0c0 >> 0;\r\n  if (permissionObject.printing === 'lowResolution') {\r\n    permissions |= 0b000000000100;\r\n  }\r\n  if (permissionObject.printing === 'highResolution') {\r\n    permissions |= 0b100000000100;\r\n  }\r\n  if (permissionObject.modifying) {\r\n    permissions |= 0b000000001000;\r\n  }\r\n  if (permissionObject.copying) {\r\n    permissions |= 0b000000010000;\r\n  }\r\n  if (permissionObject.annotating) {\r\n    permissions |= 0b000000100000;\r\n  }\r\n  if (permissionObject.fillingForms) {\r\n    permissions |= 0b000100000000;\r\n  }\r\n  if (permissionObject.contentAccessibility) {\r\n    permissions |= 0b001000000000;\r\n  }\r\n  if (permissionObject.documentAssembly) {\r\n    permissions |= 0b010000000000;\r\n  }\r\n  return permissions;\r\n}\r\n\r\nfunction getUserPasswordR2(encryptionKey) {\r\n  return CryptoJS.RC4.encrypt(processPasswordR2R3R4(), encryptionKey)\r\n    .ciphertext;\r\n}\r\n\r\nfunction getUserPasswordR3R4(documentId, encryptionKey) {\r\n  const key = encryptionKey.clone();\r\n  let cipher = CryptoJS.MD5(\r\n    processPasswordR2R3R4().concat(CryptoJS.lib.WordArray.create(documentId))\r\n  );\r\n  for (let i = 0; i < 20; i++) {\r\n    const xorRound = Math.ceil(key.sigBytes / 4);\r\n    for (let j = 0; j < xorRound; j++) {\r\n      key.words[j] =\r\n        encryptionKey.words[j] ^ (i | (i << 8) | (i << 16) | (i << 24));\r\n    }\r\n    cipher = CryptoJS.RC4.encrypt(cipher, key).ciphertext;\r\n  }\r\n  return cipher.concat(CryptoJS.lib.WordArray.create(null, 16));\r\n}\r\n\r\nfunction getOwnerPasswordR2R3R4(\r\n  r,\r\n  keyBits,\r\n  paddedUserPassword,\r\n  paddedOwnerPassword\r\n) {\r\n  let digest = paddedOwnerPassword;\r\n  let round = r >= 3 ? 51 : 1;\r\n  for (let i = 0; i < round; i++) {\r\n    digest = CryptoJS.MD5(digest);\r\n  }\r\n\r\n  const key = digest.clone();\r\n  key.sigBytes = keyBits / 8;\r\n  let cipher = paddedUserPassword;\r\n  round = r >= 3 ? 20 : 1;\r\n  for (let i = 0; i < round; i++) {\r\n    const xorRound = Math.ceil(key.sigBytes / 4);\r\n    for (let j = 0; j < xorRound; j++) {\r\n      key.words[j] = digest.words[j] ^ (i | (i << 8) | (i << 16) | (i << 24));\r\n    }\r\n    cipher = CryptoJS.RC4.encrypt(cipher, key).ciphertext;\r\n  }\r\n  return cipher;\r\n}\r\n\r\nfunction getEncryptionKeyR2R3R4(\r\n  r,\r\n  keyBits,\r\n  documentId,\r\n  paddedUserPassword,\r\n  ownerPasswordEntry,\r\n  permissions\r\n) {\r\n  let key = paddedUserPassword\r\n    .clone()\r\n    .concat(ownerPasswordEntry)\r\n    .concat(CryptoJS.lib.WordArray.create([lsbFirstWord(permissions)], 4))\r\n    .concat(CryptoJS.lib.WordArray.create(documentId));\r\n  const round = r >= 3 ? 51 : 1;\r\n  for (let i = 0; i < round; i++) {\r\n    key = CryptoJS.MD5(key);\r\n    key.sigBytes = keyBits / 8;\r\n  }\r\n  return key;\r\n}\r\n\r\nfunction getUserPasswordR5(processedUserPassword, generateRandomWordArray) {\r\n  const validationSalt = generateRandomWordArray(8);\r\n  const keySalt = generateRandomWordArray(8);\r\n  return CryptoJS.SHA256(processedUserPassword.clone().concat(validationSalt))\r\n    .concat(validationSalt)\r\n    .concat(keySalt);\r\n}\r\n\r\nfunction getUserEncryptionKeyR5(\r\n  processedUserPassword,\r\n  userKeySalt,\r\n  encryptionKey\r\n) {\r\n  const key = CryptoJS.SHA256(\r\n    processedUserPassword.clone().concat(userKeySalt)\r\n  );\r\n  const options = {\r\n    mode: CryptoJS.mode.CBC,\r\n    padding: CryptoJS.pad.NoPadding,\r\n    iv: CryptoJS.lib.WordArray.create(null, 16)\r\n  };\r\n  return CryptoJS.AES.encrypt(encryptionKey, key, options).ciphertext;\r\n}\r\n\r\nfunction getOwnerPasswordR5(\r\n  processedOwnerPassword,\r\n  userPasswordEntry,\r\n  generateRandomWordArray\r\n) {\r\n  const validationSalt = generateRandomWordArray(8);\r\n  const keySalt = generateRandomWordArray(8);\r\n  return CryptoJS.SHA256(\r\n    processedOwnerPassword\r\n      .clone()\r\n      .concat(validationSalt)\r\n      .concat(userPasswordEntry)\r\n  )\r\n    .concat(validationSalt)\r\n    .concat(keySalt);\r\n}\r\n\r\nfunction getOwnerEncryptionKeyR5(\r\n  processedOwnerPassword,\r\n  ownerKeySalt,\r\n  userPasswordEntry,\r\n  encryptionKey\r\n) {\r\n  const key = CryptoJS.SHA256(\r\n    processedOwnerPassword\r\n      .clone()\r\n      .concat(ownerKeySalt)\r\n      .concat(userPasswordEntry)\r\n  );\r\n  const options = {\r\n    mode: CryptoJS.mode.CBC,\r\n    padding: CryptoJS.pad.NoPadding,\r\n    iv: CryptoJS.lib.WordArray.create(null, 16)\r\n  };\r\n  return CryptoJS.AES.encrypt(encryptionKey, key, options).ciphertext;\r\n}\r\n\r\nfunction getEncryptionKeyR5(generateRandomWordArray) {\r\n  return generateRandomWordArray(32);\r\n}\r\n\r\nfunction getEncryptedPermissionsR5(\r\n  permissions,\r\n  encryptionKey,\r\n  generateRandomWordArray\r\n) {\r\n  const cipher = CryptoJS.lib.WordArray.create(\r\n    [lsbFirstWord(permissions), 0xffffffff, 0x54616462],\r\n    12\r\n  ).concat(generateRandomWordArray(4));\r\n  const options = {\r\n    mode: CryptoJS.mode.ECB,\r\n    padding: CryptoJS.pad.NoPadding\r\n  };\r\n  return CryptoJS.AES.encrypt(cipher, encryptionKey, options).ciphertext;\r\n}\r\n\r\nfunction processPasswordR2R3R4(password = '') {\r\n  const out = Buffer.alloc(32);\r\n  const length = password.length;\r\n  let index = 0;\r\n  while (index < length && index < 32) {\r\n    const code = password.charCodeAt(index);\r\n    if (code > 0xff) {\r\n      throw new Error('Password contains one or more invalid characters.');\r\n    }\r\n    out[index] = code;\r\n    index++;\r\n  }\r\n  while (index < 32) {\r\n    out[index] = PASSWORD_PADDING[index - length];\r\n    index++;\r\n  }\r\n  return CryptoJS.lib.WordArray.create(out);\r\n}\r\n\r\nfunction processPasswordR5(password = '') {\r\n  password = unescape(encodeURIComponent(saslprep(password)));\r\n  const length = Math.min(127, password.length);\r\n  const out = Buffer.alloc(length);\r\n\r\n  for (let i = 0; i < length; i++) {\r\n    out[i] = password.charCodeAt(i);\r\n  }\r\n\r\n  return CryptoJS.lib.WordArray.create(out);\r\n}\r\n\r\nfunction lsbFirstWord(data) {\r\n  return (\r\n    ((data & 0xff) << 24) |\r\n    ((data & 0xff00) << 8) |\r\n    ((data >> 8) & 0xff00) |\r\n    ((data >> 24) & 0xff)\r\n  );\r\n}\r\n\r\nfunction wordArrayToBuffer(wordArray) {\r\n  const byteArray = [];\r\n  for (let i = 0; i < wordArray.sigBytes; i++) {\r\n    byteArray.push(\r\n      (wordArray.words[Math.floor(i / 4)] >> (8 * (3 - (i % 4)))) & 0xff\r\n    );\r\n  }\r\n  return Buffer.from(byteArray);\r\n}\r\n\r\nconst PASSWORD_PADDING = [\r\n  0x28,\r\n  0xbf,\r\n  0x4e,\r\n  0x5e,\r\n  0x4e,\r\n  0x75,\r\n  0x8a,\r\n  0x41,\r\n  0x64,\r\n  0x00,\r\n  0x4e,\r\n  0x56,\r\n  0xff,\r\n  0xfa,\r\n  0x01,\r\n  0x08,\r\n  0x2e,\r\n  0x2e,\r\n  0x00,\r\n  0xb6,\r\n  0xd0,\r\n  0x68,\r\n  0x3e,\r\n  0x80,\r\n  0x2f,\r\n  0x0c,\r\n  0xa9,\r\n  0xfe,\r\n  0x64,\r\n  0x53,\r\n  0x69,\r\n  0x7a\r\n];\r\n\r\nexport default PDFSecurity;\r\n", "import PDFObject from './object';\r\n\r\nconst { number } = PDFObject;\r\n\r\nclass PDFGradient {\r\n  constructor(doc) {\r\n    this.doc = doc;\r\n    this.stops = [];\r\n    this.embedded = false;\r\n    this.transform = [1, 0, 0, 1, 0, 0];\r\n  }\r\n\r\n  stop(pos, color, opacity) {\r\n    if (opacity == null) {\r\n      opacity = 1;\r\n    }\r\n    color = this.doc._normalizeColor(color);\r\n\r\n    if (this.stops.length === 0) {\r\n      if (color.length === 3) {\r\n        this._colorSpace = 'DeviceRGB';\r\n      } else if (color.length === 4) {\r\n        this._colorSpace = 'DeviceCMYK';\r\n      } else if (color.length === 1) {\r\n        this._colorSpace = 'DeviceGray';\r\n      } else {\r\n        throw new Error('Unknown color space');\r\n      }\r\n    } else if (\r\n      (this._colorSpace === 'DeviceRGB' && color.length !== 3) ||\r\n      (this._colorSpace === 'DeviceCMYK' && color.length !== 4) ||\r\n      (this._colorSpace === 'DeviceGray' && color.length !== 1)\r\n    ) {\r\n      throw new Error('All gradient stops must use the same color space');\r\n    }\r\n\r\n    opacity = Math.max(0, Math.min(1, opacity));\r\n    this.stops.push([pos, color, opacity]);\r\n    return this;\r\n  }\r\n\r\n  setTransform(m11, m12, m21, m22, dx, dy) {\r\n    this.transform = [m11, m12, m21, m22, dx, dy];\r\n    return this;\r\n  }\r\n\r\n  embed(m) {\r\n    let fn;\r\n    const stopsLength = this.stops.length;\r\n    if (stopsLength === 0) {\r\n      return;\r\n    }\r\n    this.embedded = true;\r\n    this.matrix = m;\r\n\r\n    // if the last stop comes before 100%, add a copy at 100%\r\n    const last = this.stops[stopsLength - 1];\r\n    if (last[0] < 1) {\r\n      this.stops.push([1, last[1], last[2]]);\r\n    }\r\n\r\n    const bounds = [];\r\n    const encode = [];\r\n    const stops = [];\r\n\r\n    for (let i = 0; i < stopsLength - 1; i++) {\r\n      encode.push(0, 1);\r\n      if (i + 2 !== stopsLength) {\r\n        bounds.push(this.stops[i + 1][0]);\r\n      }\r\n\r\n      fn = this.doc.ref({\r\n        FunctionType: 2,\r\n        Domain: [0, 1],\r\n        C0: this.stops[i + 0][1],\r\n        C1: this.stops[i + 1][1],\r\n        N: 1\r\n      });\r\n\r\n      stops.push(fn);\r\n      fn.end();\r\n    }\r\n\r\n    // if there are only two stops, we don't need a stitching function\r\n    if (stopsLength === 1) {\r\n      fn = stops[0];\r\n    } else {\r\n      fn = this.doc.ref({\r\n        FunctionType: 3, // stitching function\r\n        Domain: [0, 1],\r\n        Functions: stops,\r\n        Bounds: bounds,\r\n        Encode: encode\r\n      });\r\n\r\n      fn.end();\r\n    }\r\n\r\n    this.id = `Sh${++this.doc._gradCount}`;\r\n\r\n    const shader = this.shader(fn);\r\n    shader.end();\r\n\r\n    const pattern = this.doc.ref({\r\n      Type: 'Pattern',\r\n      PatternType: 2,\r\n      Shading: shader,\r\n      Matrix: this.matrix.map(number)\r\n    });\r\n\r\n    pattern.end();\r\n\r\n    if (this.stops.some(stop => stop[2] < 1)) {\r\n      let grad = this.opacityGradient();\r\n      grad._colorSpace = 'DeviceGray';\r\n\r\n      for (let stop of this.stops) {\r\n        grad.stop(stop[0], [stop[2]]);\r\n      }\r\n\r\n      grad = grad.embed(this.matrix);\r\n\r\n      const pageBBox = [0, 0, this.doc.page.width, this.doc.page.height];\r\n\r\n      const form = this.doc.ref({\r\n        Type: 'XObject',\r\n        Subtype: 'Form',\r\n        FormType: 1,\r\n        BBox: pageBBox,\r\n        Group: {\r\n          Type: 'Group',\r\n          S: 'Transparency',\r\n          CS: 'DeviceGray'\r\n        },\r\n        Resources: {\r\n          ProcSet: ['PDF', 'Text', 'ImageB', 'ImageC', 'ImageI'],\r\n          Pattern: {\r\n            Sh1: grad\r\n          }\r\n        }\r\n      });\r\n\r\n      form.write('/Pattern cs /Sh1 scn');\r\n      form.end(`${pageBBox.join(' ')} re f`);\r\n\r\n      const gstate = this.doc.ref({\r\n        Type: 'ExtGState',\r\n        SMask: {\r\n          Type: 'Mask',\r\n          S: 'Luminosity',\r\n          G: form\r\n        }\r\n      });\r\n\r\n      gstate.end();\r\n\r\n      const opacityPattern = this.doc.ref({\r\n        Type: 'Pattern',\r\n        PatternType: 1,\r\n        PaintType: 1,\r\n        TilingType: 2,\r\n        BBox: pageBBox,\r\n        XStep: pageBBox[2],\r\n        YStep: pageBBox[3],\r\n        Resources: {\r\n          ProcSet: ['PDF', 'Text', 'ImageB', 'ImageC', 'ImageI'],\r\n          Pattern: {\r\n            Sh1: pattern\r\n          },\r\n          ExtGState: {\r\n            Gs1: gstate\r\n          }\r\n        }\r\n      });\r\n\r\n      opacityPattern.write('/Gs1 gs /Pattern cs /Sh1 scn');\r\n      opacityPattern.end(`${pageBBox.join(' ')} re f`);\r\n\r\n      this.doc.page.patterns[this.id] = opacityPattern;\r\n    } else {\r\n      this.doc.page.patterns[this.id] = pattern;\r\n    }\r\n\r\n    return pattern;\r\n  }\r\n\r\n  apply(stroke) {\r\n    // apply gradient transform to existing document ctm\r\n    const [m0, m1, m2, m3, m4, m5] = this.doc._ctm;\r\n    const [m11, m12, m21, m22, dx, dy] = this.transform;\r\n    const m = [\r\n      m0 * m11 + m2 * m12,\r\n      m1 * m11 + m3 * m12,\r\n      m0 * m21 + m2 * m22,\r\n      m1 * m21 + m3 * m22,\r\n      m0 * dx + m2 * dy + m4,\r\n      m1 * dx + m3 * dy + m5\r\n    ];\r\n\r\n    if (!this.embedded || m.join(' ') !== this.matrix.join(' ')) {\r\n      this.embed(m);\r\n    }\r\n    this.doc._setColorSpace('Pattern', stroke);\r\n    const op = stroke ? 'SCN' : 'scn';\r\n    return this.doc.addContent(`/${this.id} ${op}`);\r\n  }\r\n}\r\n\r\nclass PDFLinearGradient extends PDFGradient {\r\n  constructor(doc, x1, y1, x2, y2) {\r\n    super(doc);\r\n    this.x1 = x1;\r\n    this.y1 = y1;\r\n    this.x2 = x2;\r\n    this.y2 = y2;\r\n  }\r\n\r\n  shader(fn) {\r\n    return this.doc.ref({\r\n      ShadingType: 2,\r\n      ColorSpace: this._colorSpace,\r\n      Coords: [this.x1, this.y1, this.x2, this.y2],\r\n      Function: fn,\r\n      Extend: [true, true]\r\n    });\r\n  }\r\n\r\n  opacityGradient() {\r\n    return new PDFLinearGradient(this.doc, this.x1, this.y1, this.x2, this.y2);\r\n  }\r\n}\r\n\r\nclass PDFRadialGradient extends PDFGradient {\r\n  constructor(doc, x1, y1, r1, x2, y2, r2) {\r\n    super(doc);\r\n    this.doc = doc;\r\n    this.x1 = x1;\r\n    this.y1 = y1;\r\n    this.r1 = r1;\r\n    this.x2 = x2;\r\n    this.y2 = y2;\r\n    this.r2 = r2;\r\n  }\r\n\r\n  shader(fn) {\r\n    return this.doc.ref({\r\n      ShadingType: 3,\r\n      ColorSpace: this._colorSpace,\r\n      Coords: [this.x1, this.y1, this.r1, this.x2, this.y2, this.r2],\r\n      Function: fn,\r\n      Extend: [true, true]\r\n    });\r\n  }\r\n\r\n  opacityGradient() {\r\n    return new PDFRadialGradient(\r\n      this.doc,\r\n      this.x1,\r\n      this.y1,\r\n      this.r1,\r\n      this.x2,\r\n      this.y2,\r\n      this.r2\r\n    );\r\n  }\r\n}\r\n\r\nexport default { PDFGradient, PDFLinearGradient, PDFRadialGradient };\r\n", "/*\r\nPDF tiling pattern support. Uncolored only.\r\n */\r\n\r\nconst underlyingColorSpaces = ['DeviceCMYK', 'DeviceRGB'];\r\n\r\nclass PDFTilingPattern {\r\n  constructor(doc, bBox, xStep, yStep, stream) {\r\n    this.doc = doc;\r\n    this.bBox = bBox;\r\n    this.xStep = xStep;\r\n    this.yStep = yStep;\r\n    this.stream = stream;\r\n  }\r\n\r\n  createPattern() {\r\n    // no resources needed for our current usage\r\n    // required entry\r\n    const resources = this.doc.ref();\r\n    resources.end();\r\n    // apply default transform matrix (flipped in the default doc._ctm)\r\n    // see document.js & gradient.js\r\n    const [m0, m1, m2, m3, m4, m5] = this.doc._ctm;\r\n    const [m11, m12, m21, m22, dx, dy] = [1, 0, 0, 1, 0, 0];\r\n    const m = [\r\n      m0 * m11 + m2 * m12,\r\n      m1 * m11 + m3 * m12,\r\n      m0 * m21 + m2 * m22,\r\n      m1 * m21 + m3 * m22,\r\n      m0 * dx + m2 * dy + m4,\r\n      m1 * dx + m3 * dy + m5\r\n    ];\r\n    const pattern = this.doc.ref({\r\n      Type: 'Pattern',\r\n      PatternType: 1, // tiling\r\n      PaintType: 2, // 1-colored, 2-uncolored\r\n      TilingType: 2, // 2-no distortion\r\n      BBox: this.bBox,\r\n      XStep: this.xStep,\r\n      YStep: this.yStep,\r\n      Matrix: m.map(v => +v.toFixed(5)),\r\n      Resources: resources\r\n    });\r\n    pattern.end(this.stream);\r\n    return pattern;\r\n  }\r\n\r\n  embedPatternColorSpaces() {\r\n    // map each pattern to an underlying color space\r\n    // and embed on each page\r\n    underlyingColorSpaces.forEach(csName => {\r\n      const csId = this.getPatternColorSpaceId(csName);\r\n\r\n      if (this.doc.page.colorSpaces[csId]) return;\r\n      const cs = this.doc.ref(['Pattern', csName]);\r\n      cs.end();\r\n      this.doc.page.colorSpaces[csId] = cs;\r\n    });\r\n  }\r\n\r\n  getPatternColorSpaceId(underlyingColorspace) {\r\n    return `CsP${underlyingColorspace}`;\r\n  }\r\n\r\n  embed() {\r\n    if (!this.id) {\r\n      this.doc._patternCount = this.doc._patternCount + 1;\r\n      this.id = 'P' + this.doc._patternCount;\r\n      this.pattern = this.createPattern();\r\n    }\r\n\r\n    // patterns are embedded in each page\r\n    if (!this.doc.page.patterns[this.id]) {\r\n      this.doc.page.patterns[this.id] = this.pattern;\r\n    }\r\n  }\r\n\r\n  apply(stroke, patternColor) {\r\n    // do any embedding/creating that might be needed\r\n    this.embedPatternColorSpaces();\r\n    this.embed();\r\n\r\n    const normalizedColor = this.doc._normalizeColor(patternColor);\r\n    if (!normalizedColor)\r\n      throw Error(`invalid pattern color. (value: ${patternColor})`);\r\n\r\n    // select one of the pattern color spaces\r\n    const csId = this.getPatternColorSpaceId(\r\n      this.doc._getColorSpace(normalizedColor)\r\n    );\r\n    this.doc._setColorSpace(csId, stroke);\r\n\r\n    // stroke/fill using the pattern and color (in the above underlying color space)\r\n    const op = stroke ? 'SCN' : 'scn';\r\n    return this.doc.addContent(\r\n      `${normalizedColor.join(' ')} /${this.id} ${op}`\r\n    );\r\n  }\r\n}\r\n\r\nexport default { PDFTilingPattern };\r\n", "import Gradient from '../gradient';\r\nimport pattern from '../pattern';\r\nimport SpotColor from '../spotcolor';\r\n\r\nconst { PDFGradient, PDFLinearGradient, PDFRadialGradient } = Gradient;\r\nconst { PDFTilingPattern } = pattern;\r\n\r\nexport default {\r\n  initColor() {\r\n    this.spotColors = {};\r\n    // The opacity dictionaries\r\n    this._opacityRegistry = {};\r\n    this._opacityCount = 0;\r\n    this._patternCount = 0;\r\n    return (this._gradCount = 0);\r\n  },\r\n\r\n  _normalizeColor(color) {\r\n    if (typeof color === 'string') {\r\n      if (color.charAt(0) === '#') {\r\n        if (color.length === 4) {\r\n          color = color.replace(\r\n            /#([0-9A-F])([0-9A-F])([0-9A-F])/i,\r\n            '#$1$1$2$2$3$3'\r\n          );\r\n        }\r\n        const hex = parseInt(color.slice(1), 16);\r\n        color = [hex >> 16, (hex >> 8) & 0xff, hex & 0xff];\r\n      } else if (namedColors[color]) {\r\n        color = namedColors[color];\r\n      } else if (this.spotColors[color]) {\r\n        return this.spotColors[color];\r\n      }\r\n    }\r\n\r\n    if (Array.isArray(color)) {\r\n      // RGB\r\n      if (color.length === 3) {\r\n        color = color.map(part => part / 255);\r\n        // CMYK\r\n      } else if (color.length === 4) {\r\n        color = color.map(part => part / 100);\r\n      }\r\n      return color;\r\n    }\r\n\r\n    return null;\r\n  },\r\n\r\n  _setColor(color, stroke) {\r\n    if (color instanceof PDFGradient) {\r\n      color.apply(stroke);\r\n      return true;\r\n      // see if tiling pattern, decode & apply it it\r\n    } else if (Array.isArray(color) && color[0] instanceof PDFTilingPattern) {\r\n      color[0].apply(stroke, color[1]);\r\n      return true;\r\n    }\r\n    // any other case should be a normal color and not a pattern\r\n    return this._setColorCore(color, stroke);\r\n  },\r\n\r\n  _setColorCore(color, stroke) {\r\n    color = this._normalizeColor(color);\r\n    if (!color) {\r\n      return false;\r\n    }\r\n\r\n    const op = stroke ? 'SCN' : 'scn';\r\n    const space = this._getColorSpace(color);\r\n    this._setColorSpace(space, stroke);\r\n\r\n    if (color instanceof SpotColor) {\r\n      this.page.colorSpaces[color.id] = color.ref;\r\n      this.addContent(`1 ${op}`);\r\n    } else {\r\n      this.addContent(`${color.join(' ')} ${op}`);\r\n    }\r\n\r\n    return true;\r\n  },\r\n\r\n  _setColorSpace(space, stroke) {\r\n    const op = stroke ? 'CS' : 'cs';\r\n    return this.addContent(`/${space} ${op}`);\r\n  },\r\n\r\n  _getColorSpace(color) {\r\n    if (color instanceof SpotColor) {\r\n      return color.id;\r\n    }\r\n\r\n    return color.length === 4 ? 'DeviceCMYK' : 'DeviceRGB';\r\n  },\r\n\r\n  fillColor(color, opacity) {\r\n    const set = this._setColor(color, false);\r\n    if (set) {\r\n      this.fillOpacity(opacity);\r\n    }\r\n\r\n    // save this for text wrapper, which needs to reset\r\n    // the fill color on new pages\r\n    this._fillColor = [color, opacity];\r\n    return this;\r\n  },\r\n\r\n  strokeColor(color, opacity) {\r\n    const set = this._setColor(color, true);\r\n    if (set) {\r\n      this.strokeOpacity(opacity);\r\n    }\r\n    return this;\r\n  },\r\n\r\n  opacity(opacity) {\r\n    this._doOpacity(opacity, opacity);\r\n    return this;\r\n  },\r\n\r\n  fillOpacity(opacity) {\r\n    this._doOpacity(opacity, null);\r\n    return this;\r\n  },\r\n\r\n  strokeOpacity(opacity) {\r\n    this._doOpacity(null, opacity);\r\n    return this;\r\n  },\r\n\r\n  _doOpacity(fillOpacity, strokeOpacity) {\r\n    let dictionary, name;\r\n    if (fillOpacity == null && strokeOpacity == null) {\r\n      return;\r\n    }\r\n\r\n    if (fillOpacity != null) {\r\n      fillOpacity = Math.max(0, Math.min(1, fillOpacity));\r\n    }\r\n    if (strokeOpacity != null) {\r\n      strokeOpacity = Math.max(0, Math.min(1, strokeOpacity));\r\n    }\r\n    const key = `${fillOpacity}_${strokeOpacity}`;\r\n\r\n    if (this._opacityRegistry[key]) {\r\n      [dictionary, name] = this._opacityRegistry[key];\r\n    } else {\r\n      dictionary = { Type: 'ExtGState' };\r\n\r\n      if (fillOpacity != null) {\r\n        dictionary.ca = fillOpacity;\r\n      }\r\n      if (strokeOpacity != null) {\r\n        dictionary.CA = strokeOpacity;\r\n      }\r\n\r\n      dictionary = this.ref(dictionary);\r\n      dictionary.end();\r\n      const id = ++this._opacityCount;\r\n      name = `Gs${id}`;\r\n      this._opacityRegistry[key] = [dictionary, name];\r\n    }\r\n\r\n    this.page.ext_gstates[name] = dictionary;\r\n    return this.addContent(`/${name} gs`);\r\n  },\r\n\r\n  linearGradient(x1, y1, x2, y2) {\r\n    return new PDFLinearGradient(this, x1, y1, x2, y2);\r\n  },\r\n\r\n  radialGradient(x1, y1, r1, x2, y2, r2) {\r\n    return new PDFRadialGradient(this, x1, y1, r1, x2, y2, r2);\r\n  },\r\n\r\n  pattern(bbox, xStep, yStep, stream) {\r\n    return new PDFTilingPattern(this, bbox, xStep, yStep, stream);\r\n  },\r\n\r\n  addSpotColor(name, C, M, Y, K) {\r\n    const color = new SpotColor(this, name, C, M, Y, K);\r\n    this.spotColors[name] = color;\r\n    return this;\r\n  }\r\n};\r\n\r\nvar namedColors = {\r\n  aliceblue: [240, 248, 255],\r\n  antiquewhite: [250, 235, 215],\r\n  aqua: [0, 255, 255],\r\n  aquamarine: [127, 255, 212],\r\n  azure: [240, 255, 255],\r\n  beige: [245, 245, 220],\r\n  bisque: [255, 228, 196],\r\n  black: [0, 0, 0],\r\n  blanchedalmond: [255, 235, 205],\r\n  blue: [0, 0, 255],\r\n  blueviolet: [138, 43, 226],\r\n  brown: [165, 42, 42],\r\n  burlywood: [222, 184, 135],\r\n  cadetblue: [95, 158, 160],\r\n  chartreuse: [127, 255, 0],\r\n  chocolate: [210, 105, 30],\r\n  coral: [255, 127, 80],\r\n  cornflowerblue: [100, 149, 237],\r\n  cornsilk: [255, 248, 220],\r\n  crimson: [220, 20, 60],\r\n  cyan: [0, 255, 255],\r\n  darkblue: [0, 0, 139],\r\n  darkcyan: [0, 139, 139],\r\n  darkgoldenrod: [184, 134, 11],\r\n  darkgray: [169, 169, 169],\r\n  darkgreen: [0, 100, 0],\r\n  darkgrey: [169, 169, 169],\r\n  darkkhaki: [189, 183, 107],\r\n  darkmagenta: [139, 0, 139],\r\n  darkolivegreen: [85, 107, 47],\r\n  darkorange: [255, 140, 0],\r\n  darkorchid: [153, 50, 204],\r\n  darkred: [139, 0, 0],\r\n  darksalmon: [233, 150, 122],\r\n  darkseagreen: [143, 188, 143],\r\n  darkslateblue: [72, 61, 139],\r\n  darkslategray: [47, 79, 79],\r\n  darkslategrey: [47, 79, 79],\r\n  darkturquoise: [0, 206, 209],\r\n  darkviolet: [148, 0, 211],\r\n  deeppink: [255, 20, 147],\r\n  deepskyblue: [0, 191, 255],\r\n  dimgray: [105, 105, 105],\r\n  dimgrey: [105, 105, 105],\r\n  dodgerblue: [30, 144, 255],\r\n  firebrick: [178, 34, 34],\r\n  floralwhite: [255, 250, 240],\r\n  forestgreen: [34, 139, 34],\r\n  fuchsia: [255, 0, 255],\r\n  gainsboro: [220, 220, 220],\r\n  ghostwhite: [248, 248, 255],\r\n  gold: [255, 215, 0],\r\n  goldenrod: [218, 165, 32],\r\n  gray: [128, 128, 128],\r\n  grey: [128, 128, 128],\r\n  green: [0, 128, 0],\r\n  greenyellow: [173, 255, 47],\r\n  honeydew: [240, 255, 240],\r\n  hotpink: [255, 105, 180],\r\n  indianred: [205, 92, 92],\r\n  indigo: [75, 0, 130],\r\n  ivory: [255, 255, 240],\r\n  khaki: [240, 230, 140],\r\n  lavender: [230, 230, 250],\r\n  lavenderblush: [255, 240, 245],\r\n  lawngreen: [124, 252, 0],\r\n  lemonchiffon: [255, 250, 205],\r\n  lightblue: [173, 216, 230],\r\n  lightcoral: [240, 128, 128],\r\n  lightcyan: [224, 255, 255],\r\n  lightgoldenrodyellow: [250, 250, 210],\r\n  lightgray: [211, 211, 211],\r\n  lightgreen: [144, 238, 144],\r\n  lightgrey: [211, 211, 211],\r\n  lightpink: [255, 182, 193],\r\n  lightsalmon: [255, 160, 122],\r\n  lightseagreen: [32, 178, 170],\r\n  lightskyblue: [135, 206, 250],\r\n  lightslategray: [119, 136, 153],\r\n  lightslategrey: [119, 136, 153],\r\n  lightsteelblue: [176, 196, 222],\r\n  lightyellow: [255, 255, 224],\r\n  lime: [0, 255, 0],\r\n  limegreen: [50, 205, 50],\r\n  linen: [250, 240, 230],\r\n  magenta: [255, 0, 255],\r\n  maroon: [128, 0, 0],\r\n  mediumaquamarine: [102, 205, 170],\r\n  mediumblue: [0, 0, 205],\r\n  mediumorchid: [186, 85, 211],\r\n  mediumpurple: [147, 112, 219],\r\n  mediumseagreen: [60, 179, 113],\r\n  mediumslateblue: [123, 104, 238],\r\n  mediumspringgreen: [0, 250, 154],\r\n  mediumturquoise: [72, 209, 204],\r\n  mediumvioletred: [199, 21, 133],\r\n  midnightblue: [25, 25, 112],\r\n  mintcream: [245, 255, 250],\r\n  mistyrose: [255, 228, 225],\r\n  moccasin: [255, 228, 181],\r\n  navajowhite: [255, 222, 173],\r\n  navy: [0, 0, 128],\r\n  oldlace: [253, 245, 230],\r\n  olive: [128, 128, 0],\r\n  olivedrab: [107, 142, 35],\r\n  orange: [255, 165, 0],\r\n  orangered: [255, 69, 0],\r\n  orchid: [218, 112, 214],\r\n  palegoldenrod: [238, 232, 170],\r\n  palegreen: [152, 251, 152],\r\n  paleturquoise: [175, 238, 238],\r\n  palevioletred: [219, 112, 147],\r\n  papayawhip: [255, 239, 213],\r\n  peachpuff: [255, 218, 185],\r\n  peru: [205, 133, 63],\r\n  pink: [255, 192, 203],\r\n  plum: [221, 160, 221],\r\n  powderblue: [176, 224, 230],\r\n  purple: [128, 0, 128],\r\n  red: [255, 0, 0],\r\n  rosybrown: [188, 143, 143],\r\n  royalblue: [65, 105, 225],\r\n  saddlebrown: [139, 69, 19],\r\n  salmon: [250, 128, 114],\r\n  sandybrown: [244, 164, 96],\r\n  seagreen: [46, 139, 87],\r\n  seashell: [255, 245, 238],\r\n  sienna: [160, 82, 45],\r\n  silver: [192, 192, 192],\r\n  skyblue: [135, 206, 235],\r\n  slateblue: [106, 90, 205],\r\n  slategray: [112, 128, 144],\r\n  slategrey: [112, 128, 144],\r\n  snow: [255, 250, 250],\r\n  springgreen: [0, 255, 127],\r\n  steelblue: [70, 130, 180],\r\n  tan: [210, 180, 140],\r\n  teal: [0, 128, 128],\r\n  thistle: [216, 191, 216],\r\n  tomato: [255, 99, 71],\r\n  turquoise: [64, 224, 208],\r\n  violet: [238, 130, 238],\r\n  wheat: [245, 222, 179],\r\n  white: [255, 255, 255],\r\n  whitesmoke: [245, 245, 245],\r\n  yellow: [255, 255, 0],\r\n  yellowgreen: [154, 205, 50]\r\n};\r\n", "let cx, cy, px, py, sx, sy;\r\n\r\ncx = cy = px = py = sx = sy = 0;\r\n\r\nconst parameters = {\r\n  A: 7,\r\n  a: 7,\r\n  C: 6,\r\n  c: 6,\r\n  H: 1,\r\n  h: 1,\r\n  L: 2,\r\n  l: 2,\r\n  M: 2,\r\n  m: 2,\r\n  Q: 4,\r\n  q: 4,\r\n  S: 4,\r\n  s: 4,\r\n  T: 2,\r\n  t: 2,\r\n  V: 1,\r\n  v: 1,\r\n  Z: 0,\r\n  z: 0\r\n};\r\n\r\nconst parse = function(path) {\r\n  let cmd;\r\n  const ret = [];\r\n  let args = [];\r\n  let curArg = '';\r\n  let foundDecimal = false;\r\n  let params = 0;\r\n\r\n  for (let c of path) {\r\n    if (parameters[c] != null) {\r\n      params = parameters[c];\r\n      if (cmd) {\r\n        // save existing command\r\n        if (curArg.length > 0) {\r\n          args[args.length] = +curArg;\r\n        }\r\n        ret[ret.length] = { cmd, args };\r\n\r\n        args = [];\r\n        curArg = '';\r\n        foundDecimal = false;\r\n      }\r\n\r\n      cmd = c;\r\n    } else if (\r\n      [' ', ','].includes(c) ||\r\n      (c === '-' && curArg.length > 0 && curArg[curArg.length - 1] !== 'e') ||\r\n      (c === '.' && foundDecimal)\r\n    ) {\r\n      if (curArg.length === 0) {\r\n        continue;\r\n      }\r\n\r\n      if (args.length === params) {\r\n        // handle reused commands\r\n        ret[ret.length] = { cmd, args };\r\n        args = [+curArg];\r\n\r\n        // handle assumed commands\r\n        if (cmd === 'M') {\r\n          cmd = 'L';\r\n        }\r\n        if (cmd === 'm') {\r\n          cmd = 'l';\r\n        }\r\n      } else {\r\n        args[args.length] = +curArg;\r\n      }\r\n\r\n      foundDecimal = c === '.';\r\n\r\n      // fix for negative numbers or repeated decimals with no delimeter between commands\r\n      curArg = ['-', '.'].includes(c) ? c : '';\r\n    } else {\r\n      curArg += c;\r\n      if (c === '.') {\r\n        foundDecimal = true;\r\n      }\r\n    }\r\n  }\r\n\r\n  // add the last command\r\n  if (curArg.length > 0) {\r\n    if (args.length === params) {\r\n      // handle reused commands\r\n      ret[ret.length] = { cmd, args };\r\n      args = [+curArg];\r\n\r\n      // handle assumed commands\r\n      if (cmd === 'M') {\r\n        cmd = 'L';\r\n      }\r\n      if (cmd === 'm') {\r\n        cmd = 'l';\r\n      }\r\n    } else {\r\n      args[args.length] = +curArg;\r\n    }\r\n  }\r\n\r\n  ret[ret.length] = { cmd, args };\r\n\r\n  return ret;\r\n};\r\n\r\nconst apply = function(commands, doc) {\r\n  // current point, control point, and subpath starting point\r\n  cx = cy = px = py = sx = sy = 0;\r\n\r\n  // run the commands\r\n  for (let i = 0; i < commands.length; i++) {\r\n    const c = commands[i];\r\n    if (typeof runners[c.cmd] === 'function') {\r\n      runners[c.cmd](doc, c.args);\r\n    }\r\n  }\r\n};\r\n\r\nconst runners = {\r\n  M(doc, a) {\r\n    cx = a[0];\r\n    cy = a[1];\r\n    px = py = null;\r\n    sx = cx;\r\n    sy = cy;\r\n    return doc.moveTo(cx, cy);\r\n  },\r\n\r\n  m(doc, a) {\r\n    cx += a[0];\r\n    cy += a[1];\r\n    px = py = null;\r\n    sx = cx;\r\n    sy = cy;\r\n    return doc.moveTo(cx, cy);\r\n  },\r\n\r\n  C(doc, a) {\r\n    cx = a[4];\r\n    cy = a[5];\r\n    px = a[2];\r\n    py = a[3];\r\n    return doc.bezierCurveTo(...a);\r\n  },\r\n\r\n  c(doc, a) {\r\n    doc.bezierCurveTo(\r\n      a[0] + cx,\r\n      a[1] + cy,\r\n      a[2] + cx,\r\n      a[3] + cy,\r\n      a[4] + cx,\r\n      a[5] + cy\r\n    );\r\n    px = cx + a[2];\r\n    py = cy + a[3];\r\n    cx += a[4];\r\n    return (cy += a[5]);\r\n  },\r\n\r\n  S(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    }\r\n\r\n    doc.bezierCurveTo(cx - (px - cx), cy - (py - cy), a[0], a[1], a[2], a[3]);\r\n    px = a[0];\r\n    py = a[1];\r\n    cx = a[2];\r\n    return (cy = a[3]);\r\n  },\r\n\r\n  s(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    }\r\n\r\n    doc.bezierCurveTo(\r\n      cx - (px - cx),\r\n      cy - (py - cy),\r\n      cx + a[0],\r\n      cy + a[1],\r\n      cx + a[2],\r\n      cy + a[3]\r\n    );\r\n    px = cx + a[0];\r\n    py = cy + a[1];\r\n    cx += a[2];\r\n    return (cy += a[3]);\r\n  },\r\n\r\n  Q(doc, a) {\r\n    px = a[0];\r\n    py = a[1];\r\n    cx = a[2];\r\n    cy = a[3];\r\n    return doc.quadraticCurveTo(a[0], a[1], cx, cy);\r\n  },\r\n\r\n  q(doc, a) {\r\n    doc.quadraticCurveTo(a[0] + cx, a[1] + cy, a[2] + cx, a[3] + cy);\r\n    px = cx + a[0];\r\n    py = cy + a[1];\r\n    cx += a[2];\r\n    return (cy += a[3]);\r\n  },\r\n\r\n  T(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    } else {\r\n      px = cx - (px - cx);\r\n      py = cy - (py - cy);\r\n    }\r\n\r\n    doc.quadraticCurveTo(px, py, a[0], a[1]);\r\n    px = cx - (px - cx);\r\n    py = cy - (py - cy);\r\n    cx = a[0];\r\n    return (cy = a[1]);\r\n  },\r\n\r\n  t(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    } else {\r\n      px = cx - (px - cx);\r\n      py = cy - (py - cy);\r\n    }\r\n\r\n    doc.quadraticCurveTo(px, py, cx + a[0], cy + a[1]);\r\n    cx += a[0];\r\n    return (cy += a[1]);\r\n  },\r\n\r\n  A(doc, a) {\r\n    solveArc(doc, cx, cy, a);\r\n    cx = a[5];\r\n    return (cy = a[6]);\r\n  },\r\n\r\n  a(doc, a) {\r\n    a[5] += cx;\r\n    a[6] += cy;\r\n    solveArc(doc, cx, cy, a);\r\n    cx = a[5];\r\n    return (cy = a[6]);\r\n  },\r\n\r\n  L(doc, a) {\r\n    cx = a[0];\r\n    cy = a[1];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  l(doc, a) {\r\n    cx += a[0];\r\n    cy += a[1];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  H(doc, a) {\r\n    cx = a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  h(doc, a) {\r\n    cx += a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  V(doc, a) {\r\n    cy = a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  v(doc, a) {\r\n    cy += a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  Z(doc) {\r\n    doc.closePath();\r\n    cx = sx;\r\n    return (cy = sy);\r\n  },\r\n\r\n  z(doc) {\r\n    doc.closePath();\r\n    cx = sx;\r\n    return (cy = sy);\r\n  }\r\n};\r\n\r\nconst solveArc = function(doc, x, y, coords) {\r\n  const [rx, ry, rot, large, sweep, ex, ey] = coords;\r\n  const segs = arcToSegments(ex, ey, rx, ry, large, sweep, rot, x, y);\r\n\r\n  for (let seg of segs) {\r\n    const bez = segmentToBezier(...seg);\r\n    doc.bezierCurveTo(...bez);\r\n  }\r\n};\r\n\r\n// from Inkscape svgtopdf, thanks!\r\nconst arcToSegments = function(x, y, rx, ry, large, sweep, rotateX, ox, oy) {\r\n  const th = rotateX * (Math.PI / 180);\r\n  const sin_th = Math.sin(th);\r\n  const cos_th = Math.cos(th);\r\n  rx = Math.abs(rx);\r\n  ry = Math.abs(ry);\r\n  px = cos_th * (ox - x) * 0.5 + sin_th * (oy - y) * 0.5;\r\n  py = cos_th * (oy - y) * 0.5 - sin_th * (ox - x) * 0.5;\r\n  let pl = (px * px) / (rx * rx) + (py * py) / (ry * ry);\r\n  if (pl > 1) {\r\n    pl = Math.sqrt(pl);\r\n    rx *= pl;\r\n    ry *= pl;\r\n  }\r\n\r\n  const a00 = cos_th / rx;\r\n  const a01 = sin_th / rx;\r\n  const a10 = -sin_th / ry;\r\n  const a11 = cos_th / ry;\r\n  const x0 = a00 * ox + a01 * oy;\r\n  const y0 = a10 * ox + a11 * oy;\r\n  const x1 = a00 * x + a01 * y;\r\n  const y1 = a10 * x + a11 * y;\r\n\r\n  const d = (x1 - x0) * (x1 - x0) + (y1 - y0) * (y1 - y0);\r\n  let sfactor_sq = 1 / d - 0.25;\r\n  if (sfactor_sq < 0) {\r\n    sfactor_sq = 0;\r\n  }\r\n  let sfactor = Math.sqrt(sfactor_sq);\r\n  if (sweep === large) {\r\n    sfactor = -sfactor;\r\n  }\r\n\r\n  const xc = 0.5 * (x0 + x1) - sfactor * (y1 - y0);\r\n  const yc = 0.5 * (y0 + y1) + sfactor * (x1 - x0);\r\n\r\n  const th0 = Math.atan2(y0 - yc, x0 - xc);\r\n  const th1 = Math.atan2(y1 - yc, x1 - xc);\r\n\r\n  let th_arc = th1 - th0;\r\n  if (th_arc < 0 && sweep === 1) {\r\n    th_arc += 2 * Math.PI;\r\n  } else if (th_arc > 0 && sweep === 0) {\r\n    th_arc -= 2 * Math.PI;\r\n  }\r\n\r\n  const segments = Math.ceil(Math.abs(th_arc / (Math.PI * 0.5 + 0.001)));\r\n  const result = [];\r\n\r\n  for (let i = 0; i < segments; i++) {\r\n    const th2 = th0 + (i * th_arc) / segments;\r\n    const th3 = th0 + ((i + 1) * th_arc) / segments;\r\n    result[i] = [xc, yc, th2, th3, rx, ry, sin_th, cos_th];\r\n  }\r\n\r\n  return result;\r\n};\r\n\r\nconst segmentToBezier = function(cx, cy, th0, th1, rx, ry, sin_th, cos_th) {\r\n  const a00 = cos_th * rx;\r\n  const a01 = -sin_th * ry;\r\n  const a10 = sin_th * rx;\r\n  const a11 = cos_th * ry;\r\n\r\n  const th_half = 0.5 * (th1 - th0);\r\n  const t =\r\n    ((8 / 3) * Math.sin(th_half * 0.5) * Math.sin(th_half * 0.5)) /\r\n    Math.sin(th_half);\r\n  const x1 = cx + Math.cos(th0) - t * Math.sin(th0);\r\n  const y1 = cy + Math.sin(th0) + t * Math.cos(th0);\r\n  const x3 = cx + Math.cos(th1);\r\n  const y3 = cy + Math.sin(th1);\r\n  const x2 = x3 + t * Math.sin(th1);\r\n  const y2 = y3 - t * Math.cos(th1);\r\n\r\n  return [\r\n    a00 * x1 + a01 * y1,\r\n    a10 * x1 + a11 * y1,\r\n    a00 * x2 + a01 * y2,\r\n    a10 * x2 + a11 * y2,\r\n    a00 * x3 + a01 * y3,\r\n    a10 * x3 + a11 * y3\r\n  ];\r\n};\r\n\r\nclass SVGPath {\r\n  static apply(doc, path) {\r\n    const commands = parse(path);\r\n    apply(commands, doc);\r\n  }\r\n}\r\n\r\nexport default SVGPath;\r\n", "import SVGPath from '../path';\r\nimport PDFObject from '../object';\r\n\r\nconst { number } = PDFObject;\r\n\r\n// This constant is used to approximate a symmetrical arc using a cubic\r\n// Bezier curve.\r\nconst KAPPA = 4.0 * ((Math.sqrt(2) - 1.0) / 3.0);\r\nexport default {\r\n  initVector() {\r\n    this._ctm = [1, 0, 0, 1, 0, 0]; // current transformation matrix\r\n    return (this._ctmStack = []);\r\n  },\r\n\r\n  save() {\r\n    this._ctmStack.push(this._ctm.slice());\r\n    // TODO: save/restore colorspace and styles so not setting it unnessesarily all the time?\r\n    return this.addContent('q');\r\n  },\r\n\r\n  restore() {\r\n    this._ctm = this._ctmStack.pop() || [1, 0, 0, 1, 0, 0];\r\n    return this.addContent('Q');\r\n  },\r\n\r\n  closePath() {\r\n    return this.addContent('h');\r\n  },\r\n\r\n  lineWidth(w) {\r\n    return this.addContent(`${number(w)} w`);\r\n  },\r\n\r\n  _CAP_STYLES: {\r\n    BUTT: 0,\r\n    ROUND: 1,\r\n    SQUARE: 2\r\n  },\r\n\r\n  lineCap(c) {\r\n    if (typeof c === 'string') {\r\n      c = this._CAP_STYLES[c.toUpperCase()];\r\n    }\r\n    return this.addContent(`${c} J`);\r\n  },\r\n\r\n  _JOIN_STYLES: {\r\n    MITER: 0,\r\n    ROUND: 1,\r\n    BEVEL: 2\r\n  },\r\n\r\n  lineJoin(j) {\r\n    if (typeof j === 'string') {\r\n      j = this._JOIN_STYLES[j.toUpperCase()];\r\n    }\r\n    return this.addContent(`${j} j`);\r\n  },\r\n\r\n  miterLimit(m) {\r\n    return this.addContent(`${number(m)} M`);\r\n  },\r\n\r\n  dash(length, options = {}) {\r\n    const originalLength = length;\r\n    if (!Array.isArray(length)) {\r\n      length = [length, options.space || length];\r\n    }\r\n\r\n    const valid = length.every(x => Number.isFinite(x) && x > 0);\r\n    if (!valid) {\r\n      throw new Error(\r\n        `dash(${JSON.stringify(originalLength)}, ${JSON.stringify(\r\n          options\r\n        )}) invalid, lengths must be numeric and greater than zero`\r\n      );\r\n    }\r\n\r\n    length = length.map(number).join(' ');\r\n    return this.addContent(`[${length}] ${number(options.phase || 0)} d`);\r\n  },\r\n\r\n  undash() {\r\n    return this.addContent('[] 0 d');\r\n  },\r\n\r\n  moveTo(x, y) {\r\n    return this.addContent(`${number(x)} ${number(y)} m`);\r\n  },\r\n\r\n  lineTo(x, y) {\r\n    return this.addContent(`${number(x)} ${number(y)} l`);\r\n  },\r\n\r\n  bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y) {\r\n    return this.addContent(\r\n      `${number(cp1x)} ${number(cp1y)} ${number(cp2x)} ${number(cp2y)} ${number(\r\n        x\r\n      )} ${number(y)} c`\r\n    );\r\n  },\r\n\r\n  quadraticCurveTo(cpx, cpy, x, y) {\r\n    return this.addContent(\r\n      `${number(cpx)} ${number(cpy)} ${number(x)} ${number(y)} v`\r\n    );\r\n  },\r\n\r\n  rect(x, y, w, h) {\r\n    return this.addContent(\r\n      `${number(x)} ${number(y)} ${number(w)} ${number(h)} re`\r\n    );\r\n  },\r\n\r\n  roundedRect(x, y, w, h, r) {\r\n    if (r == null) {\r\n      r = 0;\r\n    }\r\n    r = Math.min(r, 0.5 * w, 0.5 * h);\r\n\r\n    // amount to inset control points from corners (see `ellipse`)\r\n    const c = r * (1.0 - KAPPA);\r\n\r\n    this.moveTo(x + r, y);\r\n    this.lineTo(x + w - r, y);\r\n    this.bezierCurveTo(x + w - c, y, x + w, y + c, x + w, y + r);\r\n    this.lineTo(x + w, y + h - r);\r\n    this.bezierCurveTo(x + w, y + h - c, x + w - c, y + h, x + w - r, y + h);\r\n    this.lineTo(x + r, y + h);\r\n    this.bezierCurveTo(x + c, y + h, x, y + h - c, x, y + h - r);\r\n    this.lineTo(x, y + r);\r\n    this.bezierCurveTo(x, y + c, x + c, y, x + r, y);\r\n    return this.closePath();\r\n  },\r\n\r\n  ellipse(x, y, r1, r2) {\r\n    // based on http://stackoverflow.com/questions/2172798/how-to-draw-an-oval-in-html5-canvas/2173084#2173084\r\n    if (r2 == null) {\r\n      r2 = r1;\r\n    }\r\n    x -= r1;\r\n    y -= r2;\r\n    const ox = r1 * KAPPA;\r\n    const oy = r2 * KAPPA;\r\n    const xe = x + r1 * 2;\r\n    const ye = y + r2 * 2;\r\n    const xm = x + r1;\r\n    const ym = y + r2;\r\n\r\n    this.moveTo(x, ym);\r\n    this.bezierCurveTo(x, ym - oy, xm - ox, y, xm, y);\r\n    this.bezierCurveTo(xm + ox, y, xe, ym - oy, xe, ym);\r\n    this.bezierCurveTo(xe, ym + oy, xm + ox, ye, xm, ye);\r\n    this.bezierCurveTo(xm - ox, ye, x, ym + oy, x, ym);\r\n    return this.closePath();\r\n  },\r\n\r\n  circle(x, y, radius) {\r\n    return this.ellipse(x, y, radius);\r\n  },\r\n\r\n  arc(x, y, radius, startAngle, endAngle, anticlockwise) {\r\n    if (anticlockwise == null) {\r\n      anticlockwise = false;\r\n    }\r\n    const TWO_PI = 2.0 * Math.PI;\r\n    const HALF_PI = 0.5 * Math.PI;\r\n\r\n    let deltaAng = endAngle - startAngle;\r\n\r\n    if (Math.abs(deltaAng) > TWO_PI) {\r\n      // draw only full circle if more than that is specified\r\n      deltaAng = TWO_PI;\r\n    } else if (deltaAng !== 0 && anticlockwise !== deltaAng < 0) {\r\n      // necessary to flip direction of rendering\r\n      const dir = anticlockwise ? -1 : 1;\r\n      deltaAng = dir * TWO_PI + deltaAng;\r\n    }\r\n\r\n    const numSegs = Math.ceil(Math.abs(deltaAng) / HALF_PI);\r\n    const segAng = deltaAng / numSegs;\r\n    const handleLen = (segAng / HALF_PI) * KAPPA * radius;\r\n    let curAng = startAngle;\r\n\r\n    // component distances between anchor point and control point\r\n    let deltaCx = -Math.sin(curAng) * handleLen;\r\n    let deltaCy = Math.cos(curAng) * handleLen;\r\n\r\n    // anchor point\r\n    let ax = x + Math.cos(curAng) * radius;\r\n    let ay = y + Math.sin(curAng) * radius;\r\n\r\n    // calculate and render segments\r\n    this.moveTo(ax, ay);\r\n\r\n    for (let segIdx = 0; segIdx < numSegs; segIdx++) {\r\n      // starting control point\r\n      const cp1x = ax + deltaCx;\r\n      const cp1y = ay + deltaCy;\r\n\r\n      // step angle\r\n      curAng += segAng;\r\n\r\n      // next anchor point\r\n      ax = x + Math.cos(curAng) * radius;\r\n      ay = y + Math.sin(curAng) * radius;\r\n\r\n      // next control point delta\r\n      deltaCx = -Math.sin(curAng) * handleLen;\r\n      deltaCy = Math.cos(curAng) * handleLen;\r\n\r\n      // ending control point\r\n      const cp2x = ax - deltaCx;\r\n      const cp2y = ay - deltaCy;\r\n\r\n      // render segment\r\n      this.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, ax, ay);\r\n    }\r\n\r\n    return this;\r\n  },\r\n\r\n  polygon(...points) {\r\n    this.moveTo(...(points.shift() || []));\r\n    for (let point of points) {\r\n      this.lineTo(...(point || []));\r\n    }\r\n    return this.closePath();\r\n  },\r\n\r\n  path(path) {\r\n    SVGPath.apply(this, path);\r\n    return this;\r\n  },\r\n\r\n  _windingRule(rule) {\r\n    if (/even-?odd/.test(rule)) {\r\n      return '*';\r\n    }\r\n\r\n    return '';\r\n  },\r\n\r\n  fill(color, rule) {\r\n    if (/(even-?odd)|(non-?zero)/.test(color)) {\r\n      rule = color;\r\n      color = null;\r\n    }\r\n\r\n    if (color) {\r\n      this.fillColor(color);\r\n    }\r\n    return this.addContent(`f${this._windingRule(rule)}`);\r\n  },\r\n\r\n  stroke(color) {\r\n    if (color) {\r\n      this.strokeColor(color);\r\n    }\r\n    return this.addContent('S');\r\n  },\r\n\r\n  fillAndStroke(fillColor, strokeColor, rule) {\r\n    if (strokeColor == null) {\r\n      strokeColor = fillColor;\r\n    }\r\n    const isFillRule = /(even-?odd)|(non-?zero)/;\r\n    if (isFillRule.test(fillColor)) {\r\n      rule = fillColor;\r\n      fillColor = null;\r\n    }\r\n\r\n    if (isFillRule.test(strokeColor)) {\r\n      rule = strokeColor;\r\n      strokeColor = fillColor;\r\n    }\r\n\r\n    if (fillColor) {\r\n      this.fillColor(fillColor);\r\n      this.strokeColor(strokeColor);\r\n    }\r\n\r\n    return this.addContent(`B${this._windingRule(rule)}`);\r\n  },\r\n\r\n  clip(rule) {\r\n    return this.addContent(`W${this._windingRule(rule)} n`);\r\n  },\r\n\r\n  transform(m11, m12, m21, m22, dx, dy) {\r\n    // keep track of the current transformation matrix\r\n    if (m11 === 1 && m12 === 0 && m21 === 0 && m22 === 1 && dx === 0 && dy === 0) {\r\n      // Ignore identity transforms\r\n      return this;\r\n    }\r\n    const m = this._ctm;\r\n    const [m0, m1, m2, m3, m4, m5] = m;\r\n    m[0] = m0 * m11 + m2 * m12;\r\n    m[1] = m1 * m11 + m3 * m12;\r\n    m[2] = m0 * m21 + m2 * m22;\r\n    m[3] = m1 * m21 + m3 * m22;\r\n    m[4] = m0 * dx + m2 * dy + m4;\r\n    m[5] = m1 * dx + m3 * dy + m5;\r\n\r\n    const values = [m11, m12, m21, m22, dx, dy].map(v => number(v)).join(' ');\r\n    return this.addContent(`${values} cm`);\r\n  },\r\n\r\n  translate(x, y) {\r\n    return this.transform(1, 0, 0, 1, x, y);\r\n  },\r\n\r\n  rotate(angle, options = {}) {\r\n    let y;\r\n    const rad = (angle * Math.PI) / 180;\r\n    const cos = Math.cos(rad);\r\n    const sin = Math.sin(rad);\r\n    let x = (y = 0);\r\n\r\n    if (options.origin != null) {\r\n      [x, y] = options.origin;\r\n      const x1 = x * cos - y * sin;\r\n      const y1 = x * sin + y * cos;\r\n      x -= x1;\r\n      y -= y1;\r\n    }\r\n\r\n    return this.transform(cos, sin, -sin, cos, x, y);\r\n  },\r\n\r\n  scale(xFactor, yFactor, options = {}) {\r\n    let y;\r\n    if (yFactor == null) {\r\n      yFactor = xFactor;\r\n    }\r\n    if (typeof yFactor === 'object') {\r\n      options = yFactor;\r\n      yFactor = xFactor;\r\n    }\r\n\r\n    let x = (y = 0);\r\n    if (options.origin != null) {\r\n      [x, y] = options.origin;\r\n      x -= xFactor * x;\r\n      y -= yFactor * y;\r\n    }\r\n\r\n    return this.transform(xFactor, 0, 0, yFactor, x, y);\r\n  }\r\n};\r\n", "import fs from 'fs';\r\n\r\nconst WIN_ANSI_MAP = {\r\n  402: 131,\r\n  8211: 150,\r\n  8212: 151,\r\n  8216: 145,\r\n  8217: 146,\r\n  8218: 130,\r\n  8220: 147,\r\n  8221: 148,\r\n  8222: 132,\r\n  8224: 134,\r\n  8225: 135,\r\n  8226: 149,\r\n  8230: 133,\r\n  8364: 128,\r\n  8240: 137,\r\n  8249: 139,\r\n  8250: 155,\r\n  710: 136,\r\n  8482: 153,\r\n  338: 140,\r\n  339: 156,\r\n  732: 152,\r\n  352: 138,\r\n  353: 154,\r\n  376: 159,\r\n  381: 142,\r\n  382: 158\r\n};\r\n\r\nconst characters = `\\\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n  \r\nspace         exclam         quotedbl       numbersign\r\ndollar        percent        ampersand      quotesingle\r\nparenleft     parenright     asterisk       plus\r\ncomma         hyphen         period         slash\r\nzero          one            two            three\r\nfour          five           six            seven\r\neight         nine           colon          semicolon\r\nless          equal          greater        question\r\n  \r\nat            A              B              C\r\nD             E              F              G\r\nH             I              J              K\r\nL             M              N              O\r\nP             Q              R              S\r\nT             U              V              W\r\nX             Y              Z              bracketleft\r\nbackslash     bracketright   asciicircum    underscore\r\n  \r\ngrave         a              b              c\r\nd             e              f              g\r\nh             i              j              k\r\nl             m              n              o\r\np             q              r              s\r\nt             u              v              w\r\nx             y              z              braceleft\r\nbar           braceright     asciitilde     .notdef\r\n  \r\nEuro          .notdef        quotesinglbase florin\r\nquotedblbase  ellipsis       dagger         daggerdbl\r\ncircumflex    perthousand    Scaron         guilsinglleft\r\nOE            .notdef        Zcaron         .notdef\r\n.notdef       quoteleft      quoteright     quotedblleft\r\nquotedblright bullet         endash         emdash\r\ntilde         trademark      scaron         guilsinglright\r\noe            .notdef        zcaron         ydieresis\r\n  \r\nspace         exclamdown     cent           sterling\r\ncurrency      yen            brokenbar      section\r\ndieresis      copyright      ordfeminine    guillemotleft\r\nlogicalnot    hyphen         registered     macron\r\ndegree        plusminus      twosuperior    threesuperior\r\nacute         mu             paragraph      periodcentered\r\ncedilla       onesuperior    ordmasculine   guillemotright\r\nonequarter    onehalf        threequarters  questiondown\r\n  \r\nAgrave        Aacute         Acircumflex    Atilde\r\nAdieresis     Aring          AE             Ccedilla\r\nEgrave        Eacute         Ecircumflex    Edieresis\r\nIgrave        Iacute         Icircumflex    Idieresis\r\nEth           Ntilde         Ograve         Oacute\r\nOcircumflex   Otilde         Odieresis      multiply\r\nOslash        Ugrave         Uacute         Ucircumflex\r\nUdieresis     Yacute         Thorn          germandbls\r\n  \r\nagrave        aacute         acircumflex    atilde\r\nadieresis     aring          ae             ccedilla\r\negrave        eacute         ecircumflex    edieresis\r\nigrave        iacute         icircumflex    idieresis\r\neth           ntilde         ograve         oacute\r\nocircumflex   otilde         odieresis      divide\r\noslash        ugrave         uacute         ucircumflex\r\nudieresis     yacute         thorn          ydieresis\\\r\n`.split(/\\s+/);\r\n\r\nclass AFMFont {\r\n  static open(filename) {\r\n    return new AFMFont(fs.readFileSync(filename, 'utf8'));\r\n  }\r\n\r\n  constructor(contents) {\r\n    this.contents = contents;\r\n    this.attributes = {};\r\n    this.glyphWidths = {};\r\n    this.boundingBoxes = {};\r\n    this.kernPairs = {};\r\n\r\n    this.parse();\r\n    // todo: remove charWidths since appears to not be used\r\n    this.charWidths = new Array(256);\r\n    for (let char = 0; char <= 255; char++) {\r\n      this.charWidths[char] = this.glyphWidths[characters[char]];\r\n    }\r\n\r\n    this.bbox = this.attributes['FontBBox'].split(/\\s+/).map(e => +e);\r\n    this.ascender = +(this.attributes['Ascender'] || 0);\r\n    this.descender = +(this.attributes['Descender'] || 0);\r\n    this.xHeight = +(this.attributes['XHeight'] || 0);\r\n    this.capHeight = +(this.attributes['CapHeight'] || 0);\r\n    this.lineGap =\r\n      this.bbox[3] - this.bbox[1] - (this.ascender - this.descender);\r\n  }\r\n\r\n  parse() {\r\n    let section = '';\r\n    for (let line of this.contents.split('\\n')) {\r\n      var match;\r\n      var a;\r\n      if ((match = line.match(/^Start(\\w+)/))) {\r\n        section = match[1];\r\n        continue;\r\n      } else if ((match = line.match(/^End(\\w+)/))) {\r\n        section = '';\r\n        continue;\r\n      }\r\n\r\n      switch (section) {\r\n        case 'FontMetrics':\r\n          match = line.match(/(^\\w+)\\s+(.*)/);\r\n          var key = match[1];\r\n          var value = match[2];\r\n\r\n          if ((a = this.attributes[key])) {\r\n            if (!Array.isArray(a)) {\r\n              a = this.attributes[key] = [a];\r\n            }\r\n            a.push(value);\r\n          } else {\r\n            this.attributes[key] = value;\r\n          }\r\n          break;\r\n\r\n        case 'CharMetrics':\r\n          if (!/^CH?\\s/.test(line)) {\r\n            continue;\r\n          }\r\n          var name = line.match(/\\bN\\s+(\\.?\\w+)\\s*;/)[1];\r\n          this.glyphWidths[name] = +line.match(/\\bWX\\s+(\\d+)\\s*;/)[1];\r\n          break;\r\n\r\n        case 'KernPairs':\r\n          match = line.match(/^KPX\\s+(\\.?\\w+)\\s+(\\.?\\w+)\\s+(-?\\d+)/);\r\n          if (match) {\r\n            this.kernPairs[match[1] + '\\0' + match[2]] = parseInt(match[3]);\r\n          }\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  encodeText(text) {\r\n    const res = [];\r\n    for (let i = 0, len = text.length; i < len; i++) {\r\n      let char = text.charCodeAt(i);\r\n      char = WIN_ANSI_MAP[char] || char;\r\n      res.push(char.toString(16));\r\n    }\r\n\r\n    return res;\r\n  }\r\n\r\n  glyphsForString(string) {\r\n    const glyphs = [];\r\n\r\n    for (let i = 0, len = string.length; i < len; i++) {\r\n      const charCode = string.charCodeAt(i);\r\n      glyphs.push(this.characterToGlyph(charCode));\r\n    }\r\n\r\n    return glyphs;\r\n  }\r\n\r\n  characterToGlyph(character) {\r\n    return characters[WIN_ANSI_MAP[character] || character] || '.notdef';\r\n  }\r\n\r\n  widthOfGlyph(glyph) {\r\n    return this.glyphWidths[glyph] || 0;\r\n  }\r\n\r\n  getKernPair(left, right) {\r\n    return this.kernPairs[left + '\\0' + right] || 0;\r\n  }\r\n\r\n  advancesForGlyphs(glyphs) {\r\n    const advances = [];\r\n\r\n    for (let index = 0; index < glyphs.length; index++) {\r\n      const left = glyphs[index];\r\n      const right = glyphs[index + 1];\r\n      advances.push(this.widthOfGlyph(left) + this.getKernPair(left, right));\r\n    }\r\n\r\n    return advances;\r\n  }\r\n}\r\n\r\nexport default AFMFont;\r\n", "class PDFFont {\r\n  constructor() {}\r\n\r\n  encode() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  widthOfString() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  ref() {\r\n    return this.dictionary != null\r\n      ? this.dictionary\r\n      : (this.dictionary = this.document.ref());\r\n  }\r\n\r\n  finalize() {\r\n    if (this.embedded || this.dictionary == null) {\r\n      return;\r\n    }\r\n\r\n    this.embed();\r\n    return (this.embedded = true);\r\n  }\r\n\r\n  embed() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  lineHeight(size, includeGap) {\r\n    if (includeGap == null) {\r\n      includeGap = false;\r\n    }\r\n    const gap = includeGap ? this.lineGap : 0;\r\n    return ((this.ascender + gap - this.descender) / 1000) * size;\r\n  }\r\n}\r\n\r\nexport default PDFFont;\r\n", "import AFMFont from './afm';\r\nimport PDFFont from '../font';\r\nimport fs from 'fs';\r\n\r\n// This insanity is so bundlers can inline the font files\r\nconst STANDARD_FONTS = {\r\n  Courier() {\r\n    return fs.readFileSync(__dirname + '/data/Courier.afm', 'utf8');\r\n  },\r\n  'Courier-Bold'() {\r\n    return fs.readFileSync(__dirname + '/data/Courier-Bold.afm', 'utf8');\r\n  },\r\n  'Courier-Oblique'() {\r\n    return fs.readFileSync(__dirname + '/data/Courier-Oblique.afm', 'utf8');\r\n  },\r\n  'Courier-BoldOblique'() {\r\n    return fs.readFileSync(__dirname + '/data/Courier-BoldOblique.afm', 'utf8');\r\n  },\r\n  Helvetica() {\r\n    return fs.readFileSync(__dirname + '/data/Helvetica.afm', 'utf8');\r\n  },\r\n  'Helvetica-Bold'() {\r\n    return fs.readFileSync(__dirname + '/data/Helvetica-Bold.afm', 'utf8');\r\n  },\r\n  'Helvetica-Oblique'() {\r\n    return fs.readFileSync(__dirname + '/data/Helvetica-Oblique.afm', 'utf8');\r\n  },\r\n  'Helvetica-BoldOblique'() {\r\n    return fs.readFileSync(\r\n      __dirname + '/data/Helvetica-BoldOblique.afm',\r\n      'utf8'\r\n    );\r\n  },\r\n  'Times-Roman'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-Roman.afm', 'utf8');\r\n  },\r\n  'Times-Bold'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-Bold.afm', 'utf8');\r\n  },\r\n  'Times-Italic'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-Italic.afm', 'utf8');\r\n  },\r\n  'Times-BoldItalic'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-BoldItalic.afm', 'utf8');\r\n  },\r\n  Symbol() {\r\n    return fs.readFileSync(__dirname + '/data/Symbol.afm', 'utf8');\r\n  },\r\n  ZapfDingbats() {\r\n    return fs.readFileSync(__dirname + '/data/ZapfDingbats.afm', 'utf8');\r\n  }\r\n};\r\n\r\nclass StandardFont extends PDFFont {\r\n  constructor(document, name, id) {\r\n    super();\r\n    this.document = document;\r\n    this.name = name;\r\n    this.id = id;\r\n    this.font = new AFMFont(STANDARD_FONTS[this.name]());\r\n    ({\r\n      ascender: this.ascender,\r\n      descender: this.descender,\r\n      bbox: this.bbox,\r\n      lineGap: this.lineGap,\r\n      xHeight: this.xHeight,\r\n      capHeight: this.capHeight\r\n    } = this.font);\r\n  }\r\n\r\n  embed() {\r\n    this.dictionary.data = {\r\n      Type: 'Font',\r\n      BaseFont: this.name,\r\n      Subtype: 'Type1',\r\n      Encoding: 'WinAnsiEncoding'\r\n    };\r\n\r\n    return this.dictionary.end();\r\n  }\r\n\r\n  encode(text) {\r\n    const encoded = this.font.encodeText(text);\r\n    const glyphs = this.font.glyphsForString(`${text}`);\r\n    const advances = this.font.advancesForGlyphs(glyphs);\r\n    const positions = [];\r\n    for (let i = 0; i < glyphs.length; i++) {\r\n      const glyph = glyphs[i];\r\n      positions.push({\r\n        xAdvance: advances[i],\r\n        yAdvance: 0,\r\n        xOffset: 0,\r\n        yOffset: 0,\r\n        advanceWidth: this.font.widthOfGlyph(glyph)\r\n      });\r\n    }\r\n\r\n    return [encoded, positions];\r\n  }\r\n\r\n  widthOfString(string, size) {\r\n    const glyphs = this.font.glyphsForString(`${string}`);\r\n    const advances = this.font.advancesForGlyphs(glyphs);\r\n\r\n    let width = 0;\r\n    for (let advance of advances) {\r\n      width += advance;\r\n    }\r\n\r\n    const scale = size / 1000;\r\n    return width * scale;\r\n  }\r\n\r\n  static isStandardFont(name) {\r\n    return name in STANDARD_FONTS;\r\n  }\r\n}\r\n\r\nexport default StandardFont;\r\n", "import PDFFont from '../font';\r\n\r\nconst toHex = function(num) {\r\n  return `0000${num.toString(16)}`.slice(-4);\r\n};\r\n\r\nclass EmbeddedFont extends PDFFont {\r\n  constructor(document, font, id) {\r\n    super();\r\n    this.document = document;\r\n    this.font = font;\r\n    this.id = id;\r\n    this.subset = this.font.createSubset();\r\n    this.unicode = [[0]];\r\n    this.widths = [this.font.getGlyph(0).advanceWidth];\r\n\r\n    this.name = this.font.postscriptName;\r\n    this.scale = 1000 / this.font.unitsPerEm;\r\n    this.ascender = this.font.ascent * this.scale;\r\n    this.descender = this.font.descent * this.scale;\r\n    this.xHeight = this.font.xHeight * this.scale;\r\n    this.capHeight = this.font.capHeight * this.scale;\r\n    this.lineGap = this.font.lineGap * this.scale;\r\n    this.bbox = this.font.bbox;\r\n\r\n    if (document.options.fontLayoutCache !== false) {\r\n      this.layoutCache = Object.create(null);\r\n    }\r\n  }\r\n\r\n  layoutRun(text, features) {\r\n    const run = this.font.layout(text, features);\r\n\r\n    // Normalize position values\r\n    for (let i = 0; i < run.positions.length; i++) {\r\n      const position = run.positions[i];\r\n      for (let key in position) {\r\n        position[key] *= this.scale;\r\n      }\r\n\r\n      position.advanceWidth = run.glyphs[i].advanceWidth * this.scale;\r\n    }\r\n\r\n    return run;\r\n  }\r\n\r\n  layoutCached(text) {\r\n    if (!this.layoutCache) {\r\n      return this.layoutRun(text);\r\n    }\r\n    let cached;\r\n    if ((cached = this.layoutCache[text])) {\r\n      return cached;\r\n    }\r\n\r\n    const run = this.layoutRun(text);\r\n    this.layoutCache[text] = run;\r\n    return run;\r\n  }\r\n\r\n  layout(text, features, onlyWidth) {\r\n    // Skip the cache if any user defined features are applied\r\n    if (features) {\r\n      return this.layoutRun(text, features);\r\n    }\r\n\r\n    let glyphs = onlyWidth ? null : [];\r\n    let positions = onlyWidth ? null : [];\r\n    let advanceWidth = 0;\r\n\r\n    // Split the string by words to increase cache efficiency.\r\n    // For this purpose, spaces and tabs are a good enough delimeter.\r\n    let last = 0;\r\n    let index = 0;\r\n    while (index <= text.length) {\r\n      var needle;\r\n      if (\r\n        (index === text.length && last < index) ||\r\n        ((needle = text.charAt(index)), [' ', '\\t'].includes(needle))\r\n      ) {\r\n        const run = this.layoutCached(text.slice(last, ++index));\r\n        if (!onlyWidth) {\r\n          glyphs = glyphs.concat(run.glyphs);\r\n          positions = positions.concat(run.positions);\r\n        }\r\n\r\n        advanceWidth += run.advanceWidth;\r\n        last = index;\r\n      } else {\r\n        index++;\r\n      }\r\n    }\r\n\r\n    return { glyphs, positions, advanceWidth };\r\n  }\r\n\r\n  encode(text, features) {\r\n    const { glyphs, positions } = this.layout(text, features);\r\n\r\n    const res = [];\r\n    for (let i = 0; i < glyphs.length; i++) {\r\n      const glyph = glyphs[i];\r\n      const gid = this.subset.includeGlyph(glyph.id);\r\n      res.push(`0000${gid.toString(16)}`.slice(-4));\r\n\r\n      if (this.widths[gid] == null) {\r\n        this.widths[gid] = glyph.advanceWidth * this.scale;\r\n      }\r\n      if (this.unicode[gid] == null) {\r\n        this.unicode[gid] = glyph.codePoints;\r\n      }\r\n    }\r\n\r\n    return [res, positions];\r\n  }\r\n\r\n  widthOfString(string, size, features) {\r\n    const width = this.layout(string, features, true).advanceWidth;\r\n    const scale = size / 1000;\r\n    return width * scale;\r\n  }\r\n\r\n  embed() {\r\n    const isCFF = this.subset.cff != null;\r\n    const fontFile = this.document.ref();\r\n\r\n    if (isCFF) {\r\n      fontFile.data.Subtype = 'CIDFontType0C';\r\n    }\r\n\r\n    this.subset\r\n      .encodeStream()\r\n      .on('data', data => fontFile.write(data))\r\n      .on('end', () => fontFile.end());\r\n\r\n    const familyClass =\r\n      ((this.font['OS/2'] != null\r\n        ? this.font['OS/2'].sFamilyClass\r\n        : undefined) || 0) >> 8;\r\n    let flags = 0;\r\n    if (this.font.post.isFixedPitch) {\r\n      flags |= 1 << 0;\r\n    }\r\n    if (1 <= familyClass && familyClass <= 7) {\r\n      flags |= 1 << 1;\r\n    }\r\n    flags |= 1 << 2; // assume the font uses non-latin characters\r\n    if (familyClass === 10) {\r\n      flags |= 1 << 3;\r\n    }\r\n    if (this.font.head.macStyle.italic) {\r\n      flags |= 1 << 6;\r\n    }\r\n\r\n    // generate a tag (6 uppercase letters. 17 is the char code offset from '0' to 'A'. 73 will map to 'Z')\r\n    const tag = [1, 2, 3, 4, 5, 6]\r\n      .map(i => String.fromCharCode((this.id.charCodeAt(i) || 73) + 17))\r\n      .join('');\r\n    const name = tag + '+' + this.font.postscriptName.replaceAll(' ', '_');\r\n\r\n    const { bbox } = this.font;\r\n    const descriptor = this.document.ref({\r\n      Type: 'FontDescriptor',\r\n      FontName: name,\r\n      Flags: flags,\r\n      FontBBox: [\r\n        bbox.minX * this.scale,\r\n        bbox.minY * this.scale,\r\n        bbox.maxX * this.scale,\r\n        bbox.maxY * this.scale\r\n      ],\r\n      ItalicAngle: this.font.italicAngle,\r\n      Ascent: this.ascender,\r\n      Descent: this.descender,\r\n      CapHeight: (this.font.capHeight || this.font.ascent) * this.scale,\r\n      XHeight: (this.font.xHeight || 0) * this.scale,\r\n      StemV: 0\r\n    }); // not sure how to calculate this\r\n\r\n    if (isCFF) {\r\n      descriptor.data.FontFile3 = fontFile;\r\n    } else {\r\n      descriptor.data.FontFile2 = fontFile;\r\n    }\r\n\r\n    if (this.document.subset && this.document.subset === 1) {\r\n      const CIDSet = Buffer.from('FFFFFFFFC0', 'hex');\r\n      const CIDSetRef = this.document.ref();\r\n      CIDSetRef.write(CIDSet);\r\n      CIDSetRef.end();\r\n\r\n      descriptor.data.CIDSet = CIDSetRef;\r\n    }\r\n\r\n    descriptor.end();\r\n\r\n    const descendantFontData = {\r\n      Type: 'Font',\r\n      Subtype: 'CIDFontType0',\r\n      BaseFont: name,\r\n      CIDSystemInfo: {\r\n        Registry: new String('Adobe'),\r\n        Ordering: new String('Identity'),\r\n        Supplement: 0\r\n      },\r\n      FontDescriptor: descriptor,\r\n      W: [0, this.widths]\r\n    };\r\n\r\n    if (!isCFF) {\r\n      descendantFontData.Subtype = 'CIDFontType2';\r\n      descendantFontData.CIDToGIDMap = 'Identity';\r\n    }\r\n\r\n    const descendantFont = this.document.ref(descendantFontData);\r\n\r\n    descendantFont.end();\r\n\r\n    this.dictionary.data = {\r\n      Type: 'Font',\r\n      Subtype: 'Type0',\r\n      BaseFont: name,\r\n      Encoding: 'Identity-H',\r\n      DescendantFonts: [descendantFont],\r\n      ToUnicode: this.toUnicodeCmap()\r\n    };\r\n\r\n    return this.dictionary.end();\r\n  }\r\n\r\n  // Maps the glyph ids encoded in the PDF back to unicode strings\r\n  // Because of ligature substitutions and the like, there may be one or more\r\n  // unicode characters represented by each glyph.\r\n  toUnicodeCmap() {\r\n    const cmap = this.document.ref();\r\n\r\n    const entries = [];\r\n    for (let codePoints of this.unicode) {\r\n      const encoded = [];\r\n\r\n      // encode codePoints to utf16\r\n      for (let value of codePoints) {\r\n        if (value > 0xffff) {\r\n          value -= 0x10000;\r\n          encoded.push(toHex(((value >>> 10) & 0x3ff) | 0xd800));\r\n          value = 0xdc00 | (value & 0x3ff);\r\n        }\r\n\r\n        encoded.push(toHex(value));\r\n      }\r\n\r\n      entries.push(`<${encoded.join(' ')}>`);\r\n    }\r\n\r\n    const chunkSize = 256;\r\n    const chunks = Math.ceil(entries.length / chunkSize);\r\n    const ranges = [];\r\n    for (let i = 0; i < chunks; i++) {\r\n      const start = i * chunkSize;\r\n      const end = Math.min((i + 1) * chunkSize, entries.length);\r\n      ranges.push(`<${toHex(start)}> <${toHex(end - 1)}> [${entries.slice(start, end).join(' ')}]`);\r\n    }\r\n\r\n    cmap.end(`\\\r\n/CIDInit /ProcSet findresource begin\r\n12 dict begin\r\nbegincmap\r\n/CIDSystemInfo <<\r\n  /Registry (Adobe)\r\n  /Ordering (UCS)\r\n  /Supplement 0\r\n>> def\r\n/CMapName /Adobe-Identity-UCS def\r\n/CMapType 2 def\r\n1 begincodespacerange\r\n<0000><ffff>\r\nendcodespacerange\r\n1 beginbfrange\r\n${ranges.join('\\n')}\r\nendbfrange\r\nendcmap\r\nCMapName currentdict /CMap defineresource pop\r\nend\r\nend\\\r\n`);\r\n\r\n    return cmap;\r\n  }\r\n}\r\n\r\nexport default EmbeddedFont;\r\n", "import fs from 'fs';\r\nimport fontkit from '@foliojs-fork/fontkit';\r\nimport StandardFont from './font/standard';\r\nimport EmbeddedFont from './font/embedded';\r\n\r\nclass PDFFontFactory {\r\n  static open(document, src, family, id) {\r\n    let font;\r\n    if (typeof src === 'string') {\r\n      if (StandardFont.isStandardFont(src)) {\r\n        return new StandardFont(document, src, id);\r\n      }\r\n\r\n      src = fs.readFileSync(src);\r\n    }\r\n    if (Buffer.isBuffer(src)) {\r\n      font = fontkit.create(src, family);\r\n    } else if (src instanceof Uint8Array) {\r\n      font = fontkit.create(Buffer.from(src), family);\r\n    } else if (src instanceof ArrayBuffer) {\r\n      font = fontkit.create(Buffer.from(new Uint8Array(src)), family);\r\n    }\r\n\r\n    if (font == null) {\r\n      throw new Error('Not a supported font format or standard PDF font.');\r\n    }\r\n\r\n    return new EmbeddedFont(document, font, id);\r\n  }\r\n}\r\n\r\nexport default PDFFontFactory;\r\n", "import PDFFontFactory from '../font_factory';\r\n\r\nconst isEqualFont = (font1, font2) => {\r\n  // compare font checksum\r\n  if (font1.font._tables?.head?.checkSumAdjustment !== font2.font._tables?.head?.checkSumAdjustment) {\r\n    return false;\r\n  }\r\n\r\n  // compare font name table\r\n  if (JSON.stringify(font1.font._tables?.name?.records) !== JSON.stringify(font2.font._tables?.name?.records)) {\r\n    return false;\r\n  }\r\n\r\n  return true;\r\n}\r\n\r\nexport default {\r\n  initFonts(defaultFont = 'Helvetica') {\r\n    // Lookup table for embedded fonts\r\n    this._fontFamilies = {};\r\n    this._fontCount = 0;\r\n\r\n    // Font state\r\n    this._fontSize = 12;\r\n    this._font = null;\r\n\r\n    this._registeredFonts = {};\r\n\r\n    // Set the default font\r\n    if (defaultFont) {\r\n      this.font(defaultFont);\r\n    }\r\n  },\r\n\r\n  font(src, family, size) {\r\n    let cacheKey, font;\r\n    if (typeof family === 'number') {\r\n      size = family;\r\n      family = null;\r\n    }\r\n\r\n    // check registered fonts if src is a string\r\n    if (typeof src === 'string' && this._registeredFonts[src]) {\r\n      cacheKey = src;\r\n      ({ src, family } = this._registeredFonts[src]);\r\n    } else {\r\n      cacheKey = family || src;\r\n      if (typeof cacheKey !== 'string') {\r\n        cacheKey = null;\r\n      }\r\n    }\r\n\r\n    if (size != null) {\r\n      this.fontSize(size);\r\n    }\r\n\r\n    // fast path: check if the font is already in the PDF\r\n    if ((font = this._fontFamilies[cacheKey])) {\r\n      this._font = font;\r\n      return this;\r\n    }\r\n\r\n    // load the font\r\n    const id = `F${++this._fontCount}`;\r\n    this._font = PDFFontFactory.open(this, src, family, id);\r\n\r\n    // check for existing font familes with the same name already in the PDF\r\n    // useful if the font was passed as a buffer\r\n    if ((font = this._fontFamilies[this._font.name]) && isEqualFont(this._font, font)) {\r\n      this._font = font;\r\n      return this;\r\n    }\r\n\r\n    // save the font for reuse later\r\n    if (cacheKey) {\r\n      this._fontFamilies[cacheKey] = this._font;\r\n    }\r\n\r\n    if (this._font.name) {\r\n      this._fontFamilies[this._font.name] = this._font;\r\n    }\r\n\r\n    return this;\r\n  },\r\n\r\n  fontSize(_fontSize) {\r\n    this._fontSize = _fontSize;\r\n    return this;\r\n  },\r\n\r\n  currentLineHeight(includeGap) {\r\n    if (includeGap == null) {\r\n      includeGap = false;\r\n    }\r\n    return this._font.lineHeight(this._fontSize, includeGap);\r\n  },\r\n\r\n  registerFont(name, src, family) {\r\n    this._registeredFonts[name] = {\r\n      src,\r\n      family\r\n    };\r\n\r\n    return this;\r\n  }\r\n};\r\n", "import { EventEmitter } from 'events';\r\nimport LineBreaker from '@foliojs-fork/linebreak';\r\n\r\nconst SOFT_HYPHEN = '\\u00AD';\r\nconst HYPHEN = '-';\r\n\r\nclass LineWrapper extends EventEmitter {\r\n  constructor(document, options) {\r\n    super();\r\n    this.document = document;\r\n    this.horizontalScaling = options.horizontalScaling || 100;\r\n    this.indent = ((options.indent || 0) * this.horizontalScaling) / 100;\r\n    this.characterSpacing = ((options.characterSpacing || 0) * this.horizontalScaling) / 100;\r\n    this.wordSpacing = ((options.wordSpacing === 0) * this.horizontalScaling) / 100;\r\n    this.columns = options.columns || 1;\r\n    this.columnGap = ((options.columnGap != null ? options.columnGap : 18) * this.horizontalScaling) / 100; // 1/4 inch\r\n    this.lineWidth = (((options.width * this.horizontalScaling) / 100) - (this.columnGap * (this.columns - 1))) / this.columns;\r\n    this.spaceLeft = this.lineWidth;\r\n    this.startX = this.document.x;\r\n    this.startY = this.document.y;\r\n    this.column = 1;\r\n    this.ellipsis = options.ellipsis;\r\n    this.continuedX = 0;\r\n    this.features = options.features;\r\n\r\n    // calculate the maximum Y position the text can appear at\r\n    if (options.height != null) {\r\n      this.height = options.height;\r\n      this.maxY = this.startY + options.height;\r\n    } else {\r\n      this.maxY = this.document.page.maxY();\r\n    }\r\n\r\n    // handle paragraph indents\r\n    this.on('firstLine', options => {\r\n      // if this is the first line of the text segment, and\r\n      // we're continuing where we left off, indent that much\r\n      // otherwise use the user specified indent option\r\n      const indent = this.continuedX || this.indent;\r\n      this.document.x += indent;\r\n      this.lineWidth -= indent;\r\n\r\n      // if indentAllLines is set to true\r\n      // we're not resetting the indentation for this paragraph after the first line\r\n      if (options.indentAllLines) {\r\n        return;\r\n      }\r\n\r\n      // otherwise we start the next line without indent\r\n      return this.once('line', () => {\r\n        this.document.x -= indent;\r\n        this.lineWidth += indent;\r\n        if (options.continued && !this.continuedX) {\r\n          this.continuedX = this.indent;\r\n        }\r\n        if (!options.continued) {\r\n          return (this.continuedX = 0);\r\n        }\r\n      });\r\n    });\r\n\r\n    // handle left aligning last lines of paragraphs\r\n    this.on('lastLine', options => {\r\n      const { align } = options;\r\n      if (align === 'justify') {\r\n        options.align = 'left';\r\n      }\r\n      this.lastLine = true;\r\n\r\n      return this.once('line', () => {\r\n        this.document.y += options.paragraphGap || 0;\r\n        options.align = align;\r\n        return (this.lastLine = false);\r\n      });\r\n    });\r\n  }\r\n\r\n  wordWidth(word) {\r\n    return (\r\n      this.document.widthOfString(word, this) +\r\n      this.characterSpacing +\r\n      this.wordSpacing\r\n    );\r\n  }\r\n\r\n  canFit(word, w) {\r\n    if (word[word.length - 1] != SOFT_HYPHEN) {\r\n      return w <= this.spaceLeft;\r\n    }\r\n    return w + this.wordWidth(HYPHEN) <= this.spaceLeft;\r\n  }\r\n\r\n  eachWord(text, fn) {\r\n    // setup a unicode line breaker\r\n    let bk;\r\n    const breaker = new LineBreaker(text);\r\n    let last = null;\r\n    const wordWidths = Object.create(null);\r\n\r\n    while ((bk = breaker.nextBreak())) {\r\n      var shouldContinue;\r\n      let word = text.slice(\r\n        (last != null ? last.position : undefined) || 0,\r\n        bk.position\r\n      );\r\n      let w =\r\n        wordWidths[word] != null\r\n          ? wordWidths[word]\r\n          : (wordWidths[word] = this.wordWidth(word));\r\n\r\n      // if the word is longer than the whole line, chop it up\r\n      // TODO: break by grapheme clusters, not JS string characters\r\n      if (w > this.lineWidth + this.continuedX) {\r\n        // make some fake break objects\r\n        let lbk = last;\r\n        const fbk = {};\r\n\r\n        while (word.length) {\r\n          // fit as much of the word as possible into the space we have\r\n          var l, mightGrow;\r\n          if (w > this.spaceLeft) {\r\n            // start our check at the end of our available space - this method is faster than a loop of each character and it resolves\r\n            // an issue with long loops when processing massive words, such as a huge number of spaces\r\n            l = Math.ceil(this.spaceLeft / (w / word.length));\r\n            w = this.wordWidth(word.slice(0, l));\r\n            mightGrow = w <= this.spaceLeft && l < word.length;\r\n          } else {\r\n            l = word.length;\r\n          }\r\n          let mustShrink = w > this.spaceLeft && l > 0;\r\n          // shrink or grow word as necessary after our near-guess above\r\n          while (mustShrink || mightGrow) {\r\n            if (mustShrink) {\r\n              w = this.wordWidth(word.slice(0, --l));\r\n              mustShrink = w > this.spaceLeft && l > 0;\r\n            } else {\r\n              w = this.wordWidth(word.slice(0, ++l));\r\n              mustShrink = w > this.spaceLeft && l > 0;\r\n              mightGrow = w <= this.spaceLeft && l < word.length;\r\n            }\r\n          }\r\n\r\n          // check for the edge case where a single character cannot fit into a line.\r\n          if (l === 0 && this.spaceLeft === this.lineWidth) {\r\n            l = 1;\r\n          }\r\n\r\n          // send a required break unless this is the last piece and a linebreak is not specified\r\n          fbk.required = bk.required || l < word.length;\r\n          shouldContinue = fn(word.slice(0, l), w, fbk, lbk);\r\n          lbk = { required: false };\r\n\r\n          // get the remaining piece of the word\r\n          word = word.slice(l);\r\n          w = this.wordWidth(word);\r\n\r\n          if (shouldContinue === false) {\r\n            break;\r\n          }\r\n        }\r\n      } else {\r\n        // otherwise just emit the break as it was given to us\r\n        shouldContinue = fn(word, w, bk, last);\r\n      }\r\n\r\n      if (shouldContinue === false) {\r\n        break;\r\n      }\r\n      last = bk;\r\n    }\r\n  }\r\n\r\n  wrap(text, options) {\r\n    // override options from previous continued fragments\r\n    this.horizontalScaling = options.horizontalScaling || 100;\r\n    if (options.indent != null) {\r\n      this.indent = (options.indent * this.horizontalScaling) / 100;\r\n    }\r\n    if (options.characterSpacing != null) {\r\n      this.characterSpacing = (options.characterSpacing * this.horizontalScaling) / 100;\r\n    }\r\n    if (options.wordSpacing != null) {\r\n      this.wordSpacing = (options.wordSpacing * this.horizontalScaling) / 100;\r\n    }\r\n    if (options.ellipsis != null) {\r\n      this.ellipsis = options.ellipsis;\r\n    }\r\n\r\n    // make sure we're actually on the page\r\n    // and that the first line of is never by\r\n    // itself at the bottom of a page (orphans)\r\n    const nextY = this.document.y + this.document.currentLineHeight(true);\r\n    if (this.document.y > this.maxY || nextY > this.maxY) {\r\n      this.nextSection();\r\n    }\r\n\r\n    let buffer = '';\r\n    let textWidth = 0;\r\n    let wc = 0;\r\n    let lc = 0;\r\n\r\n    let { y } = this.document; // used to reset Y pos if options.continued (below)\r\n    const emitLine = () => {\r\n      options.textWidth = textWidth + this.wordSpacing * (wc - 1);\r\n      options.wordCount = wc;\r\n      options.lineWidth = this.lineWidth;\r\n      ({ y } = this.document);\r\n      this.emit('line', buffer, options, this);\r\n      return lc++;\r\n    };\r\n\r\n    this.emit('sectionStart', options, this);\r\n\r\n    this.eachWord(text, (word, w, bk, last) => {\r\n      if (last == null || last.required) {\r\n        this.emit('firstLine', options, this);\r\n        this.spaceLeft = this.lineWidth;\r\n      }\r\n\r\n      if (this.canFit(word, w)) {\r\n        buffer += word;\r\n        textWidth += w;\r\n        wc++;\r\n      }\r\n\r\n      if (bk.required || !this.canFit(word, w)) {\r\n        // if the user specified a max height and an ellipsis, and is about to pass the\r\n        // max height and max columns after the next line, append the ellipsis\r\n        const lh = this.document.currentLineHeight(true);\r\n        if (\r\n          this.height != null &&\r\n          this.ellipsis &&\r\n          this.document.y + lh * 2 > this.maxY &&\r\n          this.column >= this.columns\r\n        ) {\r\n          if (this.ellipsis === true) {\r\n            this.ellipsis = '…';\r\n          } // map default ellipsis character\r\n          buffer = buffer.replace(/\\s+$/, '');\r\n          textWidth = this.wordWidth(buffer + this.ellipsis);\r\n\r\n          // remove characters from the buffer until the ellipsis fits\r\n          // to avoid infinite loop need to stop while-loop if buffer is empty string\r\n          while (buffer && textWidth > this.lineWidth) {\r\n            buffer = buffer.slice(0, -1).replace(/\\s+$/, '');\r\n            textWidth = this.wordWidth(buffer + this.ellipsis);\r\n          }\r\n          // need to add ellipsis only if there is enough space for it\r\n          if (textWidth <= this.lineWidth) {\r\n            buffer = buffer + this.ellipsis;\r\n          }\r\n\r\n          textWidth = this.wordWidth(buffer);\r\n        }\r\n\r\n        if (bk.required) {\r\n          if (w > this.spaceLeft) {\r\n            emitLine();\r\n            buffer = word;\r\n            textWidth = w;\r\n            wc = 1;\r\n          }\r\n\r\n          this.emit('lastLine', options, this);\r\n        }\r\n\r\n        // Previous entry is a soft hyphen - add visible hyphen.\r\n        if (buffer[buffer.length - 1] == SOFT_HYPHEN) {\r\n          buffer = buffer.slice(0, -1) + HYPHEN;\r\n          this.spaceLeft -= this.wordWidth(HYPHEN);\r\n        }\r\n\r\n        emitLine();\r\n\r\n        // if we've reached the edge of the page,\r\n        // continue on a new page or column\r\n        if (this.document.y + lh > this.maxY) {\r\n          const shouldContinue = this.nextSection();\r\n\r\n          // stop if we reached the maximum height\r\n          if (!shouldContinue) {\r\n            wc = 0;\r\n            buffer = '';\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // reset the space left and buffer\r\n        if (bk.required) {\r\n          this.spaceLeft = this.lineWidth;\r\n          buffer = '';\r\n          textWidth = 0;\r\n          return (wc = 0);\r\n        } else {\r\n          // reset the space left and buffer\r\n          this.spaceLeft = this.lineWidth - w;\r\n          buffer = word;\r\n          textWidth = w;\r\n          return (wc = 1);\r\n        }\r\n      } else {\r\n        return (this.spaceLeft -= w);\r\n      }\r\n    });\r\n\r\n    if (wc > 0) {\r\n      this.emit('lastLine', options, this);\r\n      emitLine();\r\n    }\r\n\r\n    this.emit('sectionEnd', options, this);\r\n\r\n    // if the wrap is set to be continued, save the X position\r\n    // to start the first line of the next segment at, and reset\r\n    // the y position\r\n    if (options.continued === true) {\r\n      if (lc > 1) {\r\n        this.continuedX = 0;\r\n      }\r\n      this.continuedX += options.textWidth || 0;\r\n      return (this.document.y = y);\r\n    } else {\r\n      return (this.document.x = this.startX);\r\n    }\r\n  }\r\n\r\n  nextSection(options) {\r\n    this.emit('sectionEnd', options, this);\r\n\r\n    if (++this.column > this.columns) {\r\n      // if a max height was specified by the user, we're done.\r\n      // otherwise, the default is to make a new page at the bottom.\r\n      if (this.height != null) {\r\n        return false;\r\n      }\r\n\r\n      this.document.continueOnNewPage();\r\n      this.column = 1;\r\n      this.startY = this.document.page.margins.top;\r\n      this.maxY = this.document.page.maxY();\r\n      this.document.x = this.startX;\r\n      if (this.document._fillColor) {\r\n        this.document.fillColor(...this.document._fillColor);\r\n      }\r\n      this.emit('pageBreak', options, this);\r\n    } else {\r\n      this.document.x += this.lineWidth + this.columnGap;\r\n      this.document.y = this.startY;\r\n      this.emit('columnBreak', options, this);\r\n    }\r\n\r\n    this.emit('sectionStart', options, this);\r\n    return true;\r\n  }\r\n}\r\n\r\nexport default LineWrapper;\r\n", "import LineWrapper from '../line_wrapper';\r\nimport PDFObject from '../object';\r\n\r\nconst { number } = PDFObject;\r\n\r\nexport default {\r\n  initText() {\r\n    this._line = this._line.bind(this);\r\n    // Current coordinates\r\n    this.x = 0;\r\n    this.y = 0;\r\n    return (this._lineGap = 0);\r\n  },\r\n\r\n  lineGap(_lineGap) {\r\n    this._lineGap = _lineGap;\r\n    return this;\r\n  },\r\n\r\n  moveDown(lines) {\r\n    if (lines == null) {\r\n      lines = 1;\r\n    }\r\n    this.y += this.currentLineHeight(true) * lines + this._lineGap;\r\n    return this;\r\n  },\r\n\r\n  moveUp(lines) {\r\n    if (lines == null) {\r\n      lines = 1;\r\n    }\r\n    this.y -= this.currentLineHeight(true) * lines + this._lineGap;\r\n    return this;\r\n  },\r\n\r\n  _text(text, x, y, options, lineCallback) {\r\n    options = this._initOptions(x, y, options);\r\n\r\n    // Convert text to a string\r\n    text = text == null ? '' : `${text}`;\r\n\r\n    // if the wordSpacing option is specified, remove multiple consecutive spaces\r\n    if (options.wordSpacing) {\r\n      text = text.replace(/\\s{2,}/g, ' ');\r\n    }\r\n\r\n    const addStructure = () => {\r\n      if (options.structParent) {\r\n        options.structParent.add(this.struct(options.structType || 'P',\r\n          [this.markStructureContent(options.structType || 'P')]));\r\n      }\r\n    };\r\n\r\n    // word wrapping\r\n    if (options.width) {\r\n      let wrapper = this._wrapper;\r\n      if (!wrapper) {\r\n        wrapper = new LineWrapper(this, options);\r\n        wrapper.on('line', lineCallback);\r\n        wrapper.on('firstLine', addStructure);\r\n      }\r\n\r\n      this._wrapper = options.continued ? wrapper : null;\r\n      this._textOptions = options.continued ? options : null;\r\n      wrapper.wrap(text, options);\r\n\r\n      // render paragraphs as single lines\r\n    } else {\r\n      for (let line of text.split('\\n')) {\r\n        addStructure();\r\n        lineCallback(line, options);\r\n      }\r\n    }\r\n\r\n    return this;\r\n  },\r\n\r\n  text(text, x, y, options) {\r\n    return this._text(text, x, y, options, this._line);\r\n  },\r\n\r\n  widthOfString(string, options = {}) {\r\n    const horizontalScaling = options.horizontalScaling || 100;\r\n    return ((this._font.widthOfString(string, this._fontSize, options.features) + (options.characterSpacing || 0) * (string.length - 1)) * horizontalScaling) / 100;\r\n  },\r\n\r\n  heightOfString(text, options) {\r\n    const { x, y } = this;\r\n\r\n    options = this._initOptions(options);\r\n    options.height = Infinity; // don't break pages\r\n\r\n    const lineGap = options.lineGap || this._lineGap || 0;\r\n    this._text(text, this.x, this.y, options, () => {\r\n      return (this.y += this.currentLineHeight(true) + lineGap);\r\n    });\r\n\r\n    const height = this.y - y;\r\n    this.x = x;\r\n    this.y = y;\r\n\r\n    return height;\r\n  },\r\n\r\n  list(list, x, y, options, wrapper) {\r\n    options = this._initOptions(x, y, options);\r\n\r\n    const listType = options.listType || 'bullet';\r\n    const unit = Math.round((this._font.ascender / 1000) * this._fontSize);\r\n    const midLine = unit / 2;\r\n    const r = options.bulletRadius || unit / 3;\r\n    const indent =\r\n      options.textIndent || (listType === 'bullet' ? r * 5 : unit * 2);\r\n    const itemIndent =\r\n      options.bulletIndent || (listType === 'bullet' ? r * 8 : unit * 2);\r\n\r\n    let level = 1;\r\n    const items = [];\r\n    const levels = [];\r\n    const numbers = [];\r\n\r\n    var flatten = function (list) {\r\n      let n = 1;\r\n      for (let i = 0; i < list.length; i++) {\r\n        const item = list[i];\r\n        if (Array.isArray(item)) {\r\n          level++;\r\n          flatten(item);\r\n          level--;\r\n        } else {\r\n          items.push(item);\r\n          levels.push(level);\r\n          if (listType !== 'bullet') {\r\n            numbers.push(n++);\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    flatten(list);\r\n\r\n    const label = function (n) {\r\n      switch (listType) {\r\n        case 'numbered':\r\n          return `${n}.`;\r\n        case 'lettered':\r\n          var letter = String.fromCharCode(((n - 1) % 26) + 65);\r\n          var times = Math.floor((n - 1) / 26 + 1);\r\n          var text = Array(times + 1).join(letter);\r\n          return `${text}.`;\r\n      }\r\n    };\r\n\r\n    const drawListItem = function (listItem, i) {\r\n      wrapper = new LineWrapper(this, options);\r\n      wrapper.on('line', this._line);\r\n\r\n      level = 1;\r\n      wrapper.once('firstLine', () => {\r\n        let item, itemType, labelType, bodyType;\r\n        if (options.structParent) {\r\n          if (options.structTypes) {\r\n            [itemType, labelType, bodyType] = options.structTypes;\r\n          } else {\r\n            [itemType, labelType, bodyType] = ['LI', 'Lbl', 'LBody'];\r\n          }\r\n        }\r\n\r\n        if (itemType) {\r\n          item = this.struct(itemType);\r\n          options.structParent.add(item);\r\n        } else if (options.structParent) {\r\n          item = options.structParent;\r\n        }\r\n\r\n        let l;\r\n        if ((l = levels[i++]) !== level) {\r\n          const diff = itemIndent * (l - level);\r\n          this.x += diff;\r\n          wrapper.lineWidth -= diff;\r\n          level = l;\r\n        }\r\n\r\n        if (item && (labelType || bodyType)) {\r\n          item.add(this.struct(labelType || bodyType,\r\n            [this.markStructureContent(labelType || bodyType)]));\r\n        }\r\n        switch (listType) {\r\n          case 'bullet':\r\n            this.circle(this.x - indent + r, this.y + midLine, r);\r\n            this.fill();\r\n            break;\r\n          case 'numbered':\r\n          case 'lettered':\r\n            var text = label(numbers[i - 1]);\r\n            this._fragment(text, this.x - indent, this.y, options);\r\n            break;\r\n        }\r\n\r\n        if (item && labelType && bodyType) {\r\n          item.add(this.struct(bodyType, [this.markStructureContent(bodyType)]));\r\n        }\r\n        if (item && item !== options.structParent) {\r\n          item.end();\r\n        }\r\n      });\r\n\r\n      wrapper.on('sectionStart', () => {\r\n        const pos = indent + itemIndent * (level - 1);\r\n        this.x += pos;\r\n        return (wrapper.lineWidth -= pos);\r\n      });\r\n\r\n      wrapper.on('sectionEnd', () => {\r\n        const pos = indent + itemIndent * (level - 1);\r\n        this.x -= pos;\r\n        return (wrapper.lineWidth += pos);\r\n      });\r\n\r\n      wrapper.wrap(listItem, options);\r\n    };\r\n\r\n\r\n    for (let i = 0; i < items.length; i++) {\r\n      drawListItem.call(this, items[i], i);\r\n    }\r\n\r\n    return this;\r\n  },\r\n\r\n  _initOptions(x = {}, y, options = {}) {\r\n    if (typeof x === 'object') {\r\n      options = x;\r\n      x = null;\r\n    }\r\n\r\n    // clone options object\r\n    const result = Object.assign({}, options);\r\n\r\n    // extend options with previous values for continued text\r\n    if (this._textOptions) {\r\n      for (let key in this._textOptions) {\r\n        const val = this._textOptions[key];\r\n        if (key !== 'continued') {\r\n          if (result[key] === undefined) {\r\n            result[key] = val;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // Update the current position\r\n    if (x != null) {\r\n      this.x = x;\r\n    }\r\n    if (y != null) {\r\n      this.y = y;\r\n    }\r\n\r\n    // wrap to margins if no x or y position passed\r\n    if (result.lineBreak !== false) {\r\n      if (result.width == null) {\r\n        result.width = this.page.width - this.x - this.page.margins.right;\r\n      }\r\n      result.width = Math.max(result.width, 0);\r\n    }\r\n\r\n    if (!result.columns) {\r\n      result.columns = 0;\r\n    }\r\n    if (result.columnGap == null) {\r\n      result.columnGap = 18;\r\n    } // 1/4 inch\r\n\r\n    return result;\r\n  },\r\n\r\n  _line(text, options = {}, wrapper) {\r\n    this._fragment(text, this.x, this.y, options);\r\n    const lineGap = options.lineGap || this._lineGap || 0;\r\n\r\n    if (!wrapper) {\r\n      return (this.x += this.widthOfString(text, options));\r\n    } else {\r\n      return (this.y += this.currentLineHeight(true) + lineGap);\r\n    }\r\n  },\r\n\r\n  _fragment(text, x, y, options) {\r\n    let dy, encoded, i, positions, textWidth, words;\r\n    text = `${text}`.replace(/\\n/g, '');\r\n    if (text.length === 0) {\r\n      return;\r\n    }\r\n\r\n    // handle options\r\n    const align = options.align || 'left';\r\n    let wordSpacing = options.wordSpacing || 0;\r\n    const characterSpacing = options.characterSpacing || 0;\r\n    const horizontalScaling = options.horizontalScaling || 100;\r\n\r\n    // text alignments\r\n    if (options.width) {\r\n      switch (align) {\r\n        case 'right':\r\n          textWidth = this.widthOfString(text.replace(/\\s+$/, ''), options);\r\n          x += options.lineWidth - textWidth;\r\n          break;\r\n\r\n        case 'center':\r\n          x += options.lineWidth / 2 - options.textWidth / 2;\r\n          break;\r\n\r\n        case 'justify':\r\n          // calculate the word spacing value\r\n          words = text.trim().split(/\\s+/);\r\n          textWidth = this.widthOfString(text.replace(/\\s+/g, ''), options);\r\n          var spaceWidth = this.widthOfString(' ') + characterSpacing;\r\n          wordSpacing = Math.max(\r\n            0,\r\n            (options.lineWidth - textWidth) / Math.max(1, words.length - 1) -\r\n            spaceWidth\r\n          );\r\n          break;\r\n      }\r\n    }\r\n\r\n    // text baseline alignments based on http://wiki.apache.org/xmlgraphics-fop/LineLayout/AlignmentHandling\r\n    if (typeof options.baseline === 'number') {\r\n      dy = -options.baseline;\r\n    } else {\r\n      switch (options.baseline) {\r\n        case 'svg-middle':\r\n          dy = 0.5 * this._font.xHeight;\r\n          break;\r\n        case 'middle':\r\n        case 'svg-central':\r\n          dy = 0.5 * (this._font.descender + this._font.ascender);\r\n          break;\r\n        case 'bottom':\r\n        case 'ideographic':\r\n          dy = this._font.descender;\r\n          break;\r\n        case 'alphabetic':\r\n          dy = 0;\r\n          break;\r\n        case 'mathematical':\r\n          dy = 0.5 * this._font.ascender;\r\n          break;\r\n        case 'hanging':\r\n          dy = 0.8 * this._font.ascender;\r\n          break;\r\n        case 'top':\r\n          dy = this._font.ascender;\r\n          break;\r\n        default:\r\n          dy = this._font.ascender;\r\n      }\r\n      dy = (dy / 1000) * this._fontSize;\r\n    }\r\n\r\n    // calculate the actual rendered width of the string after word and character spacing\r\n    const renderedWidth =\r\n      options.textWidth +\r\n      wordSpacing * (options.wordCount - 1) +\r\n      characterSpacing * (text.length - 1);\r\n\r\n    // create link annotations if the link option is given\r\n    if (options.link != null) {\r\n      this.link(x, y, renderedWidth, this.currentLineHeight(), options.link);\r\n    }\r\n    if (options.goTo != null) {\r\n      this.goTo(x, y, renderedWidth, this.currentLineHeight(), options.goTo);\r\n    }\r\n    if (options.destination != null) {\r\n      this.addNamedDestination(options.destination, 'XYZ', x, y, null);\r\n    }\r\n\r\n    // create underline\r\n    if (options.underline) {\r\n      this.save();\r\n      if (!options.stroke) {\r\n        this.strokeColor(...(this._fillColor || []));\r\n      }\r\n\r\n      const lineWidth =\r\n        this._fontSize < 10 ? 0.5 : Math.floor(this._fontSize / 10);\r\n      this.lineWidth(lineWidth);\r\n\r\n      let lineY = (y + this.currentLineHeight()) - lineWidth\r\n      this.moveTo(x, lineY);\r\n      this.lineTo(x + renderedWidth, lineY);\r\n      this.stroke();\r\n      this.restore();\r\n    }\r\n\r\n    // create strikethrough line\r\n    if (options.strike) {\r\n      this.save();\r\n      if (!options.stroke) {\r\n        this.strokeColor(...(this._fillColor || []));\r\n      }\r\n\r\n      const lineWidth =\r\n        this._fontSize < 10 ? 0.5 : Math.floor(this._fontSize / 10);\r\n      this.lineWidth(lineWidth);\r\n\r\n      let lineY = y + this.currentLineHeight() / 2;\r\n      this.moveTo(x, lineY);\r\n      this.lineTo(x + renderedWidth, lineY);\r\n      this.stroke();\r\n      this.restore();\r\n    }\r\n\r\n    this.save();\r\n\r\n    // oblique (angle in degrees or boolean)\r\n    if (options.oblique) {\r\n      let skew;\r\n      if (typeof options.oblique === 'number') {\r\n        skew = -Math.tan((options.oblique * Math.PI) / 180);\r\n      } else {\r\n        skew = -0.25;\r\n      }\r\n      this.transform(1, 0, 0, 1, x, y);\r\n      this.transform(1, 0, skew, 1, -skew * dy, 0);\r\n      this.transform(1, 0, 0, 1, -x, -y);\r\n    }\r\n\r\n    // flip coordinate system\r\n    this.transform(1, 0, 0, -1, 0, this.page.height);\r\n    y = this.page.height - y - dy;\r\n\r\n    // add current font to page if necessary\r\n    if (this.page.fonts[this._font.id] == null) {\r\n      this.page.fonts[this._font.id] = this._font.ref();\r\n    }\r\n\r\n    // begin the text object\r\n    this.addContent('BT');\r\n\r\n    // text position\r\n    this.addContent(`1 0 0 1 ${number(x)} ${number(y)} Tm`);\r\n\r\n    // font and font size\r\n    this.addContent(`/${this._font.id} ${number(this._fontSize)} Tf`);\r\n\r\n    // rendering mode\r\n    const mode = options.fill && options.stroke ? 2 : options.stroke ? 1 : 0;\r\n    if (mode) {\r\n      this.addContent(`${mode} Tr`);\r\n    }\r\n\r\n    // Character spacing\r\n    if (characterSpacing) {\r\n      this.addContent(`${number(characterSpacing)} Tc`);\r\n    }\r\n\r\n    // Horizontal scaling\r\n    if (horizontalScaling !== 100) {\r\n      this.addContent(`${horizontalScaling} Tz`);\r\n    }\r\n\r\n    // Add the actual text\r\n    // If we have a word spacing value, we need to encode each word separately\r\n    // since the normal Tw operator only works on character code 32, which isn't\r\n    // used for embedded fonts.\r\n    if (wordSpacing) {\r\n      words = text.trim().split(/\\s+/);\r\n      wordSpacing += this.widthOfString(' ') + characterSpacing;\r\n      wordSpacing *= 1000 / this._fontSize;\r\n\r\n      encoded = [];\r\n      positions = [];\r\n      for (let word of words) {\r\n        const [encodedWord, positionsWord] = this._font.encode(\r\n          word,\r\n          options.features\r\n        );\r\n        encoded = encoded.concat(encodedWord);\r\n        positions = positions.concat(positionsWord);\r\n\r\n        // add the word spacing to the end of the word\r\n        // clone object because of cache\r\n        const space = {};\r\n        const object = positions[positions.length - 1];\r\n        for (let key in object) {\r\n          const val = object[key];\r\n          space[key] = val;\r\n        }\r\n        space.xAdvance += wordSpacing;\r\n        positions[positions.length - 1] = space;\r\n      }\r\n    } else {\r\n      [encoded, positions] = this._font.encode(text, options.features);\r\n    }\r\n\r\n    const scale = this._fontSize / 1000;\r\n    const commands = [];\r\n    let last = 0;\r\n    let hadOffset = false;\r\n\r\n    // Adds a segment of text to the TJ command buffer\r\n    const addSegment = cur => {\r\n      if (last < cur) {\r\n        const hex = encoded.slice(last, cur).join('');\r\n        const advance =\r\n          positions[cur - 1].xAdvance - positions[cur - 1].advanceWidth;\r\n        commands.push(`<${hex}> ${number(-advance)}`);\r\n      }\r\n\r\n      return (last = cur);\r\n    };\r\n\r\n    // Flushes the current TJ commands to the output stream\r\n    const flush = i => {\r\n      addSegment(i);\r\n\r\n      if (commands.length > 0) {\r\n        this.addContent(`[${commands.join(' ')}] TJ`);\r\n        return (commands.length = 0);\r\n      }\r\n    };\r\n\r\n    for (i = 0; i < positions.length; i++) {\r\n      // If we have an x or y offset, we have to break out of the current TJ command\r\n      // so we can move the text position.\r\n      const pos = positions[i];\r\n      if (pos.xOffset || pos.yOffset) {\r\n        // Flush the current buffer\r\n        flush(i);\r\n\r\n        // Move the text position and flush just the current character\r\n        this.addContent(\r\n          `1 0 0 1 ${number(x + pos.xOffset * scale)} ${number(\r\n            y + pos.yOffset * scale\r\n          )} Tm`\r\n        );\r\n        flush(i + 1);\r\n\r\n        hadOffset = true;\r\n      } else {\r\n        // If the last character had an offset, reset the text position\r\n        if (hadOffset) {\r\n          this.addContent(`1 0 0 1 ${number(x)} ${number(y)} Tm`);\r\n          hadOffset = false;\r\n        }\r\n\r\n        // Group segments that don't have any advance adjustments\r\n        if (pos.xAdvance - pos.advanceWidth !== 0) {\r\n          addSegment(i + 1);\r\n        }\r\n      }\r\n\r\n      x += pos.xAdvance * scale;\r\n    }\r\n\r\n    // Flush any remaining commands\r\n    flush(i);\r\n\r\n    // end the text object\r\n    this.addContent('ET');\r\n\r\n    // restore flipped coordinate system\r\n    return this.restore();\r\n  }\r\n};\r\n", "import exif from 'jpeg-exif';\r\n\r\nconst MARKERS = [\r\n  0xffc0,\r\n  0xffc1,\r\n  0xffc2,\r\n  0xffc3,\r\n  0xffc5,\r\n  0xffc6,\r\n  0xffc7,\r\n  0xffc8,\r\n  0xffc9,\r\n  0xffca,\r\n  0xffcb,\r\n  0xffcc,\r\n  0xffcd,\r\n  0xffce,\r\n  0xffcf\r\n];\r\n\r\nconst COLOR_SPACE_MAP = {\r\n  1: 'DeviceGray',\r\n  3: 'DeviceRGB',\r\n  4: 'DeviceCMYK'\r\n};\r\n\r\nclass JPEG {\r\n  constructor(data, label) {\r\n    let marker;\r\n    this.data = data;\r\n    this.label = label;\r\n    if (this.data.readUInt16BE(0) !== 0xffd8) {\r\n      throw 'SOI not found in JPEG';\r\n    }\r\n\r\n    // Parse the EXIF orientation\r\n    this.orientation = exif.fromBuffer(this.data).Orientation || 1;\r\n\r\n    let pos = 2;\r\n    while (pos < this.data.length) {\r\n      marker = this.data.readUInt16BE(pos);\r\n      pos += 2;\r\n      if (MARKERS.includes(marker)) {\r\n        break;\r\n      }\r\n      pos += this.data.readUInt16BE(pos);\r\n    }\r\n\r\n    if (!MARKERS.includes(marker)) {\r\n      throw 'Invalid JPEG.';\r\n    }\r\n    pos += 2;\r\n\r\n    this.bits = this.data[pos++];\r\n    this.height = this.data.readUInt16BE(pos);\r\n    pos += 2;\r\n\r\n    this.width = this.data.readUInt16BE(pos);\r\n    pos += 2;\r\n\r\n    const channels = this.data[pos++];\r\n    this.colorSpace = COLOR_SPACE_MAP[channels];\r\n\r\n    this.obj = null;\r\n  }\r\n\r\n  embed(document) {\r\n    if (this.obj) {\r\n      return;\r\n    }\r\n\r\n    this.obj = document.ref({\r\n      Type: 'XObject',\r\n      Subtype: 'Image',\r\n      BitsPerComponent: this.bits,\r\n      Width: this.width,\r\n      Height: this.height,\r\n      ColorSpace: this.colorSpace,\r\n      Filter: 'DCTDecode'\r\n    });\r\n\r\n    // add extra decode params for CMYK images. By swapping the\r\n    // min and max values from the default, we invert the colors. See\r\n    // section 4.8.4 of the spec.\r\n    if (this.colorSpace === 'DeviceCMYK') {\r\n      this.obj.data['Decode'] = [1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0];\r\n    }\r\n\r\n    this.obj.end(this.data);\r\n\r\n    // free memory\r\n    return (this.data = null);\r\n  }\r\n}\r\n\r\nexport default JPEG;\r\n", "import zlib from 'zlib';\r\nimport PNG from 'png-js';\r\n\r\nclass PNGImage {\r\n  constructor(data, label) {\r\n    this.label = label;\r\n    this.image = new PNG(data);\r\n    this.width = this.image.width;\r\n    this.height = this.image.height;\r\n    this.imgData = this.image.imgData;\r\n    this.obj = null;\r\n  }\r\n\r\n  embed(document) {\r\n    let dataDecoded = false;\r\n\r\n    this.document = document;\r\n    if (this.obj) {\r\n      return;\r\n    }\r\n\r\n    const hasAlphaChannel = this.image.hasAlphaChannel;\r\n    const isInterlaced = this.image.interlaceMethod === 1;\r\n\r\n    this.obj = this.document.ref({\r\n      Type: 'XObject',\r\n      Subtype: 'Image',\r\n      BitsPerComponent: hasAlphaChannel ? 8 : this.image.bits,\r\n      Width: this.width,\r\n      Height: this.height,\r\n      Filter: 'FlateDecode'\r\n    });\r\n\r\n    if (!hasAlphaChannel) {\r\n      const params = this.document.ref({\r\n        Predictor: isInterlaced ? 1 : 15,\r\n        Colors: this.image.colors,\r\n        BitsPerComponent: this.image.bits,\r\n        Columns: this.width\r\n      });\r\n\r\n      this.obj.data['DecodeParms'] = params;\r\n      params.end();\r\n    }\r\n\r\n    if (this.image.palette.length === 0) {\r\n      this.obj.data['ColorSpace'] = this.image.colorSpace;\r\n    } else {\r\n      // embed the color palette in the PDF as an object stream\r\n      const palette = this.document.ref();\r\n      palette.end(Buffer.from(this.image.palette));\r\n\r\n      // build the color space array for the image\r\n      this.obj.data['ColorSpace'] = [\r\n        'Indexed',\r\n        'DeviceRGB',\r\n        this.image.palette.length / 3 - 1,\r\n        palette\r\n      ];\r\n    }\r\n\r\n    // For PNG color types 0, 2 and 3, the transparency data is stored in\r\n    // a dedicated PNG chunk.\r\n    if (this.image.transparency.grayscale != null) {\r\n      // Use Color Key Masking (spec section 4.8.5)\r\n      // An array with N elements, where N is two times the number of color components.\r\n      const val = this.image.transparency.grayscale;\r\n      this.obj.data['Mask'] = [val, val];\r\n    } else if (this.image.transparency.rgb) {\r\n      // Use Color Key Masking (spec section 4.8.5)\r\n      // An array with N elements, where N is two times the number of color components.\r\n      const { rgb } = this.image.transparency;\r\n      const mask = [];\r\n      for (let x of rgb) {\r\n        mask.push(x, x);\r\n      }\r\n\r\n      this.obj.data['Mask'] = mask;\r\n    } else if (this.image.transparency.indexed) {\r\n      // Create a transparency SMask for the image based on the data\r\n      // in the PLTE and tRNS sections. See below for details on SMasks.\r\n      dataDecoded = true;\r\n      return this.loadIndexedAlphaChannel();\r\n    } else if (hasAlphaChannel) {\r\n      // For PNG color types 4 and 6, the transparency data is stored as a alpha\r\n      // channel mixed in with the main image data. Separate this data out into an\r\n      // SMask object and store it separately in the PDF.\r\n      dataDecoded = true;\r\n      return this.splitAlphaChannel();\r\n    }\r\n\r\n    if (isInterlaced && !dataDecoded) {\r\n      return this.decodeData();\r\n    }\r\n\r\n    this.finalize();\r\n  }\r\n\r\n  finalize() {\r\n    if (this.alphaChannel) {\r\n      const sMask = this.document.ref({\r\n        Type: 'XObject',\r\n        Subtype: 'Image',\r\n        Height: this.height,\r\n        Width: this.width,\r\n        BitsPerComponent: 8,\r\n        Filter: 'FlateDecode',\r\n        ColorSpace: 'DeviceGray',\r\n        Decode: [0, 1]\r\n      });\r\n\r\n      sMask.end(this.alphaChannel);\r\n      this.obj.data['SMask'] = sMask;\r\n    }\r\n\r\n    // add the actual image data\r\n    this.obj.end(this.imgData);\r\n\r\n    // free memory\r\n    this.image = null;\r\n    return (this.imgData = null);\r\n  }\r\n\r\n  splitAlphaChannel() {\r\n    return this.image.decodePixels(pixels => {\r\n      let a, p;\r\n      const colorCount = this.image.colors;\r\n      const pixelCount = this.width * this.height;\r\n      const imgData = Buffer.alloc(pixelCount * colorCount);\r\n      const alphaChannel = Buffer.alloc(pixelCount);\r\n\r\n      let i = (p = a = 0);\r\n      const len = pixels.length;\r\n      // For 16bit images copy only most significant byte (MSB) - PNG data is always stored in network byte order (MSB first)\r\n      const skipByteCount = this.image.bits === 16 ? 1 : 0;\r\n      while (i < len) {\r\n        for (let colorIndex = 0; colorIndex < colorCount; colorIndex++) {\r\n          imgData[p++] = pixels[i++];\r\n          i += skipByteCount;\r\n        }\r\n        alphaChannel[a++] = pixels[i++];\r\n        i += skipByteCount;\r\n      }\r\n\r\n      this.imgData = zlib.deflateSync(imgData);\r\n      this.alphaChannel = zlib.deflateSync(alphaChannel);\r\n      return this.finalize();\r\n    });\r\n  }\r\n\r\n  loadIndexedAlphaChannel() {\r\n    const transparency = this.image.transparency.indexed;\r\n    return this.image.decodePixels(pixels => {\r\n      const alphaChannel = Buffer.alloc(this.width * this.height);\r\n\r\n      let i = 0;\r\n      for (let j = 0, end = pixels.length; j < end; j++) {\r\n        alphaChannel[i++] = transparency[pixels[j]];\r\n      }\r\n\r\n      this.alphaChannel = zlib.deflateSync(alphaChannel);\r\n      return this.finalize();\r\n    });\r\n  }\r\n\r\n  decodeData() {\r\n    this.image.decodePixels(pixels => {\r\n      this.imgData = zlib.deflateSync(pixels);\r\n      this.finalize();\r\n    });\r\n  }\r\n}\r\n\r\nexport default PNGImage;\r\n", "/*\r\nPDFImage - embeds images in PDF documents\r\nBy Devon Govett\r\n*/\r\n\r\nimport fs from 'fs';\r\nimport JPEG from './image/jpeg';\r\nimport PNG from './image/png';\r\n\r\nclass PDFImage {\r\n  static open(src, label) {\r\n    let data;\r\n    if (Buffer.isBuffer(src)) {\r\n      data = src;\r\n    } else if (src instanceof ArrayBuffer) {\r\n      data = Buffer.from(new Uint8Array(src));\r\n    } else {\r\n      let match;\r\n      if ((match = /^data:.+?;base64,(.*)$/.exec(src))) {\r\n        data = Buffer.from(match[1], 'base64');\r\n      } else {\r\n        data = fs.readFileSync(src);\r\n        if (!data) {\r\n          return;\r\n        }\r\n      }\r\n    }\r\n\r\n    if (data[0] === 0xff && data[1] === 0xd8) {\r\n      return new JPEG(data, label);\r\n    } else if (data[0] === 0x89 && data.toString('ascii', 1, 4) === 'PNG') {\r\n      return new PNG(data, label);\r\n    } else {\r\n      throw new Error('Unknown image format.');\r\n    }\r\n  }\r\n}\r\n\r\nexport default PDFImage;\r\n", "import PDFImage from '../image';\r\n\r\nexport default {\r\n  initImages() {\r\n    this._imageRegistry = {};\r\n    return (this._imageCount = 0);\r\n  },\r\n\r\n  image(src, x, y, options = {}) {\r\n    let bh, bp, bw, image, ip, left, left1, rotateAngle, originX, originY;\r\n    if (typeof x === 'object') {\r\n      options = x;\r\n      x = null;\r\n    }\r\n\r\n    // Ignore orientation based on document options or image options\r\n    const ignoreOrientation =\r\n      options.ignoreOrientation ||\r\n      (options.ignoreOrientation !== false && this.options.ignoreOrientation);\r\n\r\n    x = (left = x != null ? x : options.x) != null ? left : this.x;\r\n    y = (left1 = y != null ? y : options.y) != null ? left1 : this.y;\r\n\r\n    if (typeof src === 'string') {\r\n      image = this._imageRegistry[src];\r\n    }\r\n\r\n    if (!image) {\r\n      if (src.width && src.height) {\r\n        image = src;\r\n      } else {\r\n        image = this.openImage(src);\r\n      }\r\n    }\r\n\r\n    if (!image.obj) {\r\n      image.embed(this);\r\n    }\r\n\r\n    if (this.page.xobjects[image.label] == null) {\r\n      this.page.xobjects[image.label] = image.obj;\r\n    }\r\n\r\n    let { width, height } = image;\r\n\r\n    // If EXIF orientation calls for it, swap width and height\r\n    if (!ignoreOrientation && image.orientation > 4) {\r\n      [width, height] = [height, width];\r\n    }\r\n\r\n    let w = options.width || width;\r\n    let h = options.height || height;\r\n\r\n    if (options.width && !options.height) {\r\n      const wp = w / width;\r\n      w = width * wp;\r\n      h = height * wp;\r\n    } else if (options.height && !options.width) {\r\n      const hp = h / height;\r\n      w = width * hp;\r\n      h = height * hp;\r\n    } else if (options.scale) {\r\n      w = width * options.scale;\r\n      h = height * options.scale;\r\n    } else if (options.fit) {\r\n      [bw, bh] = options.fit;\r\n      bp = bw / bh;\r\n      ip = width / height;\r\n      if (ip > bp) {\r\n        w = bw;\r\n        h = bw / ip;\r\n      } else {\r\n        h = bh;\r\n        w = bh * ip;\r\n      }\r\n    } else if (options.cover) {\r\n      [bw, bh] = options.cover;\r\n      bp = bw / bh;\r\n      ip = width / height;\r\n      if (ip > bp) {\r\n        h = bh;\r\n        w = bh * ip;\r\n      } else {\r\n        w = bw;\r\n        h = bw / ip;\r\n      }\r\n    }\r\n\r\n    if (options.fit || options.cover) {\r\n      if (options.align === 'center') {\r\n        x = x + bw / 2 - w / 2;\r\n      } else if (options.align === 'right') {\r\n        x = x + bw - w;\r\n      }\r\n\r\n      if (options.valign === 'center') {\r\n        y = y + bh / 2 - h / 2;\r\n      } else if (options.valign === 'bottom') {\r\n        y = y + bh - h;\r\n      }\r\n    }\r\n\r\n    if (!ignoreOrientation) {\r\n      switch (image.orientation) {\r\n        // No orientation (need to flip image, though, because of the default transform matrix on the document)\r\n        default:\r\n        case 1:\r\n          h = -h;\r\n          y -= h;\r\n\r\n          rotateAngle = 0;\r\n          break;\r\n        // Flip Horizontal\r\n        case 2:\r\n          w = -w;\r\n          h = -h;\r\n          x -= w;\r\n          y -= h;\r\n\r\n          rotateAngle = 0;\r\n          break;\r\n        // Rotate 180 degrees\r\n        case 3:\r\n          originX = x;\r\n          originY = y;\r\n\r\n          h = -h;\r\n          x -= w;\r\n\r\n          rotateAngle = 180;\r\n          break;\r\n        // Flip vertical\r\n        case 4:\r\n          // Do nothing, image will be flipped\r\n\r\n          break;\r\n        // Flip horizontally and rotate 270 degrees CW\r\n        case 5:\r\n          originX = x;\r\n          originY = y;\r\n\r\n          [w, h] = [h, w];\r\n          y -= h;\r\n\r\n          rotateAngle = 90;\r\n          break;\r\n        // Rotate 90 degrees CW\r\n        case 6:\r\n          originX = x;\r\n          originY = y;\r\n\r\n          [w, h] = [h, w];\r\n          h = -h;\r\n\r\n          rotateAngle = 90;\r\n          break;\r\n        // Flip horizontally and rotate 90 degrees CW\r\n        case 7:\r\n          originX = x;\r\n          originY = y;\r\n\r\n          [w, h] = [h, w];\r\n          h = -h;\r\n          w = -w;\r\n          x -= w;\r\n\r\n          rotateAngle = 90;\r\n          break;\r\n        // Rotate 270 degrees CW\r\n        case 8:\r\n          originX = x;\r\n          originY = y;\r\n\r\n          [w, h] = [h, w];\r\n          h = -h;\r\n          x -= w;\r\n          y -= h;\r\n\r\n          rotateAngle = -90;\r\n          break;\r\n      }\r\n    } else {\r\n      h = -h;\r\n      y -= h;\r\n      rotateAngle = 0;\r\n    }\r\n\r\n    // create link annotations if the link option is given\r\n    if (options.link != null) {\r\n      this.link(x, y, w, h, options.link);\r\n    }\r\n    if (options.goTo != null) {\r\n      this.goTo(x, y, w, h, options.goTo);\r\n    }\r\n    if (options.destination != null) {\r\n      this.addNamedDestination(options.destination, 'XYZ', x, y, null);\r\n    }\r\n\r\n    // Set the current y position to below the image if it is in the document flow\r\n    if (this.y === y) {\r\n      this.y += h;\r\n    }\r\n\r\n    this.save();\r\n\r\n    if (rotateAngle) {\r\n      this.rotate(rotateAngle, {\r\n        origin: [originX, originY]\r\n      });\r\n    }\r\n\r\n    this.transform(w, 0, 0, h, x, y);\r\n    this.addContent(`/${image.label} Do`);\r\n    this.restore();\r\n\r\n    return this;\r\n  },\r\n\r\n  openImage(src) {\r\n    let image;\r\n    if (typeof src === 'string') {\r\n      image = this._imageRegistry[src];\r\n    }\r\n\r\n    if (!image) {\r\n      image = PDFImage.open(src, `I${++this._imageCount}`);\r\n      if (typeof src === 'string') {\r\n        this._imageRegistry[src] = image;\r\n      }\r\n    }\r\n\r\n    return image;\r\n  }\r\n};\r\n", "export default {\r\n  annotate(x, y, w, h, options) {\r\n    options.Type = 'Annot';\r\n    options.Rect = this._convertRect(x, y, w, h);\r\n    options.Border = [0, 0, 0];\r\n\r\n    if (options.Subtype === 'Link' && typeof options.F === 'undefined') {\r\n      options.F = 1 << 2; // Print Annotation Flag\r\n    }\r\n\r\n    if (options.Subtype !== 'Link') {\r\n      if (options.C == null) {\r\n        options.C = this._normalizeColor(options.color || [0, 0, 0]);\r\n      }\r\n    } // convert colors\r\n    delete options.color;\r\n\r\n    if (typeof options.Dest === 'string') {\r\n      options.Dest = new String(options.Dest);\r\n    }\r\n\r\n    // Capitalize keys\r\n    for (let key in options) {\r\n      const val = options[key];\r\n      options[key[0].toUpperCase() + key.slice(1)] = val;\r\n    }\r\n\r\n    const ref = this.ref(options);\r\n    this.page.annotations.push(ref);\r\n    ref.end();\r\n    return this;\r\n  },\r\n\r\n  note(x, y, w, h, contents, options = {}) {\r\n    options.Subtype = 'Text';\r\n    options.Contents = new String(contents);\r\n    if (options.Name == null) {\r\n      options.Name = 'Comment';\r\n    }\r\n    if (options.color == null) {\r\n      options.color = [243, 223, 92];\r\n    }\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  goTo(x, y, w, h, name, options = {}) {\r\n    options.Subtype = 'Link';\r\n    options.A = this.ref({\r\n      S: 'GoTo',\r\n      D: new String(name)\r\n    });\r\n    options.A.end();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  link(x, y, w, h, url, options = {}) {\r\n    options.Subtype = 'Link';\r\n\r\n    if (typeof url === 'number') {\r\n      // Link to a page in the document (the page must already exist)\r\n      const pages = this._root.data.Pages.data;\r\n      if (url >= 0 && url < pages.Kids.length) {\r\n        options.A = this.ref({\r\n          S: 'GoTo',\r\n          D: [pages.Kids[url], 'XYZ', null, null, null]\r\n        });\r\n        options.A.end();\r\n      } else {\r\n        throw new Error(`The document has no page ${url}`);\r\n      }\r\n    } else {\r\n      // Link to an external url\r\n      options.A = this.ref({\r\n        S: 'URI',\r\n        URI: new String(url)\r\n      });\r\n      options.A.end();\r\n    }\r\n\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  _markup(x, y, w, h, options = {}) {\r\n    const [x1, y1, x2, y2] = this._convertRect(x, y, w, h);\r\n    options.QuadPoints = [x1, y2, x2, y2, x1, y1, x2, y1];\r\n    options.Contents = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  highlight(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Highlight';\r\n    if (options.color == null) {\r\n      options.color = [241, 238, 148];\r\n    }\r\n    return this._markup(x, y, w, h, options);\r\n  },\r\n\r\n  underline(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Underline';\r\n    return this._markup(x, y, w, h, options);\r\n  },\r\n\r\n  strike(x, y, w, h, options = {}) {\r\n    options.Subtype = 'StrikeOut';\r\n    return this._markup(x, y, w, h, options);\r\n  },\r\n\r\n  lineAnnotation(x1, y1, x2, y2, options = {}) {\r\n    options.Subtype = 'Line';\r\n    options.Contents = new String();\r\n    options.L = [x1, this.page.height - y1, x2, this.page.height - y2];\r\n    return this.annotate(x1, y1, x2, y2, options);\r\n  },\r\n\r\n  rectAnnotation(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Square';\r\n    options.Contents = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  ellipseAnnotation(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Circle';\r\n    options.Contents = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  textAnnotation(x, y, w, h, text, options = {}) {\r\n    options.Subtype = 'FreeText';\r\n    options.Contents = new String(text);\r\n    options.DA = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  fileAnnotation(x, y, w, h, file = {}, options = {}) {\r\n    // create hidden file\r\n    const filespec = this.file(\r\n      file.src,\r\n      Object.assign({ hidden: true }, file)\r\n    );\r\n\r\n    options.Subtype = 'FileAttachment';\r\n    options.FS = filespec;\r\n\r\n    // add description from filespec unless description (Contents) has already been set\r\n    if (options.Contents) {\r\n      options.Contents = new String(options.Contents);\r\n    } else if (filespec.data.Desc) {\r\n      options.Contents = filespec.data.Desc;\r\n    }\r\n\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  _convertRect(x1, y1, w, h) {\r\n    // flip y1 and y2\r\n    let y2 = y1;\r\n    y1 += h;\r\n\r\n    // make x2\r\n    let x2 = x1 + w;\r\n\r\n    // apply current transformation matrix to points\r\n    const [m0, m1, m2, m3, m4, m5] = this._ctm;\r\n    x1 = m0 * x1 + m2 * y1 + m4;\r\n    y1 = m1 * x1 + m3 * y1 + m5;\r\n    x2 = m0 * x2 + m2 * y2 + m4;\r\n    y2 = m1 * x2 + m3 * y2 + m5;\r\n\r\n    return [x1, y1, x2, y2];\r\n  }\r\n};\r\n", "class PDFOutline {\r\n  constructor(document, parent, title, dest, options = { expanded: false }) {\r\n    this.document = document;\r\n    this.options = options;\r\n    this.outlineData = {};\r\n\r\n    if (dest !== null) {\r\n      this.outlineData['Dest'] = [dest.dictionary, 'Fit'];\r\n    }\r\n\r\n    if (parent !== null) {\r\n      this.outlineData['Parent'] = parent;\r\n    }\r\n\r\n    if (title !== null) {\r\n      this.outlineData['Title'] = new String(title);\r\n    }\r\n\r\n    this.dictionary = this.document.ref(this.outlineData);\r\n    this.children = [];\r\n  }\r\n\r\n  addItem(title, options = { expanded: false }) {\r\n    const result = new PDFOutline(\r\n      this.document,\r\n      this.dictionary,\r\n      title,\r\n      this.document.page,\r\n      options\r\n    );\r\n    this.children.push(result);\r\n\r\n    return result;\r\n  }\r\n\r\n  endOutline() {\r\n    if (this.children.length > 0) {\r\n      if (this.options.expanded) {\r\n        this.outlineData.Count = this.children.length;\r\n      }\r\n\r\n      const first = this.children[0],\r\n        last = this.children[this.children.length - 1];\r\n      this.outlineData.First = first.dictionary;\r\n      this.outlineData.Last = last.dictionary;\r\n\r\n      for (let i = 0, len = this.children.length; i < len; i++) {\r\n        const child = this.children[i];\r\n        if (i > 0) {\r\n          child.outlineData.Prev = this.children[i - 1].dictionary;\r\n        }\r\n        if (i < this.children.length - 1) {\r\n          child.outlineData.Next = this.children[i + 1].dictionary;\r\n        }\r\n        child.endOutline();\r\n      }\r\n    }\r\n\r\n    return this.dictionary.end();\r\n  }\r\n}\r\n\r\nexport default PDFOutline;\r\n", "import PDFOutline from '../outline';\r\n\r\nexport default {\r\n  initOutline() {\r\n    return (this.outline = new PDFOutline(this, null, null, null));\r\n  },\r\n\r\n  endOutline() {\r\n    this.outline.endOutline();\r\n    if (this.outline.children.length > 0) {\r\n      this._root.data.Outlines = this.outline.dictionary;\r\n      return (this._root.data.PageMode = 'UseOutlines');\r\n    }\r\n  }\r\n};\r\n", "/*\r\nPDFStructureContent - a reference to a marked structure content\r\nBy <PERSON>\r\n*/\r\n\r\nclass PDFStructureContent {\r\n  constructor(pageRef, mcid) {\r\n    this.refs = [{ pageRef, mcid }];\r\n  }\r\n\r\n  push(structContent) {\r\n    structContent.refs.forEach((ref) => this.refs.push(ref));\r\n  }\r\n}\r\n\r\nexport default PDFStructureContent;\r\n", "/*\r\nPDFStructureElement - represents an element in the PDF logical structure tree\r\nBy <PERSON>\r\n*/\r\n\r\nimport PDFStructureContent from \"./structure_content\";\r\n\r\nclass PDFStructureElement {\r\n  constructor(document, type, options = {}, children = null) {\r\n    this.document = document;\r\n\r\n    this._attached = false;\r\n    this._ended = false;\r\n    this._flushed = false;\r\n    this.dictionary = document.ref({\r\n      // Type: \"StructElem\",\r\n      S: type\r\n    });\r\n\r\n    const data = this.dictionary.data;\r\n\r\n    if (Array.isArray(options) || this._isValidChild(options)) {\r\n      children = options;\r\n      options = {};\r\n    }\r\n\r\n    if (typeof options.title !== 'undefined') {\r\n      data.T = new String(options.title);\r\n    }\r\n    if (typeof options.lang !== 'undefined') {\r\n      data.Lang = new String(options.lang);\r\n    }\r\n    if (typeof options.alt !== 'undefined') {\r\n      data.Alt = new String(options.alt);\r\n    }\r\n    if (typeof options.expanded !== 'undefined') {\r\n      data.E = new String(options.expanded);\r\n    }\r\n    if (typeof options.actual !== 'undefined') {\r\n      data.ActualText = new String(options.actual);\r\n    }\r\n\r\n    this._children = [];\r\n\r\n    if (children) {\r\n      if (!Array.isArray(children)) {\r\n        children = [children];\r\n      }\r\n      children.forEach((child) => this.add(child));\r\n      this.end();\r\n    }\r\n  }\r\n\r\n  add(child) {\r\n    if (this._ended) {\r\n      throw new Error(`Cannot add child to already-ended structure element`);\r\n    }\r\n\r\n    if (!this._isValidChild(child)) {\r\n      throw new Error(`Invalid structure element child`);\r\n    }\r\n\r\n    if (child instanceof PDFStructureElement) {\r\n      child.setParent(this.dictionary);\r\n      if (this._attached) {\r\n        child.setAttached();\r\n      }\r\n    }\r\n\r\n    if (child instanceof PDFStructureContent) {\r\n      this._addContentToParentTree(child);\r\n    }\r\n\r\n    if (typeof child === 'function' && this._attached) {\r\n      // _contentForClosure() adds the content to the parent tree\r\n      child = this._contentForClosure(child);\r\n    }\r\n\r\n    this._children.push(child);\r\n\r\n    return this;\r\n  }\r\n\r\n  _addContentToParentTree(content) {\r\n    content.refs.forEach(({ pageRef, mcid }) => {\r\n      const pageStructParents = this.document.getStructParentTree()\r\n        .get(pageRef.data.StructParents);\r\n      pageStructParents[mcid] = this.dictionary;\r\n    });\r\n  }\r\n\r\n  setParent(parentRef) {\r\n    if (this.dictionary.data.P) {\r\n      throw new Error(`Structure element added to more than one parent`);\r\n    }\r\n\r\n    this.dictionary.data.P = parentRef;\r\n\r\n    this._flush();\r\n  }\r\n\r\n  setAttached() {\r\n    if (this._attached) {\r\n      return;\r\n    }\r\n\r\n    this._children.forEach((child, index) => {\r\n      if (child instanceof PDFStructureElement) {\r\n        child.setAttached();\r\n      }\r\n      if (typeof child === 'function') {\r\n        this._children[index] = this._contentForClosure(child);\r\n      }\r\n    });\r\n\r\n    this._attached = true;\r\n\r\n    this._flush();\r\n  }\r\n\r\n  end() {\r\n    if (this._ended) {\r\n      return;\r\n    }\r\n\r\n    this._children\r\n      .filter((child) => child instanceof PDFStructureElement)\r\n      .forEach((child) => child.end());\r\n\r\n    this._ended = true;\r\n\r\n    this._flush();\r\n  }\r\n\r\n  _isValidChild(child) {\r\n    return child instanceof PDFStructureElement ||\r\n        child instanceof PDFStructureContent ||\r\n        typeof child === 'function';\r\n  }\r\n\r\n  _contentForClosure(closure) {\r\n    const content = this.document.markStructureContent(this.dictionary.data.S);\r\n    closure();\r\n    this.document.endMarkedContent();\r\n\r\n    this._addContentToParentTree(content);\r\n\r\n    return content;\r\n  }\r\n\r\n  _isFlushable() {\r\n    if (!this.dictionary.data.P || !this._ended) {\r\n      return false;\r\n    }\r\n\r\n    return this._children.every((child) => {\r\n      if (typeof child === 'function') {\r\n        return false;\r\n      }\r\n      if (child instanceof PDFStructureElement) {\r\n        return child._isFlushable();\r\n      }\r\n      return true;\r\n    });\r\n  }\r\n\r\n  _flush() {\r\n    if (this._flushed || !this._isFlushable()) {\r\n      return;\r\n    }\r\n\r\n    this.dictionary.data.K = [];\r\n\r\n    this._children.forEach((child) => this._flushChild(child));\r\n\r\n    this.dictionary.end();\r\n\r\n    // free memory used by children; the dictionary itself may still be\r\n    // referenced by a parent structure element or root, but we can\r\n    // at least trim the tree here\r\n    this._children = [];\r\n    this.dictionary.data.K = null;\r\n\r\n    this._flushed = true;\r\n  }\r\n\r\n  _flushChild(child) {\r\n    if (child instanceof PDFStructureElement) {\r\n      this.dictionary.data.K.push(child.dictionary);\r\n    }\r\n\r\n    if (child instanceof PDFStructureContent) {\r\n      child.refs.forEach(({ pageRef, mcid }) => {\r\n        if (!this.dictionary.data.Pg) {\r\n          this.dictionary.data.Pg = pageRef;\r\n        }\r\n\r\n        if (this.dictionary.data.Pg === pageRef) {\r\n          this.dictionary.data.K.push(mcid);\r\n        } else {\r\n          this.dictionary.data.K.push({\r\n            Type: \"MCR\",\r\n            Pg: pageRef,\r\n            MCID: mcid\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n\r\nexport default PDFStructureElement;\r\n", "/*\r\nPDFNumberTree - represents a number tree object\r\n*/\r\n\r\nimport PDFTree from \"./tree\";\r\n\r\nclass PDFNumberTree extends PDFTree {\r\n  _compareKeys(a, b) {\r\n    return parseInt(a) - parseInt(b);\r\n  }\r\n\r\n  _keysName() {\r\n    return \"Nums\";\r\n  }\r\n\r\n  _dataForKey(k) {\r\n    return parseInt(k);\r\n  }\r\n}\r\n\r\nexport default PDFNumberTree;\r\n", "/*\r\nMarkings mixin - support marked content sequences in content streams\r\nBy <PERSON>\r\n*/\r\n\r\nimport PDFStructureElement from \"../structure_element\";\r\nimport PDFStructureContent from \"../structure_content\";\r\nimport PDFNumberTree from \"../number_tree\";\r\nimport PDFObject from \"../object\";\r\n\r\nexport default {\r\n\r\n  initMarkings(options) {\r\n    this.structChildren = [];\r\n\r\n    if (options.tagged) {\r\n      this.getMarkInfoDictionary().data.Marked = true;\r\n      this.getStructTreeRoot();\r\n    }\r\n  },\r\n\r\n  markContent(tag, options = null) {\r\n    if (tag === 'Artifact' || (options && options.mcid)) {\r\n      let toClose = 0;\r\n      this.page.markings.forEach((marking) => {\r\n        if (toClose || marking.structContent || marking.tag === 'Artifact') {\r\n          toClose++;\r\n        }\r\n      });\r\n      while (toClose--) {\r\n        this.endMarkedContent();\r\n      }\r\n    }\r\n\r\n    if (!options) {\r\n      this.page.markings.push({ tag });\r\n      this.addContent(`/${tag} BMC`);\r\n      return this;\r\n    }\r\n\r\n    this.page.markings.push({ tag, options });\r\n\r\n    const dictionary = {};\r\n\r\n    if (typeof options.mcid !== 'undefined') {\r\n      dictionary.MCID = options.mcid;\r\n    }\r\n    if (tag === 'Artifact') {\r\n      if (typeof options.type === 'string') {\r\n        dictionary.Type = options.type;\r\n      }\r\n      if (Array.isArray(options.bbox)) {\r\n        dictionary.BBox = [options.bbox[0], this.page.height - options.bbox[3],\r\n          options.bbox[2], this.page.height - options.bbox[1]];\r\n      }\r\n      if (Array.isArray(options.attached) &&\r\n        options.attached.every(val => typeof val === 'string')) {\r\n        dictionary.Attached = options.attached;\r\n      }\r\n    }\r\n    if (tag === 'Span') {\r\n      if (options.lang) {\r\n        dictionary.Lang = new String(options.lang);\r\n      }\r\n      if (options.alt) {\r\n        dictionary.Alt = new String(options.alt);\r\n      }\r\n      if (options.expanded) {\r\n        dictionary.E = new String(options.expanded);\r\n      }\r\n      if (options.actual) {\r\n        dictionary.ActualText = new String(options.actual);\r\n      }\r\n    }\r\n\r\n    this.addContent(`/${tag} ${PDFObject.convert(dictionary)} BDC`);\r\n    return this;\r\n  },\r\n\r\n  markStructureContent(tag, options = {}) {\r\n    const pageStructParents = this.getStructParentTree().get(this.page.structParentTreeKey);\r\n    const mcid = pageStructParents.length;\r\n    pageStructParents.push(null);\r\n\r\n    this.markContent(tag, { ...options, mcid });\r\n\r\n    const structContent = new PDFStructureContent(this.page.dictionary, mcid);\r\n    this.page.markings.slice(-1)[0].structContent = structContent;\r\n    return structContent;\r\n  },\r\n\r\n  endMarkedContent() {\r\n    this.page.markings.pop();\r\n    this.addContent('EMC');\r\n    return this;\r\n  },\r\n\r\n  struct(type, options = {}, children = null) {\r\n    return new PDFStructureElement(this, type, options, children);\r\n  },\r\n\r\n  addStructure(structElem) {\r\n    const structTreeRoot = this.getStructTreeRoot();\r\n    structElem.setParent(structTreeRoot);\r\n    structElem.setAttached();\r\n    this.structChildren.push(structElem);\r\n    if (!structTreeRoot.data.K) {\r\n      structTreeRoot.data.K = [];\r\n    }\r\n    structTreeRoot.data.K.push(structElem.dictionary);\r\n    return this;\r\n  },\r\n\r\n  initPageMarkings(pageMarkings) {\r\n    pageMarkings.forEach((marking) => {\r\n      if (marking.structContent) {\r\n        const structContent = marking.structContent;\r\n        const newStructContent = this.markStructureContent(marking.tag, marking.options);\r\n        structContent.push(newStructContent);\r\n        this.page.markings.slice(-1)[0].structContent = structContent;\r\n      } else {\r\n        this.markContent(marking.tag, marking.options);\r\n      }\r\n    });\r\n  },\r\n\r\n  endPageMarkings(page) {\r\n    const pageMarkings = page.markings;\r\n    pageMarkings.forEach(() => page.write('EMC'));\r\n    page.markings = [];\r\n    return pageMarkings;\r\n  },\r\n\r\n  getMarkInfoDictionary() {\r\n    if (!this._root.data.MarkInfo) {\r\n      this._root.data.MarkInfo = this.ref({});\r\n    }\r\n    return this._root.data.MarkInfo;\r\n  },\r\n\r\n  hasMarkInfoDictionary() {\r\n    return !!this._root.data.MarkInfo;\r\n  },\r\n\r\n  getStructTreeRoot() {\r\n    if (!this._root.data.StructTreeRoot) {\r\n      this._root.data.StructTreeRoot = this.ref({\r\n        Type: 'StructTreeRoot',\r\n        ParentTree: new PDFNumberTree(),\r\n        ParentTreeNextKey: 0\r\n      });\r\n    }\r\n    return this._root.data.StructTreeRoot;\r\n  },\r\n\r\n  getStructParentTree() {\r\n    return this.getStructTreeRoot().data.ParentTree;\r\n  },\r\n\r\n  createStructParentTreeNextKey() {\r\n    // initialise the MarkInfo dictionary\r\n    this.getMarkInfoDictionary();\r\n\r\n    const structTreeRoot = this.getStructTreeRoot();\r\n    const key = structTreeRoot.data.ParentTreeNextKey++;\r\n    structTreeRoot.data.ParentTree.add(key, []);\r\n    return key;\r\n  },\r\n\r\n  endMarkings() {\r\n    const structTreeRoot = this._root.data.StructTreeRoot;\r\n    if (structTreeRoot) {\r\n      structTreeRoot.end();\r\n      this.structChildren.forEach((structElem) => structElem.end());\r\n    }\r\n    if (this._root.data.MarkInfo) {\r\n      this._root.data.MarkInfo.end();\r\n    }\r\n  }\r\n\r\n};\r\n", "const FIELD_FLAGS = {\r\n  readOnly: 1,\r\n  required: 2,\r\n  noExport: 4,\r\n  multiline: 0x1000,\r\n  password: 0x2000,\r\n  toggleToOffButton: 0x4000,\r\n  radioButton: 0x8000,\r\n  pushButton: 0x10000,\r\n  combo: 0x20000,\r\n  edit: 0x40000,\r\n  sort: 0x80000,\r\n  multiSelect: 0x200000,\r\n  noSpell: 0x400000\r\n};\r\nconst FIELD_JUSTIFY = {\r\n  left: 0,\r\n  center: 1,\r\n  right: 2\r\n};\r\nconst VALUE_MAP = { value: 'V', defaultValue: 'DV' };\r\nconst FORMAT_SPECIAL = {\r\n  zip: '0',\r\n  zipPlus4: '1',\r\n  zip4: '1',\r\n  phone: '2',\r\n  ssn: '3'\r\n};\r\nconst FORMAT_DEFAULT = {\r\n  number: {\r\n    nDec: 0,\r\n    sepComma: false,\r\n    negStyle: 'MinusBlack',\r\n    currency: '',\r\n    currencyPrepend: true\r\n  },\r\n  percent: {\r\n    nDec: 0,\r\n    sepComma: false\r\n  }\r\n};\r\n\r\nexport default {\r\n  /**\r\n   * Must call if adding AcroForms to a document. Must also call font() before\r\n   * this method to set the default font.\r\n   */\r\n  initForm() {\r\n    if (!this._font) {\r\n      throw new Error('Must set a font before calling initForm method');\r\n    }\r\n    this._acroform = {\r\n      fonts: {},\r\n      defaultFont: this._font.name\r\n    };\r\n    this._acroform.fonts[this._font.id] = this._font.ref();\r\n\r\n    let data = {\r\n      Fields: [],\r\n      NeedAppearances: true,\r\n      DA: new String(`/${this._font.id} 0 Tf 0 g`),\r\n      DR: {\r\n        Font: {}\r\n      }\r\n    };\r\n    data.DR.Font[this._font.id] = this._font.ref();\r\n    const AcroForm = this.ref(data);\r\n    this._root.data.AcroForm = AcroForm;\r\n    return this;\r\n  },\r\n\r\n  /**\r\n   * Called automatically by document.js\r\n   */\r\n  endAcroForm() {\r\n    if (this._root.data.AcroForm) {\r\n      if (\r\n        !Object.keys(this._acroform.fonts).length &&\r\n        !this._acroform.defaultFont\r\n      ) {\r\n        throw new Error('No fonts specified for PDF form');\r\n      }\r\n      let fontDict = this._root.data.AcroForm.data.DR.Font;\r\n      Object.keys(this._acroform.fonts).forEach(name => {\r\n        fontDict[name] = this._acroform.fonts[name];\r\n      });\r\n      this._root.data.AcroForm.data.Fields.forEach(fieldRef => {\r\n        this._endChild(fieldRef);\r\n      });\r\n      this._root.data.AcroForm.end();\r\n    }\r\n    return this;\r\n  },\r\n\r\n  _endChild(ref) {\r\n    if (Array.isArray(ref.data.Kids)) {\r\n      ref.data.Kids.forEach(childRef => {\r\n        this._endChild(childRef);\r\n      });\r\n      ref.end();\r\n    }\r\n    return this;\r\n  },\r\n\r\n  /**\r\n   * Creates and adds a form field to the document. Form fields are intermediate\r\n   * nodes in a PDF form that are used to specify form name heirarchy and form\r\n   * value defaults.\r\n   * @param {string} name - field name (T attribute in field dictionary)\r\n   * @param {object} options  - other attributes to include in field dictionary\r\n   */\r\n  formField(name, options = {}) {\r\n    let fieldDict = this._fieldDict(name, null, options);\r\n    let fieldRef = this.ref(fieldDict);\r\n    this._addToParent(fieldRef);\r\n    return fieldRef;\r\n  },\r\n\r\n  /**\r\n   * Creates and adds a Form Annotation to the document. Form annotations are\r\n   * called Widget annotations internally within a PDF file.\r\n   * @param {string} name - form field name (T attribute of widget annotation\r\n   * dictionary)\r\n   * @param {number} x\r\n   * @param {number} y\r\n   * @param {number} w\r\n   * @param {number} h\r\n   * @param {object} options\r\n   */\r\n  formAnnotation(name, type, x, y, w, h, options = {}) {\r\n    let fieldDict = this._fieldDict(name, type, options);\r\n    fieldDict.Subtype = 'Widget';\r\n    if (fieldDict.F === undefined) {\r\n      fieldDict.F = 4; // print the annotation\r\n    }\r\n\r\n    // Add Field annot to page, and get it's ref\r\n    this.annotate(x, y, w, h, fieldDict);\r\n    let annotRef = this.page.annotations[this.page.annotations.length - 1];\r\n\r\n    return this._addToParent(annotRef);\r\n  },\r\n\r\n  formText(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'text', x, y, w, h, options);\r\n  },\r\n\r\n  formPushButton(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'pushButton', x, y, w, h, options);\r\n  },\r\n\r\n  formCombo(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'combo', x, y, w, h, options);\r\n  },\r\n\r\n  formList(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'list', x, y, w, h, options);\r\n  },\r\n\r\n  formRadioButton(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'radioButton', x, y, w, h, options);\r\n  },\r\n\r\n  formCheckbox(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'checkbox', x, y, w, h, options);\r\n  },\r\n\r\n  _addToParent(fieldRef) {\r\n    let parent = fieldRef.data.Parent;\r\n    if (parent) {\r\n      if (!parent.data.Kids) {\r\n        parent.data.Kids = [];\r\n      }\r\n      parent.data.Kids.push(fieldRef);\r\n    } else {\r\n      this._root.data.AcroForm.data.Fields.push(fieldRef);\r\n    }\r\n    return this;\r\n  },\r\n\r\n  _fieldDict(name, type, options = {}) {\r\n    if (!this._acroform) {\r\n      throw new Error(\r\n        'Call document.initForm() method before adding form elements to document'\r\n      );\r\n    }\r\n    let opts = Object.assign({}, options);\r\n    if (type !== null) {\r\n      opts = this._resolveType(type, options);\r\n    }\r\n    opts = this._resolveFlags(opts);\r\n    opts = this._resolveJustify(opts);\r\n    opts = this._resolveFont(opts);\r\n    opts = this._resolveStrings(opts);\r\n    opts = this._resolveColors(opts);\r\n    opts = this._resolveFormat(opts);\r\n    opts.T = new String(name);\r\n    if (opts.parent) {\r\n      opts.Parent = opts.parent;\r\n      delete opts.parent;\r\n    }\r\n    return opts;\r\n  },\r\n\r\n  _resolveType(type, opts) {\r\n    if (type === 'text') {\r\n      opts.FT = 'Tx';\r\n    } else if (type === 'pushButton') {\r\n      opts.FT = 'Btn';\r\n      opts.pushButton = true;\r\n    } else if (type === 'radioButton') {\r\n      opts.FT = 'Btn';\r\n      opts.radioButton = true;\r\n    } else if (type === 'checkbox') {\r\n      opts.FT = 'Btn';\r\n    } else if (type === 'combo') {\r\n      opts.FT = 'Ch';\r\n      opts.combo = true;\r\n    } else if (type === 'list') {\r\n      opts.FT = 'Ch';\r\n    } else {\r\n      throw new Error(`Invalid form annotation type '${type}'`);\r\n    }\r\n    return opts;\r\n  },\r\n\r\n  _resolveFormat(opts) {\r\n    const f = opts.format;\r\n    if (f && f.type) {\r\n      let fnKeystroke;\r\n      let fnFormat;\r\n      let params = '';\r\n      if (FORMAT_SPECIAL[f.type] !== undefined) {\r\n        fnKeystroke = `AFSpecial_Keystroke`;\r\n        fnFormat = `AFSpecial_Format`;\r\n        params = FORMAT_SPECIAL[f.type];\r\n      } else {\r\n        let format = f.type.charAt(0).toUpperCase() + f.type.slice(1);\r\n        fnKeystroke = `AF${format}_Keystroke`;\r\n        fnFormat = `AF${format}_Format`;\r\n\r\n        if (f.type === 'date') {\r\n          fnKeystroke += 'Ex';\r\n          params = String(f.param);\r\n        } else if (f.type === 'time') {\r\n          params = String(f.param);\r\n        } else if (f.type === 'number') {\r\n          let p = Object.assign({}, FORMAT_DEFAULT.number, f);\r\n          params = String(\r\n            [\r\n              String(p.nDec),\r\n              p.sepComma ? '0' : '1',\r\n              '\"' + p.negStyle + '\"',\r\n              'null',\r\n              '\"' + p.currency + '\"',\r\n              String(p.currencyPrepend)\r\n            ].join(',')\r\n          );\r\n        } else if (f.type === 'percent') {\r\n          let p = Object.assign({}, FORMAT_DEFAULT.percent, f);\r\n          params = String([String(p.nDec), p.sepComma ? '0' : '1'].join(','));\r\n        }\r\n      }\r\n      opts.AA = opts.AA ? opts.AA : {};\r\n      opts.AA.K = {\r\n        S: 'JavaScript',\r\n        JS: new String(`${fnKeystroke}(${params});`)\r\n      };\r\n      opts.AA.F = {\r\n        S: 'JavaScript',\r\n        JS: new String(`${fnFormat}(${params});`)\r\n      };\r\n    }\r\n    delete opts.format;\r\n    return opts;\r\n  },\r\n\r\n  _resolveColors(opts) {\r\n    let color = this._normalizeColor(opts.backgroundColor);\r\n    if (color) {\r\n      if (!opts.MK) {\r\n        opts.MK = {};\r\n      }\r\n      opts.MK.BG = color;\r\n    }\r\n    color = this._normalizeColor(opts.borderColor);\r\n    if (color) {\r\n      if (!opts.MK) {\r\n        opts.MK = {};\r\n      }\r\n      opts.MK.BC = color;\r\n    }\r\n    delete opts.backgroundColor;\r\n    delete opts.borderColor;\r\n    return opts;\r\n  },\r\n\r\n  _resolveFlags(options) {\r\n    let result = 0;\r\n    Object.keys(options).forEach(key => {\r\n      if (FIELD_FLAGS[key]) {\r\n        if (options[key]) {\r\n          result |= FIELD_FLAGS[key];\r\n        }\r\n        delete options[key];\r\n      }\r\n    });\r\n    if (result !== 0) {\r\n      options.Ff = options.Ff ? options.Ff : 0;\r\n      options.Ff |= result;\r\n    }\r\n    return options;\r\n  },\r\n\r\n  _resolveJustify(options) {\r\n    let result = 0;\r\n    if (options.align !== undefined) {\r\n      if (typeof FIELD_JUSTIFY[options.align] === 'number') {\r\n        result = FIELD_JUSTIFY[options.align];\r\n      }\r\n      delete options.align;\r\n    }\r\n    if (result !== 0) {\r\n      options.Q = result; // default\r\n    }\r\n    return options;\r\n  },\r\n\r\n  _resolveFont(options) {\r\n    // add current font to document-level AcroForm dict if necessary\r\n    if (this._acroform.fonts[this._font.id] == null) {\r\n      this._acroform.fonts[this._font.id] = this._font.ref();\r\n    }\r\n\r\n    // add current font to field's resource dict (RD) if not the default acroform font\r\n    if (this._acroform.defaultFont !== this._font.name) {\r\n      options.DR = { Font: {} };\r\n\r\n      // Get the fontSize option. If not set use auto sizing\r\n      const fontSize = options.fontSize || 0;\r\n\r\n      options.DR.Font[this._font.id] = this._font.ref();\r\n      options.DA = new String(`/${this._font.id} ${fontSize} Tf 0 g`);\r\n    }\r\n    return options;\r\n  },\r\n\r\n  _resolveStrings(options) {\r\n    let select = [];\r\n    function appendChoices(a) {\r\n      if (Array.isArray(a)) {\r\n        for (let idx = 0; idx < a.length; idx++) {\r\n          if (typeof a[idx] === 'string') {\r\n            select.push(new String(a[idx]));\r\n          } else {\r\n            select.push(a[idx]);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    appendChoices(options.Opt);\r\n    if (options.select) {\r\n      appendChoices(options.select);\r\n      delete options.select;\r\n    }\r\n    if (select.length) {\r\n      options.Opt = select;\r\n    }\r\n\r\n    Object.keys(VALUE_MAP).forEach(key => {\r\n      if (options[key] !== undefined) {\r\n        options[VALUE_MAP[key]] = options[key];\r\n        delete options[key];\r\n      }\r\n    });\r\n    ['V', 'DV'].forEach(key => {\r\n      if (typeof options[key] === 'string') {\r\n        options[key] = new String(options[key]);\r\n      }\r\n    });\r\n\r\n    if (options.MK && options.MK.CA) {\r\n      options.MK.CA = new String(options.MK.CA);\r\n    }\r\n    if (options.label) {\r\n      options.MK = options.MK ? options.MK : {};\r\n      options.MK.CA = new String(options.label);\r\n      delete options.label;\r\n    }\r\n    return options;\r\n  }\r\n};\r\n", "import fs from 'fs';\r\nimport CryptoJS from 'crypto-js';\r\n\r\nexport default {\r\n  /**\r\n   * Embed contents of `src` in PDF\r\n   * @param {Buffer | ArrayBuffer | string} src input Buffer, ArrayBuffer, base64 encoded string or path to file\r\n   * @param {object} options\r\n   *  * options.name: filename to be shown in PDF, will use `src` if none set\r\n   *  * options.type: filetype to be shown in PDF\r\n   *  * options.description: description to be shown in PDF\r\n   *  * options.hidden: if true, do not add attachment to EmbeddedFiles dictionary. Useful for file attachment annotations\r\n   *  * options.creationDate: override creation date\r\n   *  * options.modifiedDate: override modified date\r\n   *  * options.relationship: Relationship between the PDF document and its attached file. Can be 'Alternative', 'Data', 'Source', 'Supplement' or 'Unspecified'.\r\n   * @returns filespec reference\r\n   */\r\n  file(src, options = {}) {\r\n    options.name = options.name || src;\r\n    options.relationship = options.relationship || 'Unspecified';\r\n\r\n    const refBody = {\r\n      Type: 'EmbeddedFile',\r\n      Params: {}\r\n    };\r\n    let data;\r\n\r\n    if (!src) {\r\n      throw new Error('No src specified');\r\n    }\r\n    if (Buffer.isBuffer(src)) {\r\n      data = src;\r\n    } else if (src instanceof ArrayBuffer) {\r\n      data = Buffer.from(new Uint8Array(src));\r\n    } else {\r\n      let match;\r\n      if ((match = /^data:(.*?);base64,(.*)$/.exec(src))) {\r\n        if (match[1]) {\r\n          refBody.Subtype = match[1].replace('/', '#2F');\r\n        }\r\n        data = Buffer.from(match[2], 'base64');\r\n      } else {\r\n        data = fs.readFileSync(src);\r\n        if (!data) {\r\n          throw new Error(`Could not read contents of file at filepath ${src}`);\r\n        }\r\n\r\n        // update CreationDate and ModDate\r\n        const { birthtime, ctime } = fs.statSync(src);\r\n        refBody.Params.CreationDate = birthtime;\r\n        refBody.Params.ModDate = ctime;\r\n      }\r\n    }\r\n\r\n    // override creation date and modified date\r\n    if (options.creationDate instanceof Date) {\r\n      refBody.Params.CreationDate = options.creationDate;\r\n    }\r\n    if (options.modifiedDate instanceof Date) {\r\n      refBody.Params.ModDate = options.modifiedDate;\r\n    }\r\n    // add optional subtype\r\n    if (options.type) {\r\n      refBody.Subtype = options.type.replace('/', '#2F');\r\n    }\r\n\r\n    // add checksum and size information\r\n    const checksum = CryptoJS.MD5(\r\n      CryptoJS.lib.WordArray.create(new Uint8Array(data))\r\n    );\r\n    refBody.Params.CheckSum = new String(checksum);\r\n    refBody.Params.Size = data.byteLength;\r\n\r\n    // save some space when embedding the same file again\r\n    // if a file with the same name and metadata exists, reuse its reference\r\n    let ref;\r\n    if (!this._fileRegistry) this._fileRegistry = {};\r\n    let file = this._fileRegistry[options.name];\r\n    if (file && isEqual(refBody, file)) {\r\n      ref = file.ref;\r\n    } else {\r\n      ref = this.ref(refBody);\r\n      ref.end(data);\r\n\r\n      this._fileRegistry[options.name] = { ...refBody, ref };\r\n    }\r\n    // add filespec for embedded file\r\n    const fileSpecBody = {\r\n      Type: 'Filespec',\r\n      AFRelationship: options.relationship,\r\n      F: new String(options.name),\r\n      EF: { F: ref },\r\n      UF: new String(options.name)\r\n    };\r\n    if (options.description) {\r\n      fileSpecBody.Desc = new String(options.description);\r\n    }\r\n    const filespec = this.ref(fileSpecBody);\r\n    filespec.end();\r\n\r\n    if (!options.hidden) {\r\n      this.addNamedEmbeddedFile(options.name, filespec);\r\n    }\r\n\r\n    // Add file to the catalogue to be PDF/A3 compliant\r\n    if (this._root.data.AF) {\r\n      this._root.data.AF.push(filespec);\r\n    } else {\r\n      this._root.data.AF = [filespec];\r\n    }\r\n\r\n    return filespec;\r\n  }\r\n};\r\n\r\n/** check two embedded file metadata objects for equality */\r\nfunction isEqual(a, b) {\r\n  return (\r\n    a.Subtype === b.Subtype &&\r\n    a.Params.CheckSum.toString() === b.Params.CheckSum.toString() &&\r\n    a.Params.Size === b.Params.Size &&\r\n    a.Params.CreationDate.getTime() === b.Params.CreationDate.getTime() &&\r\n    ((a.Params.ModDate === undefined && b.Params.ModDate === undefined) ||\r\n      a.Params.ModDate.getTime() === b.Params.ModDate.getTime())\r\n  );\r\n}\r\n", "import fs from 'fs';\r\n\r\nexport default {\r\n  initPDFA(pSubset) {\r\n    if (pSubset.charAt(pSubset.length - 3) === '-') {\r\n      this.subset_conformance = pSubset\r\n        .charAt(pSubset.length - 1)\r\n        .toUpperCase();\r\n      this.subset = parseInt(pSubset.charAt(pSubset.length - 2));\r\n    } else {\r\n      // Default to Basic conformance when user doesn't specify\r\n      this.subset_conformance = 'B';\r\n      this.subset = parseInt(pSubset.charAt(pSubset.length - 1));\r\n    }\r\n  },\r\n\r\n  endSubset() {\r\n    this._addPdfaMetadata();\r\n    this._addColorOutputIntent();\r\n  },\r\n\r\n  _addColorOutputIntent() {\r\n    const iccProfile = fs.readFileSync(`${__dirname}/data/sRGB_IEC61966_2_1.icc`);\r\n\r\n    const colorProfileRef = this.ref({\r\n      Length: iccProfile.length,\r\n      N: 3\r\n    });\r\n    colorProfileRef.write(iccProfile);\r\n    colorProfileRef.end();\r\n\r\n    const intentRef = this.ref({\r\n      Type: 'OutputIntent',\r\n      S: 'GTS_PDFA1',\r\n      Info: new String('sRGB IEC61966-2.1'),\r\n      OutputConditionIdentifier: new String('sRGB IEC61966-2.1'),\r\n      DestOutputProfile: colorProfileRef\r\n    });\r\n    intentRef.end();\r\n\r\n    this._root.data.OutputIntents = [intentRef];\r\n  },\r\n\r\n  _getPdfaid() {\r\n    return `\r\n        <rdf:Description xmlns:pdfaid=\"http://www.aiim.org/pdfa/ns/id/\" rdf:about=\"\">\r\n            <pdfaid:part>${this.subset}</pdfaid:part>\r\n            <pdfaid:conformance>${this.subset_conformance}</pdfaid:conformance>\r\n        </rdf:Description>\r\n        `;\r\n  },\r\n\r\n  _addPdfaMetadata() {\r\n    this.appendXML(this._getPdfaid());\r\n  }\r\n};\r\n", "\r\nexport default {\r\n\r\n    initPDFUA() {\r\n        this.subset = 1;\r\n    },\r\n\r\n    endSubset() {\r\n        this._addPdfuaMetadata();\r\n    },\r\n\r\n    _addPdfuaMetadata() {\r\n        this.appendXML(this._getPdfuaid());\r\n    },\r\n\r\n    _getPdfuaid() {\r\n        return `\r\n        <rdf:Description xmlns:pdfuaid=\"http://www.aiim.org/pdfua/ns/id/\" rdf:about=\"\">\r\n            <pdfuaid:part>${this.subset}</pdfuaid:part>\r\n        </rdf:Description>\r\n        `;\r\n    },\r\n\r\n}", "import PDFA from './pdfa';\r\nimport PDFUA from './pdfua';\r\n\r\nexport default {\r\n    _importSubset(subset) {\r\n        Object.assign(this, subset)\r\n    },\r\n\r\n    initSubset(options) {\r\n\r\n        switch (options.subset) {\r\n            case 'PDF/A-1':\r\n            case 'PDF/A-1a':\r\n            case 'PDF/A-1b':\r\n            case 'PDF/A-2':\r\n            case 'PDF/A-2a':\r\n            case 'PDF/A-2b':\r\n            case 'PDF/A-3':\r\n            case 'PDF/A-3a':\r\n            case 'PDF/A-3b':\r\n                this._importSubset(PDFA);\r\n                this.initPDFA(options.subset);\r\n                break;\r\n            case 'PDF/UA':\r\n                this._importSubset(PDFUA);\r\n                this.initPDFUA();\r\n                break;\r\n        }\r\n    }\r\n}", "\r\nclass PDFMetadata {\r\n    constructor() {\r\n        this._metadata = `\r\n        <?xpacket begin=\"\\ufeff\" id=\"W5M0MpCehiHzreSzNTczkc9d\"?>\r\n            <x:xmpmeta xmlns:x=\"adobe:ns:meta/\">\r\n                <rdf:RDF xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\r\n        `;\r\n    }\r\n    \r\n    _closeTags() {\r\n        this._metadata = this._metadata.concat(`\r\n                </rdf:RDF>\r\n            </x:xmpmeta>\r\n        <?xpacket end=\"w\"?>\r\n        `);\r\n    }\r\n\r\n    append(xml, newline=true) {\r\n        this._metadata = this._metadata.concat(xml); \r\n        if (newline)\r\n            this._metadata = this._metadata.concat('\\n'); \r\n    }\r\n\r\n    getXML() { return this._metadata; }\r\n\r\n    getLength() { return this._metadata.length; }\r\n\r\n    end() {\r\n        this._closeTags();\r\n        this._metadata = this._metadata.trim();\r\n    }\r\n}\r\n\r\nexport default PDFMetadata;", "import PDFMetadata from \"../metadata\"\r\n\r\nexport default {\r\n    initMetadata() {\r\n        this.metadata = new PDFMetadata();\r\n    },\r\n\r\n    appendXML(xml, newline=true) { this.metadata.append(xml,newline); },\r\n\r\n    _addInfo() {\r\n        this.appendXML(`\r\n        <rdf:Description rdf:about=\"\" xmlns:xmp=\"http://ns.adobe.com/xap/1.0/\">\r\n            <xmp:CreateDate>${this.info.CreationDate.toISOString().split('.')[0]+\"Z\"}</xmp:CreateDate>\r\n            <xmp:CreatorTool>${this.info.Creator}</xmp:CreatorTool>\r\n        </rdf:Description>\r\n        `\r\n        );\r\n\r\n        if (this.info.Title || this.info.Author || this.info.Subject) {\r\n            this.appendXML(`\r\n            <rdf:Description rdf:about=\"\" xmlns:dc=\"http://purl.org/dc/elements/1.1/\">\r\n            `);\r\n            \r\n            if (this.info.Title) {\r\n                this.appendXML(`\r\n                <dc:title>\r\n                    <rdf:Alt>\r\n                        <rdf:li xml:lang=\"x-default\">${this.info.Title}</rdf:li>\r\n                    </rdf:Alt>\r\n                </dc:title>\r\n                `);\r\n            }\r\n\r\n            if (this.info.Author) {\r\n                this.appendXML(`\r\n                <dc:creator>\r\n                    <rdf:Seq>\r\n                        <rdf:li>${this.info.Author}</rdf:li>\r\n                    </rdf:Seq>\r\n                </dc:creator>\r\n                `);\r\n            }\r\n\r\n            if (this.info.Subject) {\r\n                this.appendXML(`\r\n                <dc:description>\r\n                    <rdf:Alt>\r\n                        <rdf:li xml:lang=\"x-default\">${this.info.Subject}</rdf:li>\r\n                    </rdf:Alt>\r\n                </dc:description>\r\n                `);\r\n            }\r\n\r\n            this.appendXML(`\r\n            </rdf:Description>\r\n            `);\r\n        }\r\n\r\n        this.appendXML(`\r\n        <rdf:Description rdf:about=\"\" xmlns:pdf=\"http://ns.adobe.com/pdf/1.3/\">\r\n            <pdf:Producer>${this.info.Creator}</pdf:Producer>`, false);\r\n\r\n        if (this.info.Keywords) {\r\n            this.appendXML(`\r\n            <pdf:Keywords>${this.info.Keywords}</pdf:Keywords>`, false);\r\n        }\r\n\r\n        this.appendXML(`\r\n        </rdf:Description>\r\n        `);\r\n    },\r\n\r\n    endMetadata() {\r\n        this._addInfo();\r\n    \r\n        this.metadata.end();\r\n\r\n        /*\r\n        Metadata was introduced in PDF 1.4, so adding it to 1.3 \r\n        will likely only take up more space.\r\n        */\r\n        if (this.version != 1.3) {\r\n            this.metadataRef = this.ref({\r\n                length: this.metadata.getLength(),\r\n                Type: 'Metadata',\r\n                Subtype: 'XML'\r\n            });\r\n            this.metadataRef.compress = false;\r\n            this.metadataRef.write(Buffer.from(this.metadata.getXML(), 'utf-8'));\r\n            this.metadataRef.end();\r\n            this._root.data.Metadata = this.metadataRef;\r\n        }\r\n    }\r\n}", "/*\r\nPDFDocument - represents an entire PDF document\r\nBy <PERSON>\r\n*/\r\n\r\nimport stream from 'stream';\r\nimport PDFObject from './object';\r\nimport PDFReference from './reference';\r\nimport PDFPage from './page';\r\nimport PDFNameTree from './name_tree';\r\nimport PDFSecurity from './security';\r\nimport ColorMixin from './mixins/color';\r\nimport VectorMixin from './mixins/vector';\r\nimport FontsMixin from './mixins/fonts';\r\nimport TextMixin from './mixins/text';\r\nimport ImagesMixin from './mixins/images';\r\nimport AnnotationsMixin from './mixins/annotations';\r\nimport OutlineMixin from './mixins/outline';\r\nimport MarkingsMixin from './mixins/markings';\r\nimport AcroFormMixin from './mixins/acroform';\r\nimport AttachmentsMixin from './mixins/attachments';\r\nimport LineWrapper from './line_wrapper';\r\nimport SubsetMixin from './mixins/subsets';\r\nimport MetadataMixin from './mixins/metadata';\r\n\r\nclass PDFDocument extends stream.Readable {\r\n  constructor(options = {}) {\r\n    super(options);\r\n    this.options = options;\r\n\r\n    // PDF version\r\n    switch (options.pdfVersion) {\r\n      case '1.4':\r\n        this.version = 1.4;\r\n        break;\r\n      case '1.5':\r\n        this.version = 1.5;\r\n        break;\r\n      case '1.6':\r\n        this.version = 1.6;\r\n        break;\r\n      case '1.7':\r\n      case '1.7ext3':\r\n        this.version = 1.7;\r\n        break;\r\n      default:\r\n        this.version = 1.3;\r\n        break;\r\n    }\r\n\r\n    // Whether streams should be compressed\r\n    this.compress =\r\n      this.options.compress != null ? this.options.compress : true;\r\n\r\n    this._pageBuffer = [];\r\n    this._pageBufferStart = 0;\r\n\r\n    // The PDF object store\r\n    this._offsets = [];\r\n    this._waiting = 0;\r\n    this._ended = false;\r\n    this._offset = 0;\r\n    const Pages = this.ref({\r\n      Type: 'Pages',\r\n      Count: 0,\r\n      Kids: []\r\n    });\r\n\r\n    const Names = this.ref({\r\n      Dests: new PDFNameTree()\r\n    });\r\n\r\n    this._root = this.ref({\r\n      Type: 'Catalog',\r\n      Pages,\r\n      Names\r\n    });\r\n\r\n    if (this.options.lang) {\r\n      this._root.data.Lang = new String(this.options.lang);\r\n    }\r\n\r\n    // The current page\r\n    this.page = null;\r\n\r\n    // Initialize mixins\r\n    this.initMetadata();\r\n    this.initColor();\r\n    this.initVector();\r\n    this.initFonts(options.font);\r\n    this.initText();\r\n    this.initImages();\r\n    this.initOutline();\r\n    this.initMarkings(options);\r\n    this.initSubset(options);\r\n\r\n    // Initialize the metadata\r\n    this.info = {\r\n      Producer: 'PDFKit',\r\n      Creator: 'PDFKit',\r\n      CreationDate: new Date()\r\n    };\r\n\r\n    if (this.options.info) {\r\n      for (let key in this.options.info) {\r\n        const val = this.options.info[key];\r\n        this.info[key] = val;\r\n      }\r\n    }\r\n\r\n    if (this.options.displayTitle) {\r\n      this._root.data.ViewerPreferences = this.ref({\r\n        DisplayDocTitle: true\r\n      });\r\n    }\r\n\r\n    // Generate file ID\r\n    this._id = PDFSecurity.generateFileID(this.info);\r\n\r\n    // Initialize security settings\r\n    this._security = PDFSecurity.create(this, options);\r\n\r\n    // Write the header\r\n    // PDF version\r\n    this._write(`%PDF-${this.version}`);\r\n\r\n    // 4 binary chars, as recommended by the spec\r\n    this._write('%\\xFF\\xFF\\xFF\\xFF');\r\n\r\n    // Add the first page\r\n    if (this.options.autoFirstPage !== false) {\r\n      this.addPage();\r\n    }\r\n  }\r\n\r\n  addPage(options) {\r\n    if (options == null) {\r\n      ({ options } = this);\r\n    }\r\n\r\n    // end the current page if needed\r\n    if (!this.options.bufferPages) {\r\n      this.flushPages();\r\n    }\r\n\r\n    // create a page object\r\n    this.page = new PDFPage(this, options);\r\n    this._pageBuffer.push(this.page);\r\n\r\n    // add the page to the object store\r\n    const pages = this._root.data.Pages.data;\r\n    pages.Kids.push(this.page.dictionary);\r\n    pages.Count++;\r\n\r\n    // reset x and y coordinates\r\n    this.x = this.page.margins.left;\r\n    this.y = this.page.margins.top;\r\n\r\n    // flip PDF coordinate system so that the origin is in\r\n    // the top left rather than the bottom left\r\n    this._ctm = [1, 0, 0, 1, 0, 0];\r\n    this.transform(1, 0, 0, -1, 0, this.page.height);\r\n\r\n    this.emit('pageAdded');\r\n\r\n    return this;\r\n  }\r\n\r\n  continueOnNewPage(options) {\r\n    const pageMarkings = this.endPageMarkings(this.page);\r\n\r\n    this.addPage(options);\r\n\r\n    this.initPageMarkings(pageMarkings);\r\n\r\n    return this;\r\n  }\r\n\r\n  bufferedPageRange() {\r\n    return { start: this._pageBufferStart, count: this._pageBuffer.length };\r\n  }\r\n\r\n  switchToPage(n) {\r\n    let page;\r\n    if (!(page = this._pageBuffer[n - this._pageBufferStart])) {\r\n      throw new Error(\r\n        `switchToPage(${n}) out of bounds, current buffer covers pages ${\r\n          this._pageBufferStart\r\n        } to ${this._pageBufferStart + this._pageBuffer.length - 1}`\r\n      );\r\n    }\r\n\r\n    return (this.page = page);\r\n  }\r\n\r\n  flushPages() {\r\n    // this local variable exists so we're future-proof against\r\n    // reentrant calls to flushPages.\r\n    const pages = this._pageBuffer;\r\n    this._pageBuffer = [];\r\n    this._pageBufferStart += pages.length;\r\n    for (let page of pages) {\r\n      this.endPageMarkings(page);\r\n      page.end();\r\n    }\r\n  }\r\n\r\n  addNamedDestination(name, ...args) {\r\n    if (args.length === 0) {\r\n      args = ['XYZ', null, null, null];\r\n    }\r\n    if (args[0] === 'XYZ' && args[2] !== null) {\r\n      args[2] = this.page.height - args[2];\r\n    }\r\n    args.unshift(this.page.dictionary);\r\n    this._root.data.Names.data.Dests.add(name, args);\r\n  }\r\n\r\n  addNamedEmbeddedFile(name, ref) {\r\n    if (!this._root.data.Names.data.EmbeddedFiles) {\r\n      // disabling /Limits for this tree fixes attachments not showing in Adobe Reader\r\n      this._root.data.Names.data.EmbeddedFiles = new PDFNameTree({\r\n        limits: false\r\n      });\r\n    }\r\n\r\n    // add filespec to EmbeddedFiles\r\n    this._root.data.Names.data.EmbeddedFiles.add(name, ref);\r\n  }\r\n\r\n  addNamedJavaScript(name, js) {\r\n    if (!this._root.data.Names.data.JavaScript) {\r\n      this._root.data.Names.data.JavaScript = new PDFNameTree();\r\n    }\r\n    let data = {\r\n      JS: new String(js),\r\n      S: 'JavaScript'\r\n    };\r\n    this._root.data.Names.data.JavaScript.add(name, data);\r\n  }\r\n\r\n  ref(data) {\r\n    const ref = new PDFReference(this, this._offsets.length + 1, data);\r\n    this._offsets.push(null); // placeholder for this object's offset once it is finalized\r\n    this._waiting++;\r\n    return ref;\r\n  }\r\n\r\n  _read() {}\r\n  // do nothing, but this method is required by node\r\n\r\n  _write(data) {\r\n    if (!Buffer.isBuffer(data)) {\r\n      data = Buffer.from(data + '\\n', 'binary');\r\n    }\r\n\r\n    this.push(data);\r\n    return (this._offset += data.length);\r\n  }\r\n\r\n  addContent(data) {\r\n    this.page.write(data);\r\n    return this;\r\n  }\r\n\r\n  _refEnd(ref) {\r\n    this._offsets[ref.id - 1] = ref.offset;\r\n    if (--this._waiting === 0 && this._ended) {\r\n      this._finalize();\r\n      return (this._ended = false);\r\n    }\r\n  }\r\n\r\n  end() {\r\n    this.flushPages();\r\n\r\n    this._info = this.ref();\r\n    for (let key in this.info) {\r\n      let val = this.info[key];\r\n      if (typeof val === 'string') {\r\n        val = new String(val);\r\n      }\r\n\r\n      let entry = this.ref(val);\r\n      entry.end();\r\n\r\n      this._info.data[key] = entry;\r\n    }\r\n\r\n    this._info.end();\r\n\r\n    for (let name in this._fontFamilies) {\r\n      const font = this._fontFamilies[name];\r\n      font.finalize();\r\n    }\r\n\r\n    this.endOutline();\r\n    this.endMarkings();\r\n\r\n    if (this.subset) {\r\n      this.endSubset();\r\n    }\r\n\r\n    this.endMetadata();\r\n\r\n    this._root.end();\r\n    this._root.data.Pages.end();\r\n    this._root.data.Names.end();\r\n    this.endAcroForm();\r\n\r\n    if (this._root.data.ViewerPreferences) {\r\n      this._root.data.ViewerPreferences.end();\r\n    }\r\n\r\n    if (this._security) {\r\n      this._security.end();\r\n    }\r\n\r\n    if (this._waiting === 0) {\r\n      return this._finalize();\r\n    } else {\r\n      return (this._ended = true);\r\n    }\r\n  }\r\n\r\n  _finalize() {\r\n    // generate xref\r\n    const xRefOffset = this._offset;\r\n    this._write('xref');\r\n    this._write(`0 ${this._offsets.length + 1}`);\r\n    this._write('0000000000 65535 f ');\r\n\r\n    for (let offset of this._offsets) {\r\n      offset = `0000000000${offset}`.slice(-10);\r\n      this._write(offset + ' 00000 n ');\r\n    }\r\n\r\n    // trailer\r\n    const trailer = {\r\n      Size: this._offsets.length + 1,\r\n      Root: this._root,\r\n      Info: this._info,\r\n      ID: [this._id, this._id]\r\n    };\r\n    if (this._security) {\r\n      trailer.Encrypt = this._security.dictionary;\r\n    }\r\n\r\n    this._write('trailer');\r\n    this._write(PDFObject.convert(trailer));\r\n\r\n    this._write('startxref');\r\n    this._write(`${xRefOffset}`);\r\n    this._write('%%EOF');\r\n\r\n    // end the stream\r\n    return this.push(null);\r\n  }\r\n\r\n  toString() {\r\n    return '[object PDFDocument]';\r\n  }\r\n}\r\n\r\nconst mixin = methods => {\r\n  Object.assign(PDFDocument.prototype, methods);\r\n};\r\n\r\nmixin(MetadataMixin);\r\nmixin(ColorMixin);\r\nmixin(VectorMixin);\r\nmixin(FontsMixin);\r\nmixin(TextMixin);\r\nmixin(ImagesMixin);\r\nmixin(AnnotationsMixin);\r\nmixin(OutlineMixin);\r\nmixin(MarkingsMixin);\r\nmixin(AcroFormMixin);\r\nmixin(AttachmentsMixin);\r\nmixin(SubsetMixin);\r\n\r\nPDFDocument.LineWrapper = LineWrapper;\r\n\r\nexport default PDFDocument;\r\n"], "names": ["PDFAbstractReference", "toString", "Error", "PDFTree", "constructor", "options", "_items", "limits", "add", "key", "val", "get", "sortedKeys", "Object", "keys", "sort", "a", "b", "_compareKeys", "out", "length", "first", "last", "push", "PDFObject", "convert", "_dataForKey", "_keysName", "join", "SpotColor", "doc", "name", "C", "M", "Y", "K", "id", "spotColors", "values", "ref", "Range", "C0", "C1", "map", "value", "FunctionType", "Domain", "N", "end", "pad", "str", "Array", "slice", "escapableRe", "escapable", "swapBytes", "buff", "l", "i", "object", "encryptFn", "String", "string", "isUnicode", "charCodeAt", "stringBuffer", "<PERSON><PERSON><PERSON>", "from", "valueOf", "replace", "c", "<PERSON><PERSON><PERSON><PERSON>", "Date", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "isArray", "items", "e", "call", "number", "n", "Math", "round", "PDFReference", "document", "data", "gen", "compress", "Filter", "uncompressedLength", "buffer", "write", "chunk", "Length", "finalize", "offset", "_offset", "_security", "getEncryptFn", "concat", "zlib", "deflateSync", "_write", "_refEnd", "DEFAULT_MARGINS", "top", "left", "bottom", "right", "SIZES", "A0", "A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8", "A9", "A10", "B0", "B1", "B2", "B3", "B4", "B5", "B6", "B7", "B8", "B9", "B10", "C2", "C3", "C4", "C5", "C6", "C7", "C8", "C9", "C10", "RA0", "RA1", "RA2", "RA3", "RA4", "SRA0", "SRA1", "SRA2", "SRA3", "SRA4", "EXECUTIVE", "FOLIO", "LEGAL", "LETTER", "TABLOID", "PDFPage", "size", "layout", "margin", "margins", "dimensions", "toUpperCase", "width", "height", "content", "resources", "ProcSet", "dictionary", "Type", "Parent", "_root", "Pages", "MediaBox", "Contents", "Resources", "markings", "fonts", "Font", "xobjects", "XObject", "ext_gstates", "ExtGState", "patterns", "Pattern", "colorSpaces", "ColorSpace", "annotations", "Ann<PERSON>", "structParentTreeKey", "StructParents", "createStructParentTreeNextKey", "maxY", "_setTabOrder", "Tabs", "hasMarkInfoDictionary", "color", "PDFNameTree", "localeCompare", "k", "inRange", "rangeGroup", "startRange", "endRange", "<PERSON><PERSON><PERSON><PERSON>", "floor", "arrayIndex", "unassigned_code_points", "isUnassignedCodePoint", "character", "commonly_mapped_to_nothing", "isCommonlyMappedToNothing", "non_ASCII_space_characters", "isNonASCIISpaceCharacter", "non_ASCII_controls_characters", "non_character_codepoints", "prohibited_characters", "isProhibitedCharacter", "bidirectional_r_al", "isBidirectionalRAL", "bidirectional_l", "isBidirectionalL", "mapping2space", "mapping2nothing", "getCodePoint", "codePointAt", "x", "toCodePoints", "input", "codepoints", "before", "next", "saslprep", "opts", "TypeError", "mapped_input", "filter", "normalized_input", "fromCodePoint", "apply", "normalize", "normalized_map", "hasProhibited", "some", "allowUnassigned", "hasUnassigned", "hasBidiRAL", "hasBidiL", "isFirstBidiRAL", "isLastBidiRAL", "PDFSecurity", "generateFileID", "info", "infoStr", "CreationDate", "getTime", "hasOwnProperty", "wordArrayToBuffer", "CryptoJS", "MD5", "generateRandomWordArray", "bytes", "lib", "WordArray", "random", "create", "ownerPassword", "userPassword", "_setupEncryption", "pdfVersion", "version", "encDict", "_setupEncryptionV1V2V4", "_setupEncryptionV5", "v", "r", "permissions", "keyBits", "getPermissionsR2", "getPermissionsR3", "paddedUserPassword", "processPasswordR2R3R4", "paddedOwnerPassword", "ownerPasswordEntry", "getOwnerPasswordR2R3R4", "<PERSON><PERSON><PERSON>", "getEncryptionKeyR2R3R4", "_id", "userPasswordEntry", "getUserPasswordR2", "getUserPasswordR3R4", "V", "CF", "StdCF", "AuthEvent", "CFM", "StmF", "StrF", "R", "O", "U", "P", "processedUserPassword", "processPasswordR5", "processedOwnerPassword", "getEncryptionKeyR5", "getUserPasswordR5", "userKeySalt", "words", "userEncryptionKeyEntry", "getUserEncryptionKeyR5", "getOwnerPasswordR5", "ownerKeySalt", "ownerEncryptionKeyEntry", "getOwnerEncryptionKeyR5", "permsEntry", "getEncryptedPermissionsR5", "OE", "UE", "Perms", "obj", "digest", "clone", "sigBytes", "min", "RC4", "encrypt", "ciphertext", "iv", "mode", "CBC", "padding", "Pkcs7", "AES", "permissionObject", "printing", "modifying", "copying", "annotating", "fillingForms", "contentAccessibility", "documentAssembly", "documentId", "cipher", "xorRound", "ceil", "j", "lsbFirstWord", "validationSalt", "keySalt", "SHA256", "NoPadding", "ECB", "password", "alloc", "index", "code", "PASSWORD_PADDING", "unescape", "encodeURIComponent", "wordArray", "byteArray", "PDFGradient", "stops", "embedded", "transform", "stop", "pos", "opacity", "_normalizeColor", "_colorSpace", "max", "setTransform", "m11", "m12", "m21", "m22", "dx", "dy", "embed", "m", "fn", "<PERSON><PERSON><PERSON><PERSON>", "matrix", "bounds", "encode", "Functions", "Bounds", "Encode", "_gradCount", "shader", "pattern", "PatternType", "Shading", "Matrix", "grad", "opacityGradient", "pageBBox", "page", "form", "Subtype", "FormType", "BBox", "Group", "S", "CS", "Sh1", "gstate", "SMask", "G", "opacityPattern", "PaintType", "TilingType", "XStep", "YStep", "Gs1", "stroke", "m0", "m1", "m2", "m3", "m4", "m5", "_ctm", "_setColorSpace", "op", "addContent", "PDFLinearGradient", "x1", "y1", "x2", "y2", "ShadingType", "<PERSON><PERSON><PERSON>", "Function", "Extend", "PDFRadialGradient", "r1", "r2", "underlyingColorSpaces", "PDFTilingPattern", "bBox", "xStep", "yStep", "stream", "createPattern", "toFixed", "embedPatternColorSpaces", "for<PERSON>ach", "csName", "csId", "getPatternColorSpaceId", "cs", "underlyingColorspace", "_patternCount", "patternColor", "normalizedColor", "_getColorSpace", "Gradient", "initColor", "_opacityRegistry", "_opacityCount", "char<PERSON>t", "hex", "parseInt", "namedColors", "part", "_setColor", "_setColorCore", "space", "fillColor", "set", "fillOpacity", "_fillColor", "strokeColor", "strokeOpacity", "_doOpacity", "ca", "CA", "linearGradient", "radialGradient", "bbox", "addSpotColor", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "grey", "green", "greenyellow", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "cx", "cy", "px", "py", "sx", "sy", "parameters", "A", "H", "h", "L", "Q", "q", "s", "T", "t", "Z", "z", "parse", "path", "cmd", "ret", "args", "curArg", "foundDecimal", "params", "includes", "commands", "runners", "moveTo", "bezierCurveTo", "quadraticCurveTo", "solveArc", "lineTo", "closePath", "y", "coords", "rx", "ry", "rot", "large", "sweep", "ex", "ey", "segs", "arcToSegments", "seg", "bez", "segmentToBezier", "rotateX", "ox", "oy", "th", "PI", "sin_th", "sin", "cos_th", "cos", "abs", "pl", "sqrt", "a00", "a01", "a10", "a11", "x0", "y0", "d", "sfactor_sq", "sfactor", "xc", "yc", "th0", "atan2", "th1", "th_arc", "segments", "result", "th2", "th3", "th_half", "x3", "y3", "SVGPath", "KAPPA", "initVector", "_ctmStack", "save", "restore", "pop", "lineWidth", "w", "_CAP_STYLES", "BUTT", "ROUND", "SQUARE", "lineCap", "_JOIN_STYLES", "MITER", "BEVEL", "lineJoin", "miterLimit", "dash", "original<PERSON>ength", "valid", "every", "Number", "isFinite", "JSON", "stringify", "phase", "undash", "cp1x", "cp1y", "cp2x", "cp2y", "cpx", "cpy", "rect", "roundedRect", "ellipse", "xe", "ye", "xm", "ym", "circle", "radius", "arc", "startAngle", "endAngle", "anticlockwise", "TWO_PI", "HALF_PI", "deltaAng", "dir", "numSegs", "segAng", "handleLen", "curAng", "deltaCx", "deltaCy", "ax", "ay", "segIdx", "polygon", "points", "shift", "point", "_windingRule", "rule", "test", "fill", "fillAndStroke", "isFillRule", "clip", "translate", "rotate", "angle", "rad", "origin", "scale", "xFactor", "yFactor", "WIN_ANSI_MAP", "characters", "split", "AFMFont", "open", "filename", "fs", "readFileSync", "contents", "attributes", "glyphWidths", "boundingBoxes", "kernPairs", "char<PERSON><PERSON><PERSON>", "char", "ascender", "descender", "xHeight", "capHeight", "lineGap", "section", "line", "match", "encodeText", "text", "res", "len", "glyphsForString", "glyphs", "charCode", "characterToGlyph", "widthOfGlyph", "glyph", "getKernPair", "advancesForGlyphs", "advances", "PDFFont", "widthOfString", "lineHeight", "includeGap", "gap", "STANDARD_FONTS", "Courier", "__dirname", "Courier-Bold", "Courier-Oblique", "Courier-BoldOblique", "Helvetica", "Helvetica-Bold", "Helvetica-Oblique", "Helvetica-BoldOblique", "Times-Roman", "Times-Bold", "Times-Italic", "Times-BoldItalic", "Symbol", "ZapfDingbats", "StandardFont", "font", "BaseFont", "Encoding", "encoded", "positions", "xAdvance", "yAdvance", "xOffset", "yOffset", "advanceWidth", "advance", "isStandardFont", "toHex", "num", "EmbeddedFont", "subset", "createSubset", "unicode", "widths", "getGlyph", "postscriptName", "unitsPerEm", "ascent", "descent", "fontLayoutCache", "layoutCache", "layoutRun", "features", "run", "position", "layoutCached", "cached", "only<PERSON><PERSON><PERSON>", "needle", "gid", "includeGlyph", "codePoints", "isCFF", "cff", "fontFile", "encodeStream", "on", "familyClass", "sFamilyClass", "undefined", "flags", "post", "isFixedPitch", "head", "macStyle", "italic", "tag", "fromCharCode", "replaceAll", "descriptor", "FontName", "Flags", "FontBBox", "minX", "minY", "maxX", "ItalicAngle", "italicAngle", "Ascent", "Descent", "CapHeight", "XHeight", "StemV", "FontFile3", "FontFile2", "CIDSet", "CIDSetRef", "descendantFontData", "CIDSystemInfo", "Registry", "Ordering", "Supplement", "FontDescriptor", "W", "CIDToGIDMap", "descendantFont", "DescendantFonts", "ToUnicode", "toUnicodeCmap", "cmap", "entries", "chunkSize", "chunks", "ranges", "start", "PDFFontFactory", "src", "family", "fontkit", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isEqualFont", "font1", "font2", "_tables", "checkSumAdjustment", "records", "initFonts", "defaultFont", "_fontFamilies", "_fontCount", "_fontSize", "_font", "_registeredFonts", "cache<PERSON>ey", "fontSize", "currentLineHeight", "registerFont", "SOFT_HYPHEN", "HYPHEN", "LineWrapper", "EventEmitter", "horizontalScaling", "indent", "characterSpacing", "wordSpacing", "columns", "columnGap", "spaceLeft", "startX", "startY", "column", "ellipsis", "continuedX", "indentAllLines", "once", "continued", "align", "lastLine", "paragraphGap", "wordWidth", "word", "canFit", "eachWord", "bk", "breaker", "LineBreaker", "wordWidths", "nextBreak", "shouldC<PERSON><PERSON>ue", "lbk", "fbk", "<PERSON><PERSON>row", "mustShrink", "required", "wrap", "nextY", "nextSection", "textWidth", "wc", "lc", "emitLine", "wordCount", "emit", "lh", "continueOnNewPage", "initText", "_line", "bind", "_lineGap", "moveDown", "lines", "moveUp", "_text", "lineCallback", "_initOptions", "addStructure", "structParent", "struct", "structType", "mark<PERSON><PERSON><PERSON><PERSON><PERSON>nt", "wrapper", "_wrapper", "_textOptions", "heightOfString", "Infinity", "list", "listType", "unit", "midLine", "bulletRadius", "textIndent", "itemIndent", "bulletIndent", "level", "levels", "numbers", "flatten", "item", "label", "letter", "times", "drawListItem", "listItem", "itemType", "labelType", "bodyType", "structTypes", "diff", "_fragment", "assign", "lineBreak", "trim", "spaceWidth", "baseline", "rendered<PERSON><PERSON><PERSON>", "link", "goTo", "destination", "addNamedDestination", "underline", "lineY", "strike", "oblique", "skew", "encodedWord", "positionsWord", "hadOffset", "addSegment", "cur", "flush", "MARKERS", "COLOR_SPACE_MAP", "JPEG", "marker", "readUInt16BE", "orientation", "exif", "fromBuffer", "Orientation", "bits", "channels", "colorSpace", "BitsPerComponent", "<PERSON><PERSON><PERSON>", "Height", "PNGImage", "image", "PNG", "imgData", "dataDecoded", "hasAlphaChannel", "isInterlaced", "interlace<PERSON>ethod", "Predictor", "Colors", "colors", "Columns", "palette", "transparency", "grayscale", "rgb", "mask", "indexed", "loadIndexedAlphaChannel", "splitAlphaChannel", "decodeData", "alphaChannel", "sMask", "Decode", "decodePixels", "pixels", "p", "colorCount", "pixelCount", "skipByteCount", "colorIndex", "PDFImage", "exec", "initImages", "_imageRegistry", "_imageCount", "bh", "bp", "bw", "ip", "left1", "rotateAngle", "originX", "originY", "ignoreOrientation", "openImage", "wp", "hp", "fit", "cover", "valign", "annotate", "Rect", "_convertRect", "Border", "F", "Dest", "note", "Name", "D", "url", "pages", "Kids", "URI", "_markup", "QuadPoints", "highlight", "lineAnnotation", "rectAnnotation", "ellipseAnnotation", "textAnnotation", "DA", "fileAnnotation", "file", "filespec", "hidden", "FS", "Desc", "PDFOutline", "parent", "title", "dest", "expanded", "outlineData", "children", "addItem", "endOutline", "Count", "First", "Last", "child", "Prev", "Next", "initOutline", "outline", "Outlines", "PageMode", "PDFStructureContent", "pageRef", "mcid", "refs", "structContent", "PDFStructureElement", "type", "_attached", "_ended", "_flushed", "_is<PERSON><PERSON>d<PERSON><PERSON><PERSON>", "lang", "<PERSON>", "alt", "Alt", "E", "actual", "ActualText", "_children", "setParent", "setAttached", "_addContentToParentTree", "_contentForClosure", "pageStructParents", "getStructParentTree", "parentRef", "_flush", "closure", "endMarkedContent", "_is<PERSON><PERSON>hable", "_<PERSON><PERSON><PERSON>d", "Pg", "MCID", "PDFNumberTree", "initMarkings", "struct<PERSON><PERSON><PERSON><PERSON>", "tagged", "getMarkInfoDictionary", "Marked", "getStructTreeRoot", "<PERSON><PERSON><PERSON><PERSON>", "toClose", "marking", "attached", "Attached", "structElem", "structTreeRoot", "initPageMarkings", "pageMarkings", "newStructContent", "endPageMarkings", "MarkInfo", "StructTreeRoot", "<PERSON><PERSON><PERSON><PERSON>", "ParentTreeNextKey", "endMarkings", "FIELD_FLAGS", "readOnly", "noExport", "multiline", "toggleToOffButton", "radioButton", "pushButton", "combo", "edit", "multiSelect", "noSpell", "FIELD_JUSTIFY", "center", "VALUE_MAP", "defaultValue", "FORMAT_SPECIAL", "zip", "zipPlus4", "zip4", "phone", "ssn", "FORMAT_DEFAULT", "nDec", "sepComma", "negStyle", "currency", "currencyPrepend", "percent", "initForm", "_acroform", "Fields", "NeedAppearances", "DR", "AcroForm", "endAcroForm", "fontDict", "fieldRef", "_endChild", "childRef", "formField", "fieldDict", "_fieldDict", "_addToParent", "formAnnotation", "annotRef", "formText", "formPushButton", "formCombo", "formList", "formRadioButton", "formCheckbox", "_resolveType", "_resolveFlags", "_resolveJustify", "_resolveFont", "_resolveStrings", "_resolveColors", "_resolveFormat", "FT", "f", "format", "fnKeystroke", "fnFormat", "param", "AA", "JS", "backgroundColor", "MK", "BG", "borderColor", "BC", "Ff", "select", "appendChoices", "idx", "<PERSON><PERSON>", "relationship", "refBody", "Params", "birthtime", "ctime", "statSync", "ModDate", "creationDate", "modifiedDate", "checksum", "CheckSum", "Size", "byteLength", "_fileRegistry", "isEqual", "fileSpecBody", "AFRelationship", "EF", "UF", "description", "addNamedEmbeddedFile", "AF", "initPDFA", "pSubset", "subset_conformance", "endSubset", "_addPdfaMetadata", "_addColorOutputIntent", "iccProfile", "colorProfileRef", "intentRef", "Info", "OutputConditionIdentifier", "DestOutputProfile", "OutputIntents", "_get<PERSON>d<PERSON>id", "appendXML", "initPDFUA", "_addPdfuaMetadata", "_getPdfuaid", "_importSubset", "initSubset", "PDFA", "PDFUA", "PDFMetadata", "_metadata", "_closeTags", "append", "xml", "newline", "getXML", "<PERSON><PERSON><PERSON><PERSON>", "initMetadata", "metadata", "_addInfo", "toISOString", "Creator", "Title", "Author", "Subject", "Keywords", "endMetadata", "metadataRef", "<PERSON><PERSON><PERSON>", "PDFDocument", "Readable", "_pageBuffer", "_pageBufferStart", "_offsets", "_waiting", "Names", "Des<PERSON>", "Producer", "displayTitle", "ViewerPreferences", "DisplayDocTitle", "autoFirstPage", "addPage", "bufferPages", "flushPages", "bufferedPageRange", "count", "switchToPage", "unshift", "EmbeddedFiles", "addNamedJavaScript", "js", "JavaScript", "_read", "_finalize", "_info", "entry", "xRefOffset", "trailer", "Root", "ID", "Encrypt", "mixin", "methods", "prototype", "MetadataMixin", "ColorMixin", "VectorMixin", "FontsMixin", "TextMixin", "ImagesMixin", "AnnotationsMixin", "OutlineMixin", "MarkingsMixin", "AcroFormMixin", "AttachmentsMixin", "SubsetMixin"], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;;AAEA,MAAMA,oBAAoB,CAAC;EACzBC,QAAQA,GAAG;IACT,MAAM,IAAIC,KAAK,CAAC,mCAAmC,CAAC;;AAExD;;ACRA;AACA;AACA;AAIA,MAAMC,OAAO,CAAC;EACZC,WAAWA,CAACC,OAAO,GAAG,EAAE,EAAE;IACxB,IAAI,CAACC,MAAM,GAAG,EAAE;;IAEhB,IAAI,CAACC,MAAM,GACT,OAAOF,OAAO,CAACE,MAAM,KAAK,SAAS,GAAGF,OAAO,CAACE,MAAM,GAAG,IAAI;;EAG/DC,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAE;IACZ,OAAQ,IAAI,CAACJ,MAAM,CAACG,GAAG,CAAC,GAAGC,GAAG;;EAGhCC,GAAGA,CAACF,GAAG,EAAE;IACP,OAAO,IAAI,CAACH,MAAM,CAACG,GAAG,CAAC;;EAGzBR,QAAQA,GAAG;;IAET,MAAMW,UAAU,GAAGC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACR,MAAM,CAAC,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACpD,IAAI,CAACC,YAAY,CAACF,CAAC,EAAEC,CAAC,CACxB,CAAC;IAED,MAAME,GAAG,GAAG,CAAC,IAAI,CAAC;IAClB,IAAI,IAAI,CAACZ,MAAM,IAAIK,UAAU,CAACQ,MAAM,GAAG,CAAC,EAAE;MACxC,MAAMC,KAAK,GAAGT,UAAU,CAAC,CAAC,CAAC;QACzBU,IAAI,GAAGV,UAAU,CAACA,UAAU,CAACQ,MAAM,GAAG,CAAC,CAAC;MAC1CD,GAAG,CAACI,IAAI,CACL,aAAYC,SAAS,CAACC,OAAO,CAAC,CAAC,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC,EAAE,IAAI,CAACK,WAAW,CAACJ,IAAI,CAAC,CAAC,CAAE,EACpF,CAAC;;IAEHH,GAAG,CAACI,IAAI,CAAE,MAAK,IAAI,CAACI,SAAS,EAAG,IAAG,CAAC;IACpC,KAAK,IAAIlB,GAAG,IAAIG,UAAU,EAAE;MAC1BO,GAAG,CAACI,IAAI,CACL,OAAMC,SAAS,CAACC,OAAO,CAAC,IAAI,CAACC,WAAW,CAACjB,GAAG,CAAC,CAAE,IAAGe,SAAS,CAACC,OAAO,CAClE,IAAI,CAACnB,MAAM,CAACG,GAAG,CACjB,CAAE,EACJ,CAAC;;IAEHU,GAAG,CAACI,IAAI,CAAC,GAAG,CAAC;IACbJ,GAAG,CAACI,IAAI,CAAC,IAAI,CAAC;IACd,OAAOJ,GAAG,CAACS,IAAI,CAAC,IAAI,CAAC;;EAGvBV,YAAYA;IAAW;IACrB,MAAM,IAAIhB,KAAK,CAAC,mCAAmC,CAAC;;EAGtDyB,SAASA,GAAG;IACV,MAAM,IAAIzB,KAAK,CAAC,mCAAmC,CAAC;;EAGtDwB,WAAWA;IAAQ;IACjB,MAAM,IAAIxB,KAAK,CAAC,mCAAmC,CAAC;;AAExD;;AC5De,MAAM2B,SAAS,CAAC;EAC7BzB,WAAWA,CAAC0B,GAAG,EAAEC,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACjC,IAAI,CAACC,EAAE,GAAG,IAAI,GAAGvB,MAAM,CAACC,IAAI,CAACgB,GAAG,CAACO,UAAU,CAAC,CAACjB,MAAM;IACnD,IAAI,CAACW,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACO,MAAM,GAAG,CAACN,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IAC1B,IAAI,CAACI,GAAG,GAAGT,GAAG,CAACS,GAAG,CAAC,CACjB,YAAY,EACZ,IAAI,CAACR,IAAI,EACT,YAAY,EACZ;MACES,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC/BC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAChBC,EAAE,EAAE,IAAI,CAACJ,MAAM,CAACK,GAAG,CAACC,KAAK,IAAIA,KAAK,GAAG,GAAG,CAAC;MACzCC,YAAY,EAAE,CAAC;MACfC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACdC,CAAC,EAAE;KACJ,CACF,CAAC;IACF,IAAI,CAACR,GAAG,CAACS,GAAG,EAAE;;EAGhB/C,QAAQA,GAAG;IACT,OAAQ,GAAE,IAAI,CAACsC,GAAG,CAACH,EAAG,MAAK;;AAE/B;;ACxBA;AACA;AACA;AACA;AAMA,MAAMa,GAAG,GAAGA,CAACC,GAAG,EAAE9B,MAAM,KAAK,CAAC+B,KAAK,CAAC/B,MAAM,GAAG,CAAC,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC,GAAGsB,GAAG,EAAEE,KAAK,CAAC,CAAChC,MAAM,CAAC;AAE/E,MAAMiC,WAAW,GAAG,mBAAmB;AACvC,MAAMC,SAAS,GAAG;EAChB,IAAI,EAAE,KAAK;EACX,IAAI,EAAE,KAAK;EACX,IAAI,EAAE,KAAK;EACX,IAAI,EAAE,KAAK;EACX,IAAI,EAAE,KAAK;EACX,IAAI,EAAE,MAAM;EACZ,GAAG,EAAE,KAAK;EACV,GAAG,EAAE;AACP,CAAC;;AAED;AACA,MAAMC,SAAS,GAAG,UAASC,IAAI,EAAE;EAC/B,MAAMC,CAAC,GAAGD,IAAI,CAACpC,MAAM;EACrB,IAAIqC,CAAC,GAAG,IAAI,EAAE;IACZ,MAAM,IAAIvD,KAAK,CAAC,4BAA4B,CAAC;GAC9C,MAAM;IACL,KAAK,IAAIwD,CAAC,GAAG,CAAC,EAAEV,GAAG,GAAGS,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGV,GAAG,EAAEU,CAAC,IAAI,CAAC,EAAE;MAC5C,MAAM1C,CAAC,GAAGwC,IAAI,CAACE,CAAC,CAAC;MACjBF,IAAI,CAACE,CAAC,CAAC,GAAGF,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC;MACrBF,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC,GAAG1C,CAAC;;;EAInB,OAAOwC,IAAI;AACb,CAAC;AAED,MAAMhC,SAAS,CAAC;EACd,OAAOC,OAAOA,CAACkC,MAAM,EAAEC,SAAS,GAAG,IAAI,EAAE;;IAEvC,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAQ,IAAGA,MAAO,EAAC;;;KAGpB,MAAM,IAAIA,MAAM,YAAYE,MAAM,EAAE;MACnC,IAAIC,MAAM,GAAGH,MAAM;;MAEnB,IAAII,SAAS,GAAG,KAAK;MACrB,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEV,GAAG,GAAGc,MAAM,CAAC1C,MAAM,EAAEsC,CAAC,GAAGV,GAAG,EAAEU,CAAC,EAAE,EAAE;QACjD,IAAII,MAAM,CAACE,UAAU,CAACN,CAAC,CAAC,GAAG,IAAI,EAAE;UAC/BK,SAAS,GAAG,IAAI;UAChB;;;;;MAKJ,IAAIE,YAAY;MAChB,IAAIF,SAAS,EAAE;QACbE,YAAY,GAAGV,SAAS,CAACW,MAAM,CAACC,IAAI,CAAE,SAAQL,MAAO,EAAC,EAAE,SAAS,CAAC,CAAC;OACpE,MAAM;QACLG,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACL,MAAM,CAACM,OAAO,EAAE,EAAE,OAAO,CAAC;;;;MAIvD,IAAIR,SAAS,EAAE;QACbE,MAAM,GAAGF,SAAS,CAACK,YAAY,CAAC,CAAChE,QAAQ,CAAC,QAAQ,CAAC;OACpD,MAAM;QACL6D,MAAM,GAAGG,YAAY,CAAChE,QAAQ,CAAC,QAAQ,CAAC;;;;MAI1C6D,MAAM,GAAGA,MAAM,CAACO,OAAO,CAAChB,WAAW,EAAEiB,CAAC,IAAIhB,SAAS,CAACgB,CAAC,CAAC,CAAC;MAEvD,OAAQ,IAAGR,MAAO,GAAE;;;KAGrB,MAAM,IAAII,MAAM,CAACK,QAAQ,CAACZ,MAAM,CAAC,EAAE;MAClC,OAAQ,IAAGA,MAAM,CAAC1D,QAAQ,CAAC,KAAK,CAAE,GAAE;KACrC,MAAM,IACL0D,MAAM,YAAY3D,oBAAoB,IACtC2D,MAAM,YAAYxD,OAAO,IACzBwD,MAAM,YAAY9B,SAAS,EAC3B;MACA,OAAO8B,MAAM,CAAC1D,QAAQ,EAAE;KACzB,MAAM,IAAI0D,MAAM,YAAYa,IAAI,EAAE;MACjC,IAAIV,MAAM,GACP,KAAIb,GAAG,CAACU,MAAM,CAACc,cAAc,EAAE,EAAE,CAAC,CAAE,EAAC,GACtCxB,GAAG,CAACU,MAAM,CAACe,WAAW,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,GAChCzB,GAAG,CAACU,MAAM,CAACgB,UAAU,EAAE,EAAE,CAAC,CAAC,GAC3B1B,GAAG,CAACU,MAAM,CAACiB,WAAW,EAAE,EAAE,CAAC,CAAC,GAC5B3B,GAAG,CAACU,MAAM,CAACkB,aAAa,EAAE,EAAE,CAAC,CAAC,GAC9B5B,GAAG,CAACU,MAAM,CAACmB,aAAa,EAAE,EAAE,CAAC,CAAC,GAC9B,GAAG;;;MAGL,IAAIlB,SAAS,EAAE;QACbE,MAAM,GAAGF,SAAS,CAACM,MAAM,CAACC,IAAI,CAACL,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC7D,QAAQ,CAAC,QAAQ,CAAC;;;QAGnE6D,MAAM,GAAGA,MAAM,CAACO,OAAO,CAAChB,WAAW,EAAEiB,CAAC,IAAIhB,SAAS,CAACgB,CAAC,CAAC,CAAC;;MAGzD,OAAQ,IAAGR,MAAO,GAAE;KACrB,MAAM,IAAIX,KAAK,CAAC4B,OAAO,CAACpB,MAAM,CAAC,EAAE;MAChC,MAAMqB,KAAK,GAAGrB,MAAM,CAAChB,GAAG,CAACsC,CAAC,IAAIzD,SAAS,CAACC,OAAO,CAACwD,CAAC,EAAErB,SAAS,CAAC,CAAC,CAAChC,IAAI,CAAC,GAAG,CAAC;MACxE,OAAQ,IAAGoD,KAAM,GAAE;KACpB,MAAM,IAAI,EAAE,CAAC/E,QAAQ,CAACiF,IAAI,CAACvB,MAAM,CAAC,KAAK,iBAAiB,EAAE;MACzD,MAAMxC,GAAG,GAAG,CAAC,IAAI,CAAC;MAClB,KAAK,IAAIV,GAAG,IAAIkD,MAAM,EAAE;QACtB,MAAMjD,GAAG,GAAGiD,MAAM,CAAClD,GAAG,CAAC;QACvBU,GAAG,CAACI,IAAI,CAAE,IAAGd,GAAI,IAAGe,SAAS,CAACC,OAAO,CAACf,GAAG,EAAEkD,SAAS,CAAE,EAAC,CAAC;;MAG1DzC,GAAG,CAACI,IAAI,CAAC,IAAI,CAAC;MACd,OAAOJ,GAAG,CAACS,IAAI,CAAC,IAAI,CAAC;KACtB,MAAM,IAAI,OAAO+B,MAAM,KAAK,QAAQ,EAAE;MACrC,OAAOnC,SAAS,CAAC2D,MAAM,CAACxB,MAAM,CAAC;KAChC,MAAM;MACL,OAAQ,GAAEA,MAAO,EAAC;;;EAItB,OAAOwB,MAAMA,CAACC,CAAC,EAAE;IACf,IAAIA,CAAC,GAAG,CAAC,IAAI,IAAIA,CAAC,GAAG,IAAI,EAAE;MACzB,OAAOC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;;IAGlC,MAAM,IAAIlF,KAAK,CAAE,uBAAsBkF,CAAE,EAAC,CAAC;;AAE/C;;ACnIA;AACA;AACA;AACA;AAMA,MAAMG,YAAY,SAASvF,oBAAoB,CAAC;EAC9CI,WAAWA,CAACoF,QAAQ,EAAEpD,EAAE,EAAEqD,IAAI,GAAG,EAAE,EAAE;IACnC,KAAK,EAAE;IACP,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACpD,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACqD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACH,QAAQ,CAACG,QAAQ,IAAI,CAAC,IAAI,CAACF,IAAI,CAACG,MAAM;IAC3D,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,MAAM,GAAG,EAAE;;EAGlBC,KAAKA,CAACC,KAAK,EAAE;IACX,IAAI,CAAC9B,MAAM,CAACK,QAAQ,CAACyB,KAAK,CAAC,EAAE;MAC3BA,KAAK,GAAG9B,MAAM,CAACC,IAAI,CAAC6B,KAAK,GAAG,IAAI,EAAE,QAAQ,CAAC;;IAG7C,IAAI,CAACH,kBAAkB,IAAIG,KAAK,CAAC5E,MAAM;IACvC,IAAI,IAAI,CAACqE,IAAI,CAACQ,MAAM,IAAI,IAAI,EAAE;MAC5B,IAAI,CAACR,IAAI,CAACQ,MAAM,GAAG,CAAC;;IAEtB,IAAI,CAACH,MAAM,CAACvE,IAAI,CAACyE,KAAK,CAAC;IACvB,IAAI,CAACP,IAAI,CAACQ,MAAM,IAAID,KAAK,CAAC5E,MAAM;IAChC,IAAI,IAAI,CAACuE,QAAQ,EAAE;MACjB,OAAQ,IAAI,CAACF,IAAI,CAACG,MAAM,GAAG,aAAa;;;EAI5C5C,GAAGA,CAACgD,KAAK,EAAE;IACT,IAAIA,KAAK,EAAE;MACT,IAAI,CAACD,KAAK,CAACC,KAAK,CAAC;;IAEnB,OAAO,IAAI,CAACE,QAAQ,EAAE;;EAGxBA,QAAQA,GAAG;IACT,IAAI,CAACC,MAAM,GAAG,IAAI,CAACX,QAAQ,CAACY,OAAO;IAEnC,MAAMxC,SAAS,GAAG,IAAI,CAAC4B,QAAQ,CAACa,SAAS,GACrC,IAAI,CAACb,QAAQ,CAACa,SAAS,CAACC,YAAY,CAAC,IAAI,CAAClE,EAAE,EAAE,IAAI,CAACsD,GAAG,CAAC,GACvD,IAAI;IAER,IAAI,IAAI,CAACI,MAAM,CAAC1E,MAAM,EAAE;MACtB,IAAI,CAAC0E,MAAM,GAAG5B,MAAM,CAACqC,MAAM,CAAC,IAAI,CAACT,MAAM,CAAC;MACxC,IAAI,IAAI,CAACH,QAAQ,EAAE;QACjB,IAAI,CAACG,MAAM,GAAGU,IAAI,CAACC,WAAW,CAAC,IAAI,CAACX,MAAM,CAAC;;MAG7C,IAAIlC,SAAS,EAAE;QACb,IAAI,CAACkC,MAAM,GAAGlC,SAAS,CAAC,IAAI,CAACkC,MAAM,CAAC;;MAGtC,IAAI,CAACL,IAAI,CAACQ,MAAM,GAAG,IAAI,CAACH,MAAM,CAAC1E,MAAM;;IAGvC,IAAI,CAACoE,QAAQ,CAACkB,MAAM,CAAE,GAAE,IAAI,CAACtE,EAAG,IAAG,IAAI,CAACsD,GAAI,MAAK,CAAC;IAClD,IAAI,CAACF,QAAQ,CAACkB,MAAM,CAAClF,SAAS,CAACC,OAAO,CAAC,IAAI,CAACgE,IAAI,EAAE7B,SAAS,CAAC,CAAC;IAE7D,IAAI,IAAI,CAACkC,MAAM,CAAC1E,MAAM,EAAE;MACtB,IAAI,CAACoE,QAAQ,CAACkB,MAAM,CAAC,QAAQ,CAAC;MAC9B,IAAI,CAAClB,QAAQ,CAACkB,MAAM,CAAC,IAAI,CAACZ,MAAM,CAAC;MAEjC,IAAI,CAACA,MAAM,GAAG,EAAE,CAAC;MACjB,IAAI,CAACN,QAAQ,CAACkB,MAAM,CAAC,aAAa,CAAC;;IAGrC,IAAI,CAAClB,QAAQ,CAACkB,MAAM,CAAC,QAAQ,CAAC;IAC9B,IAAI,CAAClB,QAAQ,CAACmB,OAAO,CAAC,IAAI,CAAC;;EAE7B1G,QAAQA,GAAG;IACT,OAAQ,GAAE,IAAI,CAACmC,EAAG,IAAG,IAAI,CAACsD,GAAI,IAAG;;AAErC;;ACjFA;AACA;AACA;AACA;;AAEA,MAAMkB,eAAe,GAAG;EACtBC,GAAG,EAAE,EAAE;EACPC,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,KAAK,GAAG;EACZ,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACzB,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACzBC,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACtBC,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACtBC,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACtBC,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EACrBC,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACpBC,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACpBC,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACpBC,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACpBC,EAAE,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;EACnBC,EAAE,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;EACnBC,GAAG,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;EACnBC,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACtBC,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACtBC,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACtBC,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACtBC,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EACrBC,EAAE,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;EACnBC,EAAE,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;EACnBC,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACpBC,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACpBC,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACpBC,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;EACpB9F,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACtBC,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACtB8F,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACtBC,EAAE,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EACrBC,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACpBC,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACpBC,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACpBC,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACpBC,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACpBC,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACpBC,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;EACpBC,GAAG,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EACtBC,GAAG,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;EACtBC,GAAG,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EACtBC,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACrBC,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACrBC,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACxBC,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACxBC,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACxBC,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EACvBC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;EACrBC,SAAS,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;EAC1BC,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EACrBC,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;EACtBC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EACtBC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM;AACzB,CAAC;AAED,MAAMC,OAAO,CAAC;EACZ5J,WAAWA,CAACoF,QAAQ,EAAEnF,OAAO,GAAG,EAAE,EAAE;IAClC,IAAI,CAACmF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACyE,IAAI,GAAG5J,OAAO,CAAC4J,IAAI,IAAI,QAAQ;IACpC,IAAI,CAACC,MAAM,GAAG7J,OAAO,CAAC6J,MAAM,IAAI,UAAU;;;IAG1C,IAAI,OAAO7J,OAAO,CAAC8J,MAAM,KAAK,QAAQ,EAAE;MACtC,IAAI,CAACC,OAAO,GAAG;QACbvD,GAAG,EAAExG,OAAO,CAAC8J,MAAM;QACnBrD,IAAI,EAAEzG,OAAO,CAAC8J,MAAM;QACpBpD,MAAM,EAAE1G,OAAO,CAAC8J,MAAM;QACtBnD,KAAK,EAAE3G,OAAO,CAAC8J;OAChB;;;KAGF,MAAM;MACL,IAAI,CAACC,OAAO,GAAG/J,OAAO,CAAC+J,OAAO,IAAIxD,eAAe;;;;IAInD,MAAMyD,UAAU,GAAGlH,KAAK,CAAC4B,OAAO,CAAC,IAAI,CAACkF,IAAI,CAAC,GACvC,IAAI,CAACA,IAAI,GACThD,KAAK,CAAC,IAAI,CAACgD,IAAI,CAACK,WAAW,EAAE,CAAC;IAClC,IAAI,CAACC,KAAK,GAAGF,UAAU,CAAC,IAAI,CAACH,MAAM,KAAK,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3D,IAAI,CAACM,MAAM,GAAGH,UAAU,CAAC,IAAI,CAACH,MAAM,KAAK,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;IAE5D,IAAI,CAACO,OAAO,GAAG,IAAI,CAACjF,QAAQ,CAACjD,GAAG,EAAE;;;IAGlC,IAAI,CAACmI,SAAS,GAAG,IAAI,CAAClF,QAAQ,CAACjD,GAAG,CAAC;MACjCoI,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;KACtD,CAAC;;;IAGF,IAAI,CAACC,UAAU,GAAG,IAAI,CAACpF,QAAQ,CAACjD,GAAG,CAAC;MAClCsI,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,IAAI,CAACtF,QAAQ,CAACuF,KAAK,CAACtF,IAAI,CAACuF,KAAK;MACtCC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACV,KAAK,EAAE,IAAI,CAACC,MAAM,CAAC;MACzCU,QAAQ,EAAE,IAAI,CAACT,OAAO;MACtBU,SAAS,EAAE,IAAI,CAACT;KACjB,CAAC;IAEF,IAAI,CAACU,QAAQ,GAAG,EAAE;;;;EAIpB,IAAIC,KAAKA,GAAG;IACV,MAAM5F,IAAI,GAAG,IAAI,CAACiF,SAAS,CAACjF,IAAI;IAChC,OAAOA,IAAI,CAAC6F,IAAI,IAAI,IAAI,GAAG7F,IAAI,CAAC6F,IAAI,GAAI7F,IAAI,CAAC6F,IAAI,GAAG,EAAG;;EAGzD,IAAIC,QAAQA,GAAG;IACb,MAAM9F,IAAI,GAAG,IAAI,CAACiF,SAAS,CAACjF,IAAI;IAChC,OAAOA,IAAI,CAAC+F,OAAO,IAAI,IAAI,GAAG/F,IAAI,CAAC+F,OAAO,GAAI/F,IAAI,CAAC+F,OAAO,GAAG,EAAG;;EAGlE,IAAIC,WAAWA,GAAG;IAChB,MAAMhG,IAAI,GAAG,IAAI,CAACiF,SAAS,CAACjF,IAAI;IAChC,OAAOA,IAAI,CAACiG,SAAS,IAAI,IAAI,GAAGjG,IAAI,CAACiG,SAAS,GAAIjG,IAAI,CAACiG,SAAS,GAAG,EAAG;;EAGxE,IAAIC,QAAQA,GAAG;IACb,MAAMlG,IAAI,GAAG,IAAI,CAACiF,SAAS,CAACjF,IAAI;IAChC,OAAOA,IAAI,CAACmG,OAAO,IAAI,IAAI,GAAGnG,IAAI,CAACmG,OAAO,GAAInG,IAAI,CAACmG,OAAO,GAAG,EAAG;;EAGlE,IAAIC,WAAWA,GAAG;IAChB,MAAMpG,IAAI,GAAG,IAAI,CAACiF,SAAS,CAACjF,IAAI;IAChC,OAAOA,IAAI,CAACqG,UAAU,KAAKrG,IAAI,CAACqG,UAAU,GAAG,EAAE,CAAC;;EAGlD,IAAIC,WAAWA,GAAG;IAChB,MAAMtG,IAAI,GAAG,IAAI,CAACmF,UAAU,CAACnF,IAAI;IACjC,OAAOA,IAAI,CAACuG,MAAM,IAAI,IAAI,GAAGvG,IAAI,CAACuG,MAAM,GAAIvG,IAAI,CAACuG,MAAM,GAAG,EAAG;;EAG/D,IAAIC,mBAAmBA,GAAG;IACxB,MAAMxG,IAAI,GAAG,IAAI,CAACmF,UAAU,CAACnF,IAAI;IACjC,OAAOA,IAAI,CAACyG,aAAa,IAAI,IAAI,GAC7BzG,IAAI,CAACyG,aAAa,GACjBzG,IAAI,CAACyG,aAAa,GAAG,IAAI,CAAC1G,QAAQ,CAAC2G,6BAA6B,EAAG;;EAG1EC,IAAIA,GAAG;IACL,OAAO,IAAI,CAAC5B,MAAM,GAAG,IAAI,CAACJ,OAAO,CAACrD,MAAM;;EAG1ChB,KAAKA,CAACC,KAAK,EAAE;IACX,OAAO,IAAI,CAACyE,OAAO,CAAC1E,KAAK,CAACC,KAAK,CAAC;;;;EAIlCqG,YAAYA,GAAG;IACb,IAAI,CAAC,IAAI,CAACzB,UAAU,CAAC0B,IAAI,IAAI,IAAI,CAAC9G,QAAQ,CAAC+G,qBAAqB,EAAE,EAAE;MAClE,IAAI,CAAC3B,UAAU,CAACnF,IAAI,CAAC6G,IAAI,GAAG,GAAG;;;EAInCtJ,GAAGA,GAAG;IACJ,IAAI,CAACqJ,YAAY,EAAE;IACnB,IAAI,CAACzB,UAAU,CAAC5H,GAAG,EAAE;IACrB,IAAI,CAAC0H,SAAS,CAACjF,IAAI,CAACqG,UAAU,GAAG,IAAI,CAACpB,SAAS,CAACjF,IAAI,CAACqG,UAAU,IAAI,EAAE;IACrE,KAAK,IAAIU,KAAK,IAAI3L,MAAM,CAACyB,MAAM,CAAC,IAAI,CAACkD,QAAQ,CAACnD,UAAU,CAAC,EAAE;MACzD,IAAI,CAACqI,SAAS,CAACjF,IAAI,CAACqG,UAAU,CAACU,KAAK,CAACpK,EAAE,CAAC,GAAGoK,KAAK;;IAElD,IAAI,CAAC9B,SAAS,CAAC1H,GAAG,EAAE;IACpB,OAAO,IAAI,CAACyH,OAAO,CAACzH,GAAG,EAAE;;AAE7B;;AC9KA;AACA;AACA;AAIA,MAAMyJ,WAAW,SAAStM,OAAO,CAAC;EAChCe,YAAYA,CAACF,CAAC,EAAEC,CAAC,EAAE;IACjB,OAAOD,CAAC,CAAC0L,aAAa,CAACzL,CAAC,CAAC;;EAG3BU,SAASA,GAAG;IACV,OAAO,OAAO;;EAGhBD,WAAWA,CAACiL,CAAC,EAAE;IACb,OAAO,IAAI9I,MAAM,CAAC8I,CAAC,CAAC;;AAExB;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAAChK,KAAK,EAAEiK,UAAU,EAAE;EAClC,IAAIjK,KAAK,GAAGiK,UAAU,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;EACvC,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIC,QAAQ,GAAGF,UAAU,CAACzL,MAAM,GAAG,CAAC;EACpC,OAAO0L,UAAU,IAAIC,QAAQ,EAAE;IAC7B,MAAMC,WAAW,GAAG3H,IAAI,CAAC4H,KAAK,CAAC,CAACH,UAAU,GAAGC,QAAQ,IAAI,CAAC,CAAC;;;IAG3D,MAAMG,UAAU,GAAGF,WAAW,GAAG,CAAC;;;IAGlC,IACEpK,KAAK,IAAIiK,UAAU,CAACK,UAAU,CAAC,IAC/BtK,KAAK,IAAIiK,UAAU,CAACK,UAAU,GAAG,CAAC,CAAC,EACnC;MACA,OAAO,IAAI;;IAGb,IAAItK,KAAK,GAAGiK,UAAU,CAACK,UAAU,GAAG,CAAC,CAAC,EAAE;;MAEtCJ,UAAU,GAAGE,WAAW,GAAG,CAAC;KAC7B,MAAM;;MAELD,QAAQ,GAAGC,WAAW,GAAG,CAAC;;;EAG9B,OAAO,KAAK;AACd;;AC/BA;AACA;AACA;AACA;AACA;AACA,MAAMG,sBAAsB,GAAG,CAC7B,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACR;AACD;;AAEA,MAAMC,qBAAqB,GAAGC,SAAS,IACrCT,OAAO,CAACS,SAAS,EAAEF,sBAAsB,CAAC;;AAE5C;AACA;AACA;AACA;AACA;AACA,MAAMG,0BAA0B,GAAG,CACjC,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACP;AACD;;AAEA,MAAMC,yBAAyB,GAAGF,SAAS,IACzCT,OAAO,CAACS,SAAS,EAAEC,0BAA0B,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA,MAAME,0BAA0B,GAAG,CACjC,MAAM,EACN,MAAM,uBACN,MAAM,EACN,MAAM,yBACN,MAAM,EACN,MAAM,gBACN,MAAM,EACN,MAAM,gBACN,MAAM,EACN,MAAM,iBACN,MAAM,EACN,MAAM,iBACN,MAAM,EACN,MAAM,2BACN,MAAM,EACN,MAAM,0BACN,MAAM,EACN,MAAM,yBACN,MAAM,EACN,MAAM,qBACN,MAAM,EACN,MAAM,0BACN,MAAM,EACN,MAAM,mBACN,MAAM,EACN,MAAM,mBACN,MAAM,EACN,MAAM,yBACN,MAAM,EACN,MAAM,8BACN,MAAM,EACN,MAAM,kCACN,MAAM,EACN,MAAM,yBACP;AACD;;AAEA,MAAMC,wBAAwB,GAAGJ,SAAS,IACxCT,OAAO,CAACS,SAAS,EAAEG,0BAA0B,CAAC;;AAEhD;AACA,MAAME,6BAA6B,GAAG;AACpC;AACF;AACA;AACA;AACE,MAAM,EACN,MAAM,6BACN,MAAM,EACN,MAAM,2BACN,MAAM,EACN,MAAM,iCACN,MAAM,EACN,MAAM,kCACN,MAAM,EACN,MAAM,8BACN,MAAM,EACN,MAAM,0BACN,MAAM,EACN,MAAM,uBACN,MAAM,EACN,MAAM,4BACN,MAAM,EACN,MAAM,oBACN,MAAM,EACN,MAAM,6BACN,MAAM,EACN,MAAM,wBACN,MAAM,EACN,MAAM,4BACN,MAAM,EACN,MAAM,6BACN,MAAM,EACN,MAAM,kCACN,MAAM,EACN,MAAM,6BACN,OAAO,EACP,OAAO,oCACR;AAED,MAAMC,wBAAwB,GAAG;AAC/B;AACF;AACA;AACA;AACE,MAAM,EACN,MAAM,mCACN,MAAM,EACN,MAAM,mCACN,OAAO,EACP,OAAO,mCACP,OAAO,EACP,OAAO,mCACP,OAAO,EACP,OAAO,mCACP,OAAO,EACP,OAAO,mCACP,OAAO,EACP,OAAO,mCACP,OAAO,EACP,OAAO,mCACP,OAAO,EACP,OAAO,mCACP,OAAO,EACP,OAAO,mCACP,OAAO,EACP,OAAO,mCACP,OAAO,EACP,OAAO,mCACP,OAAO,EACP,OAAO,mCACP,OAAO,EACP,OAAO,mCACP,OAAO,EACP,OAAO,mCACP,OAAO,EACP,OAAO,mCACP,QAAQ,EACR,QAAQ,kCACT;;AAED;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG;AAC5B;AACF;AACA;AACA;AACE,CAAC,EACD,MAAM,6BACN,MAAM,EACN,MAAM;AAEN;AACF;AACA;AACA;AACE,MAAM,EACN,MAAM,kCACN,MAAM,EACN,MAAM,kCACN,MAAM,EACN,MAAM,2BACN,MAAM,EACN,MAAM,2BACN,MAAM,EACN,MAAM,gCACN,MAAM,EACN,MAAM,gCACN,MAAM,EACN,MAAM,mCACN,MAAM,EACN,MAAM,+BACN,MAAM,EACN,MAAM,+BACN,MAAM,EACN,MAAM,mCACN,MAAM,EACN,MAAM,oCACN,MAAM,EACN,MAAM,oCACN,MAAM,EACN,MAAM,qCACN,MAAM,EACN,MAAM,8BACN,MAAM,EACN,MAAM;AAEN;AACF;AACA;AACA;AACE,MAAM,EACN,MAAM;AAEN;AACF;AACA;AACA;AACE,MAAM,EACN,MAAM;AAEN;AACF;AACA;AACA;AACE,MAAM,EACN,MAAM;AAEN;AACF;AACA;AACA;AACE,MAAM,EACN,MAAM,sCACN,MAAM,EACN,MAAM,yCACN,MAAM,EACN,MAAM,0CACN,MAAM,EACN,MAAM,qCACN,MAAM,EACN,MAAM;AAEN;AACF;AACA;AACA;AACE,OAAO,EACP,OAAO,qBACP,OAAO,EACP,OAAO;AAEP;AACF;AACA;AACA;;AAEE,OAAO,EACP,OAAO,gCACP,QAAQ,EACR,QAAQ,+BACT;AACD;;AAEA,MAAMC,qBAAqB,GAAGR,SAAS,IACrCT,OAAO,CAACS,SAAS,EAAEG,0BAA0B,CAAC,IAC9CZ,OAAO,CAACS,SAAS,EAAEO,qBAAqB,CAAC,IACzChB,OAAO,CAACS,SAAS,EAAEK,6BAA6B,CAAC,IACjDd,OAAO,CAACS,SAAS,EAAEM,wBAAwB,CAAC;;AAE9C;AACA;AACA;AACA;AACA;AACA,MAAMG,kBAAkB,GAAG,CACzB,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACP;AACD;;AAEA,MAAMC,kBAAkB,GAAGV,SAAS,IAAIT,OAAO,CAACS,SAAS,EAAES,kBAAkB,CAAC;;AAE9E;AACA;AACA;AACA;AACA;AACA,MAAME,eAAe,GAAG,CACtB,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,CACT;AACD;;AAEA,MAAMC,gBAAgB,GAAGZ,SAAS,IAAIT,OAAO,CAACS,SAAS,EAAEW,eAAe,CAAC;;ACr3DzE;;AAEA;AACA;AACA;AACA;AACA,MAAME,aAAa,GAAGT,wBAAwB;;AAE9C;AACA;AACA;AACA;AACA,MAAMU,eAAe,GAAGZ,yBAAyB;;AAEjD;AACA,MAAMa,YAAY,GAAGf,SAAS,IAAIA,SAAS,CAACgB,WAAW,CAAC,CAAC,CAAC;AAC1D,MAAMhN,KAAK,GAAGiN,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;AACvB,MAAMhN,IAAI,GAAGgN,CAAC,IAAIA,CAAC,CAACA,CAAC,CAAClN,MAAM,GAAG,CAAC,CAAC;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmN,YAAYA,CAACC,KAAK,EAAE;EAC3B,MAAMC,UAAU,GAAG,EAAE;EACrB,MAAMxE,IAAI,GAAGuE,KAAK,CAACpN,MAAM;EAEzB,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuG,IAAI,EAAEvG,CAAC,IAAI,CAAC,EAAE;IAChC,MAAMgL,MAAM,GAAGF,KAAK,CAACxK,UAAU,CAACN,CAAC,CAAC;IAElC,IAAIgL,MAAM,IAAI,MAAM,IAAIA,MAAM,IAAI,MAAM,IAAIzE,IAAI,GAAGvG,CAAC,GAAG,CAAC,EAAE;MACxD,MAAMiL,IAAI,GAAGH,KAAK,CAACxK,UAAU,CAACN,CAAC,GAAG,CAAC,CAAC;MAEpC,IAAIiL,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,EAAE;QACpCF,UAAU,CAAClN,IAAI,CAAC,CAACmN,MAAM,GAAG,MAAM,IAAI,KAAK,GAAGC,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC;QACpEjL,CAAC,IAAI,CAAC;QACN;;;IAIJ+K,UAAU,CAAClN,IAAI,CAACmN,MAAM,CAAC;;EAGzB,OAAOD,UAAU;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACJ,KAAK,EAAEK,IAAI,GAAG,EAAE,EAAE;EAClC,IAAI,OAAOL,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAM,IAAIM,SAAS,CAAC,kBAAkB,CAAC;;EAGzC,IAAIN,KAAK,CAACpN,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,EAAE;;;;EAIX,MAAM2N,YAAY,GAAGR,YAAY,CAACC,KAAK;;GAEpC7L,GAAG,CAAC0K,SAAS,IAAKa,aAAa,CAACb,SAAS,CAAC,GAAG,IAAI,GAAGA,SAAU;;GAE9D2B,MAAM,CAAC3B,SAAS,IAAI,CAACc,eAAe,CAACd,SAAS,CAAC,CAAC;;;EAGnD,MAAM4B,gBAAgB,GAAGpL,MAAM,CAACqL,aAAa,CAC1CC,KAAK,CAAC,IAAI,EAAEJ,YAAY,CAAC,CACzBK,SAAS,CAAC,MAAM,CAAC;EAEpB,MAAMC,cAAc,GAAGd,YAAY,CAACU,gBAAgB,CAAC;;;EAGrD,MAAMK,aAAa,GAAGD,cAAc,CAACE,IAAI,CAAC1B,qBAAqB,CAAC;EAEhE,IAAIyB,aAAa,EAAE;IACjB,MAAM,IAAIpP,KAAK,CACb,2EACF,CAAC;;;;EAIH,IAAI2O,IAAI,CAACW,eAAe,KAAK,IAAI,EAAE;IACjC,MAAMC,aAAa,GAAGJ,cAAc,CAACE,IAAI,CAACnC,qBAAqB,CAAC;IAEhE,IAAIqC,aAAa,EAAE;MACjB,MAAM,IAAIvP,KAAK,CACb,4EACF,CAAC;;;;;;EAML,MAAMwP,UAAU,GAAGL,cAAc,CAACE,IAAI,CAACxB,kBAAkB,CAAC;EAE1D,MAAM4B,QAAQ,GAAGN,cAAc,CAACE,IAAI,CAACtB,gBAAgB,CAAC;;;;EAItD,IAAIyB,UAAU,IAAIC,QAAQ,EAAE;IAC1B,MAAM,IAAIzP,KAAK,CACb,8DAA8D,GAC5D,oDACJ,CAAC;;;;AAIL;AACA;AACA;AACA;;EAEE,MAAM0P,cAAc,GAAG7B,kBAAkB,CACvCK,YAAY,CAAC/M,KAAK,CAAC4N,gBAAgB,CAAC,CACtC,CAAC;EACD,MAAMY,aAAa,GAAG9B,kBAAkB,CACtCK,YAAY,CAAC9M,IAAI,CAAC2N,gBAAgB,CAAC,CACrC,CAAC;EAED,IAAIS,UAAU,IAAI,EAAEE,cAAc,IAAIC,aAAa,CAAC,EAAE;IACpD,MAAM,IAAI3P,KAAK,CACb,kEAAkE,GAChE,6EACJ,CAAC;;EAGH,OAAO+O,gBAAgB;AACzB;;AChJA;AACA;AACA;AACA;AAKA,MAAMa,WAAW,CAAC;EAChB,OAAOC,cAAcA,CAACC,IAAI,GAAG,EAAE,EAAE;IAC/B,IAAIC,OAAO,GAAI,GAAED,IAAI,CAACE,YAAY,CAACC,OAAO,EAAG,IAAG;IAEhD,KAAK,IAAI1P,GAAG,IAAIuP,IAAI,EAAE;;MAEpB,IAAI,CAACA,IAAI,CAACI,cAAc,CAAC3P,GAAG,CAAC,EAAE;QAC7B;;MAEFwP,OAAO,IAAK,GAAExP,GAAI,KAAIuP,IAAI,CAACvP,GAAG,CAAC,CAAC2D,OAAO,EAAG,IAAG;;IAG/C,OAAOiM,iBAAiB,CAACC,QAAQ,CAACC,GAAG,CAACN,OAAO,CAAC,CAAC;;EAGjD,OAAOO,uBAAuBA,CAACC,KAAK,EAAE;IACpC,OAAOH,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACC,MAAM,CAACH,KAAK,CAAC;;EAG7C,OAAOI,MAAMA,CAACrL,QAAQ,EAAEnF,OAAO,GAAG,EAAE,EAAE;IACpC,IAAI,CAACA,OAAO,CAACyQ,aAAa,IAAI,CAACzQ,OAAO,CAAC0Q,YAAY,EAAE;MACnD,OAAO,IAAI;;IAEb,OAAO,IAAIjB,WAAW,CAACtK,QAAQ,EAAEnF,OAAO,CAAC;;EAG3CD,WAAWA,CAACoF,QAAQ,EAAEnF,OAAO,GAAG,EAAE,EAAE;IAClC,IAAI,CAACA,OAAO,CAACyQ,aAAa,IAAI,CAACzQ,OAAO,CAAC0Q,YAAY,EAAE;MACnD,MAAM,IAAI7Q,KAAK,CAAC,sDAAsD,CAAC;;IAGzE,IAAI,CAACsF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACwL,gBAAgB,CAAC3Q,OAAO,CAAC;;EAGhC2Q,gBAAgBA,CAAC3Q,OAAO,EAAE;IACxB,QAAQA,OAAO,CAAC4Q,UAAU;MACxB,KAAK,KAAK;MACV,KAAK,KAAK;QACR,IAAI,CAACC,OAAO,GAAG,CAAC;QAChB;MACF,KAAK,KAAK;MACV,KAAK,KAAK;QACR,IAAI,CAACA,OAAO,GAAG,CAAC;QAChB;MACF,KAAK,SAAS;QACZ,IAAI,CAACA,OAAO,GAAG,CAAC;QAChB;MACF;QACE,IAAI,CAACA,OAAO,GAAG,CAAC;QAChB;;IAGJ,MAAMC,OAAO,GAAG;MACdvL,MAAM,EAAE;KACT;IAED,QAAQ,IAAI,CAACsL,OAAO;MAClB,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,IAAI,CAACE,sBAAsB,CAAC,IAAI,CAACF,OAAO,EAAEC,OAAO,EAAE9Q,OAAO,CAAC;QAC3D;MACF,KAAK,CAAC;QACJ,IAAI,CAACgR,kBAAkB,CAACF,OAAO,EAAE9Q,OAAO,CAAC;QACzC;;IAGJ,IAAI,CAACuK,UAAU,GAAG,IAAI,CAACpF,QAAQ,CAACjD,GAAG,CAAC4O,OAAO,CAAC;;EAG9CC,sBAAsBA,CAACE,CAAC,EAAEH,OAAO,EAAE9Q,OAAO,EAAE;IAC1C,IAAIkR,CAAC,EAAEC,WAAW;IAClB,QAAQF,CAAC;MACP,KAAK,CAAC;QACJC,CAAC,GAAG,CAAC;QACL,IAAI,CAACE,OAAO,GAAG,EAAE;QACjBD,WAAW,GAAGE,gBAAgB,CAACrR,OAAO,CAACmR,WAAW,CAAC;QACnD;MACF,KAAK,CAAC;QACJD,CAAC,GAAG,CAAC;QACL,IAAI,CAACE,OAAO,GAAG,GAAG;QAClBD,WAAW,GAAGG,gBAAgB,CAACtR,OAAO,CAACmR,WAAW,CAAC;QACnD;MACF,KAAK,CAAC;QACJD,CAAC,GAAG,CAAC;QACL,IAAI,CAACE,OAAO,GAAG,GAAG;QAClBD,WAAW,GAAGG,gBAAgB,CAACtR,OAAO,CAACmR,WAAW,CAAC;QACnD;;IAGJ,MAAMI,kBAAkB,GAAGC,qBAAqB,CAACxR,OAAO,CAAC0Q,YAAY,CAAC;IACtE,MAAMe,mBAAmB,GAAGzR,OAAO,CAACyQ,aAAa,GAC7Ce,qBAAqB,CAACxR,OAAO,CAACyQ,aAAa,CAAC,GAC5Cc,kBAAkB;IAEtB,MAAMG,kBAAkB,GAAGC,sBAAsB,CAC/CT,CAAC,EACD,IAAI,CAACE,OAAO,EACZG,kBAAkB,EAClBE,mBACF,CAAC;IACD,IAAI,CAACG,aAAa,GAAGC,sBAAsB,CACzCX,CAAC,EACD,IAAI,CAACE,OAAO,EACZ,IAAI,CAACjM,QAAQ,CAAC2M,GAAG,EACjBP,kBAAkB,EAClBG,kBAAkB,EAClBP,WACF,CAAC;IACD,IAAIY,iBAAiB;IACrB,IAAIb,CAAC,KAAK,CAAC,EAAE;MACXa,iBAAiB,GAAGC,iBAAiB,CAAC,IAAI,CAACJ,aAAa,CAAC;KAC1D,MAAM;MACLG,iBAAiB,GAAGE,mBAAmB,CACrC,IAAI,CAAC9M,QAAQ,CAAC2M,GAAG,EACjB,IAAI,CAACF,aACP,CAAC;;IAGHd,OAAO,CAACoB,CAAC,GAAGjB,CAAC;IACb,IAAIA,CAAC,IAAI,CAAC,EAAE;MACVH,OAAO,CAAClL,MAAM,GAAG,IAAI,CAACwL,OAAO;;IAE/B,IAAIH,CAAC,KAAK,CAAC,EAAE;MACXH,OAAO,CAACqB,EAAE,GAAG;QACXC,KAAK,EAAE;UACLC,SAAS,EAAE,SAAS;UACpBC,GAAG,EAAE,OAAO;UACZ1M,MAAM,EAAE,IAAI,CAACwL,OAAO,GAAG;;OAE1B;MACDN,OAAO,CAACyB,IAAI,GAAG,OAAO;MACtBzB,OAAO,CAAC0B,IAAI,GAAG,OAAO;;IAExB1B,OAAO,CAAC2B,CAAC,GAAGvB,CAAC;IACbJ,OAAO,CAAC4B,CAAC,GAAG1C,iBAAiB,CAAC0B,kBAAkB,CAAC;IACjDZ,OAAO,CAAC6B,CAAC,GAAG3C,iBAAiB,CAAC+B,iBAAiB,CAAC;IAChDjB,OAAO,CAAC8B,CAAC,GAAGzB,WAAW;;EAGzBH,kBAAkBA,CAACF,OAAO,EAAE9Q,OAAO,EAAE;IACnC,IAAI,CAACoR,OAAO,GAAG,GAAG;IAClB,MAAMD,WAAW,GAAGG,gBAAgB,CAACtR,OAAO,CAACmR,WAAW,CAAC;IAEzD,MAAM0B,qBAAqB,GAAGC,iBAAiB,CAAC9S,OAAO,CAAC0Q,YAAY,CAAC;IACrE,MAAMqC,sBAAsB,GAAG/S,OAAO,CAACyQ,aAAa,GAChDqC,iBAAiB,CAAC9S,OAAO,CAACyQ,aAAa,CAAC,GACxCoC,qBAAqB;IAEzB,IAAI,CAACjB,aAAa,GAAGoB,kBAAkB,CACrCvD,WAAW,CAACU,uBACd,CAAC;IACD,MAAM4B,iBAAiB,GAAGkB,iBAAiB,CACzCJ,qBAAqB,EACrBpD,WAAW,CAACU,uBACd,CAAC;IACD,MAAM+C,WAAW,GAAGjD,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAC/CuB,iBAAiB,CAACoB,KAAK,CAACpQ,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EACrC,CACF,CAAC;IACD,MAAMqQ,sBAAsB,GAAGC,sBAAsB,CACnDR,qBAAqB,EACrBK,WAAW,EACX,IAAI,CAACtB,aACP,CAAC;IACD,MAAMF,kBAAkB,GAAG4B,kBAAkB,CAC3CP,sBAAsB,EACtBhB,iBAAiB,EACjBtC,WAAW,CAACU,uBACd,CAAC;IACD,MAAMoD,YAAY,GAAGtD,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAChDkB,kBAAkB,CAACyB,KAAK,CAACpQ,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EACtC,CACF,CAAC;IACD,MAAMyQ,uBAAuB,GAAGC,uBAAuB,CACrDV,sBAAsB,EACtBQ,YAAY,EACZxB,iBAAiB,EACjB,IAAI,CAACH,aACP,CAAC;IACD,MAAM8B,UAAU,GAAGC,yBAAyB,CAC1CxC,WAAW,EACX,IAAI,CAACS,aAAa,EAClBnC,WAAW,CAACU,uBACd,CAAC;IAEDW,OAAO,CAACoB,CAAC,GAAG,CAAC;IACbpB,OAAO,CAAClL,MAAM,GAAG,IAAI,CAACwL,OAAO;IAC7BN,OAAO,CAACqB,EAAE,GAAG;MACXC,KAAK,EAAE;QACLC,SAAS,EAAE,SAAS;QACpBC,GAAG,EAAE,OAAO;QACZ1M,MAAM,EAAE,IAAI,CAACwL,OAAO,GAAG;;KAE1B;IACDN,OAAO,CAACyB,IAAI,GAAG,OAAO;IACtBzB,OAAO,CAAC0B,IAAI,GAAG,OAAO;IACtB1B,OAAO,CAAC2B,CAAC,GAAG,CAAC;IACb3B,OAAO,CAAC4B,CAAC,GAAG1C,iBAAiB,CAAC0B,kBAAkB,CAAC;IACjDZ,OAAO,CAAC8C,EAAE,GAAG5D,iBAAiB,CAACwD,uBAAuB,CAAC;IACvD1C,OAAO,CAAC6B,CAAC,GAAG3C,iBAAiB,CAAC+B,iBAAiB,CAAC;IAChDjB,OAAO,CAAC+C,EAAE,GAAG7D,iBAAiB,CAACoD,sBAAsB,CAAC;IACtDtC,OAAO,CAAC8B,CAAC,GAAGzB,WAAW;IACvBL,OAAO,CAACgD,KAAK,GAAG9D,iBAAiB,CAAC0D,UAAU,CAAC;;EAG/CzN,YAAYA,CAAC8N,GAAG,EAAE1O,GAAG,EAAE;IACrB,IAAI2O,MAAM;IACV,IAAI,IAAI,CAACnD,OAAO,GAAG,CAAC,EAAE;MACpBmD,MAAM,GAAG,IAAI,CAACpC,aAAa,CACxBqC,KAAK,EAAE,CACP/N,MAAM,CACL+J,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAC3B,CACG,CAACuD,GAAG,GAAG,IAAI,KAAK,EAAE,GAChB,CAACA,GAAG,GAAG,MAAM,KAAK,CAAE,GACnBA,GAAG,IAAI,CAAC,GAAI,MAAO,GACpB1O,GAAG,GAAG,IAAK,EACd,CAACA,GAAG,GAAG,MAAM,KAAK,EAAE,CACrB,EACD,CACF,CACF,CAAC;;IAGL,IAAI,IAAI,CAACwL,OAAO,KAAK,CAAC,IAAI,IAAI,CAACA,OAAO,KAAK,CAAC,EAAE;MAC5C,IAAIzQ,GAAG,GAAG6P,QAAQ,CAACC,GAAG,CAAC8D,MAAM,CAAC;MAC9B5T,GAAG,CAAC8T,QAAQ,GAAGlP,IAAI,CAACmP,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC/C,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;MACjD,OAAO3L,MAAM,IACXuK,iBAAiB,CACfC,QAAQ,CAACmE,GAAG,CAACC,OAAO,CAACpE,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAAC/K,MAAM,CAAC,EAAErF,GAAG,CAAC,CAC7DkU,UACL,CAAC;;IAGL,IAAIlU,GAAG;IACP,IAAI,IAAI,CAACyQ,OAAO,KAAK,CAAC,EAAE;MACtBzQ,GAAG,GAAG6P,QAAQ,CAACC,GAAG,CAChB8D,MAAM,CAAC9N,MAAM,CAAC+J,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAC9D,CAAC;KACF,MAAM;MACLpQ,GAAG,GAAG,IAAI,CAACwR,aAAa;;IAG1B,MAAM2C,EAAE,GAAG9E,WAAW,CAACU,uBAAuB,CAAC,EAAE,CAAC;IAClD,MAAMnQ,OAAO,GAAG;MACdwU,IAAI,EAAEvE,QAAQ,CAACuE,IAAI,CAACC,GAAG;MACvBC,OAAO,EAAEzE,QAAQ,CAACrN,GAAG,CAAC+R,KAAK;MAC3BJ;KACD;IAED,OAAO9O,MAAM,IACXuK,iBAAiB,CACfuE,EAAE,CACCN,KAAK,EAAE,CACP/N,MAAM,CACL+J,QAAQ,CAAC2E,GAAG,CAACP,OAAO,CAClBpE,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAAC/K,MAAM,CAAC,EACrCrF,GAAG,EACHJ,OACF,CAAC,CAACsU,UACJ,CACJ,CAAC;;EAGL3R,GAAGA,GAAG;IACJ,IAAI,CAAC4H,UAAU,CAAC5H,GAAG,EAAE;;AAEzB;AAEA,SAAS0O,gBAAgBA,CAACwD,gBAAgB,GAAG,EAAE,EAAE;EAC/C,IAAI1D,WAAW,GAAG,UAAU,IAAI,CAAC;EACjC,IAAI0D,gBAAgB,CAACC,QAAQ,EAAE;IAC7B3D,WAAW,IAAI,cAAc;;EAE/B,IAAI0D,gBAAgB,CAACE,SAAS,EAAE;IAC9B5D,WAAW,IAAI,cAAc;;EAE/B,IAAI0D,gBAAgB,CAACG,OAAO,EAAE;IAC5B7D,WAAW,IAAI,cAAc;;EAE/B,IAAI0D,gBAAgB,CAACI,UAAU,EAAE;IAC/B9D,WAAW,IAAI,cAAc;;EAE/B,OAAOA,WAAW;AACpB;AAEA,SAASG,gBAAgBA,CAACuD,gBAAgB,GAAG,EAAE,EAAE;EAC/C,IAAI1D,WAAW,GAAG,UAAU,IAAI,CAAC;EACjC,IAAI0D,gBAAgB,CAACC,QAAQ,KAAK,eAAe,EAAE;IACjD3D,WAAW,IAAI,cAAc;;EAE/B,IAAI0D,gBAAgB,CAACC,QAAQ,KAAK,gBAAgB,EAAE;IAClD3D,WAAW,IAAI,cAAc;;EAE/B,IAAI0D,gBAAgB,CAACE,SAAS,EAAE;IAC9B5D,WAAW,IAAI,cAAc;;EAE/B,IAAI0D,gBAAgB,CAACG,OAAO,EAAE;IAC5B7D,WAAW,IAAI,cAAc;;EAE/B,IAAI0D,gBAAgB,CAACI,UAAU,EAAE;IAC/B9D,WAAW,IAAI,cAAc;;EAE/B,IAAI0D,gBAAgB,CAACK,YAAY,EAAE;IACjC/D,WAAW,IAAI,cAAc;;EAE/B,IAAI0D,gBAAgB,CAACM,oBAAoB,EAAE;IACzChE,WAAW,IAAI,cAAc;;EAE/B,IAAI0D,gBAAgB,CAACO,gBAAgB,EAAE;IACrCjE,WAAW,IAAI,cAAc;;EAE/B,OAAOA,WAAW;AACpB;AAEA,SAASa,iBAAiBA,CAACJ,aAAa,EAAE;EACxC,OAAO3B,QAAQ,CAACmE,GAAG,CAACC,OAAO,CAAC7C,qBAAqB,EAAE,EAAEI,aAAa,CAAC,CAChE0C,UAAU;AACf;AAEA,SAASrC,mBAAmBA,CAACoD,UAAU,EAAEzD,aAAa,EAAE;EACtD,MAAMxR,GAAG,GAAGwR,aAAa,CAACqC,KAAK,EAAE;EACjC,IAAIqB,MAAM,GAAGrF,QAAQ,CAACC,GAAG,CACvBsB,qBAAqB,EAAE,CAACtL,MAAM,CAAC+J,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAAC6E,UAAU,CAAC,CAC1E,CAAC;EACD,KAAK,IAAIhS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC3B,MAAMkS,QAAQ,GAAGvQ,IAAI,CAACwQ,IAAI,CAACpV,GAAG,CAAC8T,QAAQ,GAAG,CAAC,CAAC;IAC5C,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,EAAEE,CAAC,EAAE,EAAE;MACjCrV,GAAG,CAAC+S,KAAK,CAACsC,CAAC,CAAC,GACV7D,aAAa,CAACuB,KAAK,CAACsC,CAAC,CAAC,IAAIpS,CAAC,GAAIA,CAAC,IAAI,CAAE,GAAIA,CAAC,IAAI,EAAG,GAAIA,CAAC,IAAI,EAAG,CAAC;;IAEnEiS,MAAM,GAAGrF,QAAQ,CAACmE,GAAG,CAACC,OAAO,CAACiB,MAAM,EAAElV,GAAG,CAAC,CAACkU,UAAU;;EAEvD,OAAOgB,MAAM,CAACpP,MAAM,CAAC+J,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAC/D;AAEA,SAASmB,sBAAsBA,CAC7BT,CAAC,EACDE,OAAO,EACPG,kBAAkB,EAClBE,mBAAmB,EACnB;EACA,IAAIuC,MAAM,GAAGvC,mBAAmB;EAChC,IAAIxM,KAAK,GAAGiM,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;EAC3B,KAAK,IAAI7N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,KAAK,EAAE5B,CAAC,EAAE,EAAE;IAC9B2Q,MAAM,GAAG/D,QAAQ,CAACC,GAAG,CAAC8D,MAAM,CAAC;;EAG/B,MAAM5T,GAAG,GAAG4T,MAAM,CAACC,KAAK,EAAE;EAC1B7T,GAAG,CAAC8T,QAAQ,GAAG9C,OAAO,GAAG,CAAC;EAC1B,IAAIkE,MAAM,GAAG/D,kBAAkB;EAC/BtM,KAAK,GAAGiM,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;EACvB,KAAK,IAAI7N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,KAAK,EAAE5B,CAAC,EAAE,EAAE;IAC9B,MAAMkS,QAAQ,GAAGvQ,IAAI,CAACwQ,IAAI,CAACpV,GAAG,CAAC8T,QAAQ,GAAG,CAAC,CAAC;IAC5C,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,EAAEE,CAAC,EAAE,EAAE;MACjCrV,GAAG,CAAC+S,KAAK,CAACsC,CAAC,CAAC,GAAGzB,MAAM,CAACb,KAAK,CAACsC,CAAC,CAAC,IAAIpS,CAAC,GAAIA,CAAC,IAAI,CAAE,GAAIA,CAAC,IAAI,EAAG,GAAIA,CAAC,IAAI,EAAG,CAAC;;IAEzEiS,MAAM,GAAGrF,QAAQ,CAACmE,GAAG,CAACC,OAAO,CAACiB,MAAM,EAAElV,GAAG,CAAC,CAACkU,UAAU;;EAEvD,OAAOgB,MAAM;AACf;AAEA,SAASzD,sBAAsBA,CAC7BX,CAAC,EACDE,OAAO,EACPiE,UAAU,EACV9D,kBAAkB,EAClBG,kBAAkB,EAClBP,WAAW,EACX;EACA,IAAI/Q,GAAG,GAAGmR,kBAAkB,CACzB0C,KAAK,EAAE,CACP/N,MAAM,CAACwL,kBAAkB,CAAC,CAC1BxL,MAAM,CAAC+J,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAAC,CAACkF,YAAY,CAACvE,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CACrEjL,MAAM,CAAC+J,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAAC6E,UAAU,CAAC,CAAC;EACpD,MAAMpQ,KAAK,GAAGiM,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7B,KAAK,IAAI7N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,KAAK,EAAE5B,CAAC,EAAE,EAAE;IAC9BjD,GAAG,GAAG6P,QAAQ,CAACC,GAAG,CAAC9P,GAAG,CAAC;IACvBA,GAAG,CAAC8T,QAAQ,GAAG9C,OAAO,GAAG,CAAC;;EAE5B,OAAOhR,GAAG;AACZ;AAEA,SAAS6S,iBAAiBA,CAACJ,qBAAqB,EAAE1C,uBAAuB,EAAE;EACzE,MAAMwF,cAAc,GAAGxF,uBAAuB,CAAC,CAAC,CAAC;EACjD,MAAMyF,OAAO,GAAGzF,uBAAuB,CAAC,CAAC,CAAC;EAC1C,OAAOF,QAAQ,CAAC4F,MAAM,CAAChD,qBAAqB,CAACoB,KAAK,EAAE,CAAC/N,MAAM,CAACyP,cAAc,CAAC,CAAC,CACzEzP,MAAM,CAACyP,cAAc,CAAC,CACtBzP,MAAM,CAAC0P,OAAO,CAAC;AACpB;AAEA,SAASvC,sBAAsBA,CAC7BR,qBAAqB,EACrBK,WAAW,EACXtB,aAAa,EACb;EACA,MAAMxR,GAAG,GAAG6P,QAAQ,CAAC4F,MAAM,CACzBhD,qBAAqB,CAACoB,KAAK,EAAE,CAAC/N,MAAM,CAACgN,WAAW,CAClD,CAAC;EACD,MAAMlT,OAAO,GAAG;IACdwU,IAAI,EAAEvE,QAAQ,CAACuE,IAAI,CAACC,GAAG;IACvBC,OAAO,EAAEzE,QAAQ,CAACrN,GAAG,CAACkT,SAAS;IAC/BvB,EAAE,EAAEtE,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAAC,IAAI,EAAE,EAAE;GAC3C;EACD,OAAOP,QAAQ,CAAC2E,GAAG,CAACP,OAAO,CAACzC,aAAa,EAAExR,GAAG,EAAEJ,OAAO,CAAC,CAACsU,UAAU;AACrE;AAEA,SAAShB,kBAAkBA,CACzBP,sBAAsB,EACtBhB,iBAAiB,EACjB5B,uBAAuB,EACvB;EACA,MAAMwF,cAAc,GAAGxF,uBAAuB,CAAC,CAAC,CAAC;EACjD,MAAMyF,OAAO,GAAGzF,uBAAuB,CAAC,CAAC,CAAC;EAC1C,OAAOF,QAAQ,CAAC4F,MAAM,CACpB9C,sBAAsB,CACnBkB,KAAK,EAAE,CACP/N,MAAM,CAACyP,cAAc,CAAC,CACtBzP,MAAM,CAAC6L,iBAAiB,CAC7B,CAAC,CACE7L,MAAM,CAACyP,cAAc,CAAC,CACtBzP,MAAM,CAAC0P,OAAO,CAAC;AACpB;AAEA,SAASnC,uBAAuBA,CAC9BV,sBAAsB,EACtBQ,YAAY,EACZxB,iBAAiB,EACjBH,aAAa,EACb;EACA,MAAMxR,GAAG,GAAG6P,QAAQ,CAAC4F,MAAM,CACzB9C,sBAAsB,CACnBkB,KAAK,EAAE,CACP/N,MAAM,CAACqN,YAAY,CAAC,CACpBrN,MAAM,CAAC6L,iBAAiB,CAC7B,CAAC;EACD,MAAM/R,OAAO,GAAG;IACdwU,IAAI,EAAEvE,QAAQ,CAACuE,IAAI,CAACC,GAAG;IACvBC,OAAO,EAAEzE,QAAQ,CAACrN,GAAG,CAACkT,SAAS;IAC/BvB,EAAE,EAAEtE,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAAC,IAAI,EAAE,EAAE;GAC3C;EACD,OAAOP,QAAQ,CAAC2E,GAAG,CAACP,OAAO,CAACzC,aAAa,EAAExR,GAAG,EAAEJ,OAAO,CAAC,CAACsU,UAAU;AACrE;AAEA,SAAStB,kBAAkBA,CAAC7C,uBAAuB,EAAE;EACnD,OAAOA,uBAAuB,CAAC,EAAE,CAAC;AACpC;AAEA,SAASwD,yBAAyBA,CAChCxC,WAAW,EACXS,aAAa,EACbzB,uBAAuB,EACvB;EACA,MAAMmF,MAAM,GAAGrF,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAC1C,CAACkF,YAAY,CAACvE,WAAW,CAAC,EAAE,UAAU,EAAE,UAAU,CAAC,EACnD,EACF,CAAC,CAACjL,MAAM,CAACiK,uBAAuB,CAAC,CAAC,CAAC,CAAC;EACpC,MAAMnQ,OAAO,GAAG;IACdwU,IAAI,EAAEvE,QAAQ,CAACuE,IAAI,CAACuB,GAAG;IACvBrB,OAAO,EAAEzE,QAAQ,CAACrN,GAAG,CAACkT;GACvB;EACD,OAAO7F,QAAQ,CAAC2E,GAAG,CAACP,OAAO,CAACiB,MAAM,EAAE1D,aAAa,EAAE5R,OAAO,CAAC,CAACsU,UAAU;AACxE;AAEA,SAAS9C,qBAAqBA,CAACwE,QAAQ,GAAG,EAAE,EAAE;EAC5C,MAAMlV,GAAG,GAAG+C,MAAM,CAACoS,KAAK,CAAC,EAAE,CAAC;EAC5B,MAAMlV,MAAM,GAAGiV,QAAQ,CAACjV,MAAM;EAC9B,IAAImV,KAAK,GAAG,CAAC;EACb,OAAOA,KAAK,GAAGnV,MAAM,IAAImV,KAAK,GAAG,EAAE,EAAE;IACnC,MAAMC,IAAI,GAAGH,QAAQ,CAACrS,UAAU,CAACuS,KAAK,CAAC;IACvC,IAAIC,IAAI,GAAG,IAAI,EAAE;MACf,MAAM,IAAItW,KAAK,CAAC,mDAAmD,CAAC;;IAEtEiB,GAAG,CAACoV,KAAK,CAAC,GAAGC,IAAI;IACjBD,KAAK,EAAE;;EAET,OAAOA,KAAK,GAAG,EAAE,EAAE;IACjBpV,GAAG,CAACoV,KAAK,CAAC,GAAGE,gBAAgB,CAACF,KAAK,GAAGnV,MAAM,CAAC;IAC7CmV,KAAK,EAAE;;EAET,OAAOjG,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAAC1P,GAAG,CAAC;AAC3C;AAEA,SAASgS,iBAAiBA,CAACkD,QAAQ,GAAG,EAAE,EAAE;EACxCA,QAAQ,GAAGK,QAAQ,CAACC,kBAAkB,CAAC/H,QAAQ,CAACyH,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAMjV,MAAM,GAAGiE,IAAI,CAACmP,GAAG,CAAC,GAAG,EAAE6B,QAAQ,CAACjV,MAAM,CAAC;EAC7C,MAAMD,GAAG,GAAG+C,MAAM,CAACoS,KAAK,CAAClV,MAAM,CAAC;EAEhC,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtC,MAAM,EAAEsC,CAAC,EAAE,EAAE;IAC/BvC,GAAG,CAACuC,CAAC,CAAC,GAAG2S,QAAQ,CAACrS,UAAU,CAACN,CAAC,CAAC;;EAGjC,OAAO4M,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAAC1P,GAAG,CAAC;AAC3C;AAEA,SAAS4U,YAAYA,CAACtQ,IAAI,EAAE;EAC1B,OACG,CAACA,IAAI,GAAG,IAAI,KAAK,EAAE,GACnB,CAACA,IAAI,GAAG,MAAM,KAAK,CAAE,GACpBA,IAAI,IAAI,CAAC,GAAI,MAAO,GACpBA,IAAI,IAAI,EAAE,GAAI,IAAK;AAEzB;AAEA,SAAS4K,iBAAiBA,CAACuG,SAAS,EAAE;EACpC,MAAMC,SAAS,GAAG,EAAE;EACpB,KAAK,IAAInT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkT,SAAS,CAACrC,QAAQ,EAAE7Q,CAAC,EAAE,EAAE;IAC3CmT,SAAS,CAACtV,IAAI,CACXqV,SAAS,CAACpD,KAAK,CAACnO,IAAI,CAAC4H,KAAK,CAACvJ,CAAC,GAAG,CAAC,CAAC,CAAC,IAAK,CAAC,IAAI,CAAC,GAAIA,CAAC,GAAG,CAAE,CAAE,GAAI,IAChE,CAAC;;EAEH,OAAOQ,MAAM,CAACC,IAAI,CAAC0S,SAAS,CAAC;AAC/B;AAEA,MAAMJ,gBAAgB,GAAG,CACvB,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL;;AC3iBD,MAAM;EAAEtR;AAAO,CAAC,GAAG3D,SAAS;AAE5B,MAAMsV,WAAW,CAAC;EAChB1W,WAAWA,CAAC0B,GAAG,EAAE;IACf,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACiV,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;EAGrCC,IAAIA,CAACC,GAAG,EAAE3K,KAAK,EAAE4K,OAAO,EAAE;IACxB,IAAIA,OAAO,IAAI,IAAI,EAAE;MACnBA,OAAO,GAAG,CAAC;;IAEb5K,KAAK,GAAG,IAAI,CAAC1K,GAAG,CAACuV,eAAe,CAAC7K,KAAK,CAAC;IAEvC,IAAI,IAAI,CAACuK,KAAK,CAAC3V,MAAM,KAAK,CAAC,EAAE;MAC3B,IAAIoL,KAAK,CAACpL,MAAM,KAAK,CAAC,EAAE;QACtB,IAAI,CAACkW,WAAW,GAAG,WAAW;OAC/B,MAAM,IAAI9K,KAAK,CAACpL,MAAM,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACkW,WAAW,GAAG,YAAY;OAChC,MAAM,IAAI9K,KAAK,CAACpL,MAAM,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACkW,WAAW,GAAG,YAAY;OAChC,MAAM;QACL,MAAM,IAAIpX,KAAK,CAAC,qBAAqB,CAAC;;KAEzC,MAAM,IACJ,IAAI,CAACoX,WAAW,KAAK,WAAW,IAAI9K,KAAK,CAACpL,MAAM,KAAK,CAAC,IACtD,IAAI,CAACkW,WAAW,KAAK,YAAY,IAAI9K,KAAK,CAACpL,MAAM,KAAK,CAAE,IACxD,IAAI,CAACkW,WAAW,KAAK,YAAY,IAAI9K,KAAK,CAACpL,MAAM,KAAK,CAAE,EACzD;MACA,MAAM,IAAIlB,KAAK,CAAC,kDAAkD,CAAC;;IAGrEkX,OAAO,GAAG/R,IAAI,CAACkS,GAAG,CAAC,CAAC,EAAElS,IAAI,CAACmP,GAAG,CAAC,CAAC,EAAE4C,OAAO,CAAC,CAAC;IAC3C,IAAI,CAACL,KAAK,CAACxV,IAAI,CAAC,CAAC4V,GAAG,EAAE3K,KAAK,EAAE4K,OAAO,CAAC,CAAC;IACtC,OAAO,IAAI;;EAGbI,YAAYA,CAACC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAE;IACvC,IAAI,CAACb,SAAS,GAAG,CAACQ,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC7C,OAAO,IAAI;;EAGbC,KAAKA,CAACC,CAAC,EAAE;IACP,IAAIC,EAAE;IACN,MAAMC,WAAW,GAAG,IAAI,CAACnB,KAAK,CAAC3V,MAAM;IACrC,IAAI8W,WAAW,KAAK,CAAC,EAAE;MACrB;;IAEF,IAAI,CAAClB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACmB,MAAM,GAAGH,CAAC;;;IAGf,MAAM1W,IAAI,GAAG,IAAI,CAACyV,KAAK,CAACmB,WAAW,GAAG,CAAC,CAAC;IACxC,IAAI5W,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACf,IAAI,CAACyV,KAAK,CAACxV,IAAI,CAAC,CAAC,CAAC,EAAED,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;IAGxC,MAAM8W,MAAM,GAAG,EAAE;IACjB,MAAMC,MAAM,GAAG,EAAE;IACjB,MAAMtB,KAAK,GAAG,EAAE;IAEhB,KAAK,IAAIrT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwU,WAAW,GAAG,CAAC,EAAExU,CAAC,EAAE,EAAE;MACxC2U,MAAM,CAAC9W,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MACjB,IAAImC,CAAC,GAAG,CAAC,KAAKwU,WAAW,EAAE;QACzBE,MAAM,CAAC7W,IAAI,CAAC,IAAI,CAACwV,KAAK,CAACrT,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAGnCuU,EAAE,GAAG,IAAI,CAACnW,GAAG,CAACS,GAAG,CAAC;QAChBM,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACdL,EAAE,EAAE,IAAI,CAACsU,KAAK,CAACrT,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACxBhB,EAAE,EAAE,IAAI,CAACqU,KAAK,CAACrT,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACxBX,CAAC,EAAE;OACJ,CAAC;MAEFgU,KAAK,CAACxV,IAAI,CAAC0W,EAAE,CAAC;MACdA,EAAE,CAACjV,GAAG,EAAE;;;;IAIV,IAAIkV,WAAW,KAAK,CAAC,EAAE;MACrBD,EAAE,GAAGlB,KAAK,CAAC,CAAC,CAAC;KACd,MAAM;MACLkB,EAAE,GAAG,IAAI,CAACnW,GAAG,CAACS,GAAG,CAAC;QAChBM,YAAY,EAAE,CAAC;;QACfC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACdwV,SAAS,EAAEvB,KAAK;QAChBwB,MAAM,EAAEH,MAAM;QACdI,MAAM,EAAEH;OACT,CAAC;MAEFJ,EAAE,CAACjV,GAAG,EAAE;;IAGV,IAAI,CAACZ,EAAE,GAAI,KAAI,EAAE,IAAI,CAACN,GAAG,CAAC2W,UAAW,EAAC;IAEtC,MAAMC,MAAM,GAAG,IAAI,CAACA,MAAM,CAACT,EAAE,CAAC;IAC9BS,MAAM,CAAC1V,GAAG,EAAE;IAEZ,MAAM2V,OAAO,GAAG,IAAI,CAAC7W,GAAG,CAACS,GAAG,CAAC;MAC3BsI,IAAI,EAAE,SAAS;MACf+N,WAAW,EAAE,CAAC;MACdC,OAAO,EAAEH,MAAM;MACfI,MAAM,EAAE,IAAI,CAACX,MAAM,CAACxV,GAAG,CAACwC,MAAM;KAC/B,CAAC;IAEFwT,OAAO,CAAC3V,GAAG,EAAE;IAEb,IAAI,IAAI,CAAC+T,KAAK,CAACxH,IAAI,CAAC2H,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;MACxC,IAAI6B,IAAI,GAAG,IAAI,CAACC,eAAe,EAAE;MACjCD,IAAI,CAACzB,WAAW,GAAG,YAAY;MAE/B,KAAK,IAAIJ,IAAI,IAAI,IAAI,CAACH,KAAK,EAAE;QAC3BgC,IAAI,CAAC7B,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;MAG/B6B,IAAI,GAAGA,IAAI,CAAChB,KAAK,CAAC,IAAI,CAACI,MAAM,CAAC;MAE9B,MAAMc,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACnX,GAAG,CAACoX,IAAI,CAAC3O,KAAK,EAAE,IAAI,CAACzI,GAAG,CAACoX,IAAI,CAAC1O,MAAM,CAAC;MAElE,MAAM2O,IAAI,GAAG,IAAI,CAACrX,GAAG,CAACS,GAAG,CAAC;QACxBsI,IAAI,EAAE,SAAS;QACfuO,OAAO,EAAE,MAAM;QACfC,QAAQ,EAAE,CAAC;QACXC,IAAI,EAAEL,QAAQ;QACdM,KAAK,EAAE;UACL1O,IAAI,EAAE,OAAO;UACb2O,CAAC,EAAE,cAAc;UACjBC,EAAE,EAAE;SACL;QACDtO,SAAS,EAAE;UACTR,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;UACtDiB,OAAO,EAAE;YACP8N,GAAG,EAAEX;;;OAGV,CAAC;MAEFI,IAAI,CAACpT,KAAK,CAAC,sBAAsB,CAAC;MAClCoT,IAAI,CAACnW,GAAG,CAAE,GAAEiW,QAAQ,CAACrX,IAAI,CAAC,GAAG,CAAE,OAAM,CAAC;MAEtC,MAAM+X,MAAM,GAAG,IAAI,CAAC7X,GAAG,CAACS,GAAG,CAAC;QAC1BsI,IAAI,EAAE,WAAW;QACjB+O,KAAK,EAAE;UACL/O,IAAI,EAAE,MAAM;UACZ2O,CAAC,EAAE,YAAY;UACfK,CAAC,EAAEV;;OAEN,CAAC;MAEFQ,MAAM,CAAC3W,GAAG,EAAE;MAEZ,MAAM8W,cAAc,GAAG,IAAI,CAAChY,GAAG,CAACS,GAAG,CAAC;QAClCsI,IAAI,EAAE,SAAS;QACf+N,WAAW,EAAE,CAAC;QACdmB,SAAS,EAAE,CAAC;QACZC,UAAU,EAAE,CAAC;QACbV,IAAI,EAAEL,QAAQ;QACdgB,KAAK,EAAEhB,QAAQ,CAAC,CAAC,CAAC;QAClBiB,KAAK,EAAEjB,QAAQ,CAAC,CAAC,CAAC;QAClB9N,SAAS,EAAE;UACTR,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;UACtDiB,OAAO,EAAE;YACP8N,GAAG,EAAEf;WACN;UACDjN,SAAS,EAAE;YACTyO,GAAG,EAAER;;;OAGV,CAAC;MAEFG,cAAc,CAAC/T,KAAK,CAAC,8BAA8B,CAAC;MACpD+T,cAAc,CAAC9W,GAAG,CAAE,GAAEiW,QAAQ,CAACrX,IAAI,CAAC,GAAG,CAAE,OAAM,CAAC;MAEhD,IAAI,CAACE,GAAG,CAACoX,IAAI,CAACvN,QAAQ,CAAC,IAAI,CAACvJ,EAAE,CAAC,GAAG0X,cAAc;KACjD,MAAM;MACL,IAAI,CAAChY,GAAG,CAACoX,IAAI,CAACvN,QAAQ,CAAC,IAAI,CAACvJ,EAAE,CAAC,GAAGuW,OAAO;;IAG3C,OAAOA,OAAO;;EAGhBxJ,KAAKA,CAACiL,MAAM,EAAE;;IAEZ,MAAM,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAI,CAAC5Y,GAAG,CAAC6Y,IAAI;IAC9C,MAAM,CAAClD,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAI,CAACb,SAAS;IACnD,MAAMe,CAAC,GAAG,CACRqC,EAAE,GAAG5C,GAAG,GAAG8C,EAAE,GAAG7C,GAAG,EACnB4C,EAAE,GAAG7C,GAAG,GAAG+C,EAAE,GAAG9C,GAAG,EACnB2C,EAAE,GAAG1C,GAAG,GAAG4C,EAAE,GAAG3C,GAAG,EACnB0C,EAAE,GAAG3C,GAAG,GAAG6C,EAAE,GAAG5C,GAAG,EACnByC,EAAE,GAAGxC,EAAE,GAAG0C,EAAE,GAAGzC,EAAE,GAAG2C,EAAE,EACtBH,EAAE,GAAGzC,EAAE,GAAG2C,EAAE,GAAG1C,EAAE,GAAG4C,EAAE,CACvB;IAED,IAAI,CAAC,IAAI,CAAC1D,QAAQ,IAAIgB,CAAC,CAACpW,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAACuW,MAAM,CAACvW,IAAI,CAAC,GAAG,CAAC,EAAE;MAC3D,IAAI,CAACmW,KAAK,CAACC,CAAC,CAAC;;IAEf,IAAI,CAAClW,GAAG,CAAC8Y,cAAc,CAAC,SAAS,EAAER,MAAM,CAAC;IAC1C,MAAMS,EAAE,GAAGT,MAAM,GAAG,KAAK,GAAG,KAAK;IACjC,OAAO,IAAI,CAACtY,GAAG,CAACgZ,UAAU,CAAE,IAAG,IAAI,CAAC1Y,EAAG,IAAGyY,EAAG,EAAC,CAAC;;AAEnD;AAEA,MAAME,iBAAiB,SAASjE,WAAW,CAAC;EAC1C1W,WAAWA,CAAC0B,GAAG,EAAEkZ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IAC/B,KAAK,CAACrZ,GAAG,CAAC;IACV,IAAI,CAACkZ,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;;EAGdzC,MAAMA,CAACT,EAAE,EAAE;IACT,OAAO,IAAI,CAACnW,GAAG,CAACS,GAAG,CAAC;MAClB6Y,WAAW,EAAE,CAAC;MACdtP,UAAU,EAAE,IAAI,CAACwL,WAAW;MAC5B+D,MAAM,EAAE,CAAC,IAAI,CAACL,EAAE,EAAE,IAAI,CAACC,EAAE,EAAE,IAAI,CAACC,EAAE,EAAE,IAAI,CAACC,EAAE,CAAC;MAC5CG,QAAQ,EAAErD,EAAE;MACZsD,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;KACpB,CAAC;;EAGJvC,eAAeA,GAAG;IAChB,OAAO,IAAI+B,iBAAiB,CAAC,IAAI,CAACjZ,GAAG,EAAE,IAAI,CAACkZ,EAAE,EAAE,IAAI,CAACC,EAAE,EAAE,IAAI,CAACC,EAAE,EAAE,IAAI,CAACC,EAAE,CAAC;;AAE9E;AAEA,MAAMK,iBAAiB,SAAS1E,WAAW,CAAC;EAC1C1W,WAAWA,CAAC0B,GAAG,EAAEkZ,EAAE,EAAEC,EAAE,EAAEQ,EAAE,EAAEP,EAAE,EAAEC,EAAE,EAAEO,EAAE,EAAE;IACvC,KAAK,CAAC5Z,GAAG,CAAC;IACV,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACkZ,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACQ,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACP,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACO,EAAE,GAAGA,EAAE;;EAGdhD,MAAMA,CAACT,EAAE,EAAE;IACT,OAAO,IAAI,CAACnW,GAAG,CAACS,GAAG,CAAC;MAClB6Y,WAAW,EAAE,CAAC;MACdtP,UAAU,EAAE,IAAI,CAACwL,WAAW;MAC5B+D,MAAM,EAAE,CAAC,IAAI,CAACL,EAAE,EAAE,IAAI,CAACC,EAAE,EAAE,IAAI,CAACQ,EAAE,EAAE,IAAI,CAACP,EAAE,EAAE,IAAI,CAACC,EAAE,EAAE,IAAI,CAACO,EAAE,CAAC;MAC9DJ,QAAQ,EAAErD,EAAE;MACZsD,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI;KACpB,CAAC;;EAGJvC,eAAeA,GAAG;IAChB,OAAO,IAAIwC,iBAAiB,CAC1B,IAAI,CAAC1Z,GAAG,EACR,IAAI,CAACkZ,EAAE,EACP,IAAI,CAACC,EAAE,EACP,IAAI,CAACQ,EAAE,EACP,IAAI,CAACP,EAAE,EACP,IAAI,CAACC,EAAE,EACP,IAAI,CAACO,EACP,CAAC;;AAEL;AAEA,eAAe;EAAE5E,WAAW;EAAEiE,iBAAiB;EAAES;AAAkB,CAAC;;AC3QpE;AACA;AACA;;AAEA,MAAMG,qBAAqB,GAAG,CAAC,YAAY,EAAE,WAAW,CAAC;AAEzD,MAAMC,gBAAgB,CAAC;EACrBxb,WAAWA,CAAC0B,GAAG,EAAE+Z,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAC3C,IAAI,CAACla,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC+Z,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;;EAGtBC,aAAaA,GAAG;;;IAGd,MAAMvR,SAAS,GAAG,IAAI,CAAC5I,GAAG,CAACS,GAAG,EAAE;IAChCmI,SAAS,CAAC1H,GAAG,EAAE;;;IAGf,MAAM,CAACqX,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAI,CAAC5Y,GAAG,CAAC6Y,IAAI;IAC9C,MAAM,CAAClD,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvD,MAAME,CAAC,GAAG,CACRqC,EAAE,GAAG5C,GAAG,GAAG8C,EAAE,GAAG7C,GAAG,EACnB4C,EAAE,GAAG7C,GAAG,GAAG+C,EAAE,GAAG9C,GAAG,EACnB2C,EAAE,GAAG1C,GAAG,GAAG4C,EAAE,GAAG3C,GAAG,EACnB0C,EAAE,GAAG3C,GAAG,GAAG6C,EAAE,GAAG5C,GAAG,EACnByC,EAAE,GAAGxC,EAAE,GAAG0C,EAAE,GAAGzC,EAAE,GAAG2C,EAAE,EACtBH,EAAE,GAAGzC,EAAE,GAAG2C,EAAE,GAAG1C,EAAE,GAAG4C,EAAE,CACvB;IACD,MAAM/B,OAAO,GAAG,IAAI,CAAC7W,GAAG,CAACS,GAAG,CAAC;MAC3BsI,IAAI,EAAE,SAAS;MACf+N,WAAW,EAAE,CAAC;;MACdmB,SAAS,EAAE,CAAC;;MACZC,UAAU,EAAE,CAAC;;MACbV,IAAI,EAAE,IAAI,CAACuC,IAAI;MACf5B,KAAK,EAAE,IAAI,CAAC6B,KAAK;MACjB5B,KAAK,EAAE,IAAI,CAAC6B,KAAK;MACjBjD,MAAM,EAAEd,CAAC,CAACrV,GAAG,CAAC2O,CAAC,IAAI,CAACA,CAAC,CAAC4K,OAAO,CAAC,CAAC,CAAC,CAAC;MACjC/Q,SAAS,EAAET;KACZ,CAAC;IACFiO,OAAO,CAAC3V,GAAG,CAAC,IAAI,CAACgZ,MAAM,CAAC;IACxB,OAAOrD,OAAO;;EAGhBwD,uBAAuBA,GAAG;;;IAGxBR,qBAAqB,CAACS,OAAO,CAACC,MAAM,IAAI;MACtC,MAAMC,IAAI,GAAG,IAAI,CAACC,sBAAsB,CAACF,MAAM,CAAC;MAEhD,IAAI,IAAI,CAACva,GAAG,CAACoX,IAAI,CAACrN,WAAW,CAACyQ,IAAI,CAAC,EAAE;MACrC,MAAME,EAAE,GAAG,IAAI,CAAC1a,GAAG,CAACS,GAAG,CAAC,CAAC,SAAS,EAAE8Z,MAAM,CAAC,CAAC;MAC5CG,EAAE,CAACxZ,GAAG,EAAE;MACR,IAAI,CAAClB,GAAG,CAACoX,IAAI,CAACrN,WAAW,CAACyQ,IAAI,CAAC,GAAGE,EAAE;KACrC,CAAC;;EAGJD,sBAAsBA,CAACE,oBAAoB,EAAE;IAC3C,OAAQ,MAAKA,oBAAqB,EAAC;;EAGrC1E,KAAKA,GAAG;IACN,IAAI,CAAC,IAAI,CAAC3V,EAAE,EAAE;MACZ,IAAI,CAACN,GAAG,CAAC4a,aAAa,GAAG,IAAI,CAAC5a,GAAG,CAAC4a,aAAa,GAAG,CAAC;MACnD,IAAI,CAACta,EAAE,GAAG,GAAG,GAAG,IAAI,CAACN,GAAG,CAAC4a,aAAa;MACtC,IAAI,CAAC/D,OAAO,GAAG,IAAI,CAACsD,aAAa,EAAE;;;;IAIrC,IAAI,CAAC,IAAI,CAACna,GAAG,CAACoX,IAAI,CAACvN,QAAQ,CAAC,IAAI,CAACvJ,EAAE,CAAC,EAAE;MACpC,IAAI,CAACN,GAAG,CAACoX,IAAI,CAACvN,QAAQ,CAAC,IAAI,CAACvJ,EAAE,CAAC,GAAG,IAAI,CAACuW,OAAO;;;EAIlDxJ,KAAKA,CAACiL,MAAM,EAAEuC,YAAY,EAAE;;IAE1B,IAAI,CAACR,uBAAuB,EAAE;IAC9B,IAAI,CAACpE,KAAK,EAAE;IAEZ,MAAM6E,eAAe,GAAG,IAAI,CAAC9a,GAAG,CAACuV,eAAe,CAACsF,YAAY,CAAC;IAC9D,IAAI,CAACC,eAAe,EAClB,MAAM1c,KAAK,CAAE,kCAAiCyc,YAAa,GAAE,CAAC;;;IAGhE,MAAML,IAAI,GAAG,IAAI,CAACC,sBAAsB,CACtC,IAAI,CAACza,GAAG,CAAC+a,cAAc,CAACD,eAAe,CACzC,CAAC;IACD,IAAI,CAAC9a,GAAG,CAAC8Y,cAAc,CAAC0B,IAAI,EAAElC,MAAM,CAAC;;;IAGrC,MAAMS,EAAE,GAAGT,MAAM,GAAG,KAAK,GAAG,KAAK;IACjC,OAAO,IAAI,CAACtY,GAAG,CAACgZ,UAAU,CACvB,GAAE8B,eAAe,CAAChb,IAAI,CAAC,GAAG,CAAE,KAAI,IAAI,CAACQ,EAAG,IAAGyY,EAAG,EACjD,CAAC;;AAEL;AAEA,cAAe;EAAEe;AAAiB,CAAC;;AChGnC,MAAM;eAAE9E,aAAW;qBAAEiE,mBAAiB;qBAAES;AAAkB,CAAC,GAAGsB,QAAQ;AACtE,MAAM;oBAAElB;AAAiB,CAAC,GAAGjD,OAAO;AAEpC,iBAAe;EACboE,SAASA,GAAG;IACV,IAAI,CAAC1a,UAAU,GAAG,EAAE;;IAEpB,IAAI,CAAC2a,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACP,aAAa,GAAG,CAAC;IACtB,OAAQ,IAAI,CAACjE,UAAU,GAAG,CAAC;GAC5B;EAEDpB,eAAeA,CAAC7K,KAAK,EAAE;IACrB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,IAAIA,KAAK,CAAC0Q,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC3B,IAAI1Q,KAAK,CAACpL,MAAM,KAAK,CAAC,EAAE;UACtBoL,KAAK,GAAGA,KAAK,CAACnI,OAAO,CACnB,kCAAkC,EAClC,eACF,CAAC;;QAEH,MAAM8Y,GAAG,GAAGC,QAAQ,CAAC5Q,KAAK,CAACpJ,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACxCoJ,KAAK,GAAG,CAAC2Q,GAAG,IAAI,EAAE,EAAGA,GAAG,IAAI,CAAC,GAAI,IAAI,EAAEA,GAAG,GAAG,IAAI,CAAC;OACnD,MAAM,IAAIE,WAAW,CAAC7Q,KAAK,CAAC,EAAE;QAC7BA,KAAK,GAAG6Q,WAAW,CAAC7Q,KAAK,CAAC;OAC3B,MAAM,IAAI,IAAI,CAACnK,UAAU,CAACmK,KAAK,CAAC,EAAE;QACjC,OAAO,IAAI,CAACnK,UAAU,CAACmK,KAAK,CAAC;;;IAIjC,IAAIrJ,KAAK,CAAC4B,OAAO,CAACyH,KAAK,CAAC,EAAE;;MAExB,IAAIA,KAAK,CAACpL,MAAM,KAAK,CAAC,EAAE;QACtBoL,KAAK,GAAGA,KAAK,CAAC7J,GAAG,CAAC2a,IAAI,IAAIA,IAAI,GAAG,GAAG,CAAC;;OAEtC,MAAM,IAAI9Q,KAAK,CAACpL,MAAM,KAAK,CAAC,EAAE;QAC7BoL,KAAK,GAAGA,KAAK,CAAC7J,GAAG,CAAC2a,IAAI,IAAIA,IAAI,GAAG,GAAG,CAAC;;MAEvC,OAAO9Q,KAAK;;IAGd,OAAO,IAAI;GACZ;EAED+Q,SAASA,CAAC/Q,KAAK,EAAE4N,MAAM,EAAE;IACvB,IAAI5N,KAAK,YAAYsK,aAAW,EAAE;MAChCtK,KAAK,CAAC2C,KAAK,CAACiL,MAAM,CAAC;MACnB,OAAO,IAAI;;KAEZ,MAAM,IAAIjX,KAAK,CAAC4B,OAAO,CAACyH,KAAK,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,YAAYoP,kBAAgB,EAAE;MACvEpP,KAAK,CAAC,CAAC,CAAC,CAAC2C,KAAK,CAACiL,MAAM,EAAE5N,KAAK,CAAC,CAAC,CAAC,CAAC;MAChC,OAAO,IAAI;;;IAGb,OAAO,IAAI,CAACgR,aAAa,CAAChR,KAAK,EAAE4N,MAAM,CAAC;GACzC;EAEDoD,aAAaA,CAAChR,KAAK,EAAE4N,MAAM,EAAE;IAC3B5N,KAAK,GAAG,IAAI,CAAC6K,eAAe,CAAC7K,KAAK,CAAC;IACnC,IAAI,CAACA,KAAK,EAAE;MACV,OAAO,KAAK;;IAGd,MAAMqO,EAAE,GAAGT,MAAM,GAAG,KAAK,GAAG,KAAK;IACjC,MAAMqD,KAAK,GAAG,IAAI,CAACZ,cAAc,CAACrQ,KAAK,CAAC;IACxC,IAAI,CAACoO,cAAc,CAAC6C,KAAK,EAAErD,MAAM,CAAC;IAElC,IAAI5N,KAAK,YAAY3K,SAAS,EAAE;MAC9B,IAAI,CAACqX,IAAI,CAACrN,WAAW,CAACW,KAAK,CAACpK,EAAE,CAAC,GAAGoK,KAAK,CAACjK,GAAG;MAC3C,IAAI,CAACuY,UAAU,CAAE,KAAID,EAAG,EAAC,CAAC;KAC3B,MAAM;MACL,IAAI,CAACC,UAAU,CAAE,GAAEtO,KAAK,CAAC5K,IAAI,CAAC,GAAG,CAAE,IAAGiZ,EAAG,EAAC,CAAC;;IAG7C,OAAO,IAAI;GACZ;EAEDD,cAAcA,CAAC6C,KAAK,EAAErD,MAAM,EAAE;IAC5B,MAAMS,EAAE,GAAGT,MAAM,GAAG,IAAI,GAAG,IAAI;IAC/B,OAAO,IAAI,CAACU,UAAU,CAAE,IAAG2C,KAAM,IAAG5C,EAAG,EAAC,CAAC;GAC1C;EAEDgC,cAAcA,CAACrQ,KAAK,EAAE;IACpB,IAAIA,KAAK,YAAY3K,SAAS,EAAE;MAC9B,OAAO2K,KAAK,CAACpK,EAAE;;IAGjB,OAAOoK,KAAK,CAACpL,MAAM,KAAK,CAAC,GAAG,YAAY,GAAG,WAAW;GACvD;EAEDsc,SAASA,CAAClR,KAAK,EAAE4K,OAAO,EAAE;IACxB,MAAMuG,GAAG,GAAG,IAAI,CAACJ,SAAS,CAAC/Q,KAAK,EAAE,KAAK,CAAC;IACxC,IAAImR,GAAG,EAAE;MACP,IAAI,CAACC,WAAW,CAACxG,OAAO,CAAC;;;;;IAK3B,IAAI,CAACyG,UAAU,GAAG,CAACrR,KAAK,EAAE4K,OAAO,CAAC;IAClC,OAAO,IAAI;GACZ;EAED0G,WAAWA,CAACtR,KAAK,EAAE4K,OAAO,EAAE;IAC1B,MAAMuG,GAAG,GAAG,IAAI,CAACJ,SAAS,CAAC/Q,KAAK,EAAE,IAAI,CAAC;IACvC,IAAImR,GAAG,EAAE;MACP,IAAI,CAACI,aAAa,CAAC3G,OAAO,CAAC;;IAE7B,OAAO,IAAI;GACZ;EAEDA,OAAOA,CAACA,OAAO,EAAE;IACf,IAAI,CAAC4G,UAAU,CAAC5G,OAAO,EAAEA,OAAO,CAAC;IACjC,OAAO,IAAI;GACZ;EAEDwG,WAAWA,CAACxG,OAAO,EAAE;IACnB,IAAI,CAAC4G,UAAU,CAAC5G,OAAO,EAAE,IAAI,CAAC;IAC9B,OAAO,IAAI;GACZ;EAED2G,aAAaA,CAAC3G,OAAO,EAAE;IACrB,IAAI,CAAC4G,UAAU,CAAC,IAAI,EAAE5G,OAAO,CAAC;IAC9B,OAAO,IAAI;GACZ;EAED4G,UAAUA,CAACJ,WAAW,EAAEG,aAAa,EAAE;IACrC,IAAInT,UAAU,EAAE7I,IAAI;IACpB,IAAI6b,WAAW,IAAI,IAAI,IAAIG,aAAa,IAAI,IAAI,EAAE;MAChD;;IAGF,IAAIH,WAAW,IAAI,IAAI,EAAE;MACvBA,WAAW,GAAGvY,IAAI,CAACkS,GAAG,CAAC,CAAC,EAAElS,IAAI,CAACmP,GAAG,CAAC,CAAC,EAAEoJ,WAAW,CAAC,CAAC;;IAErD,IAAIG,aAAa,IAAI,IAAI,EAAE;MACzBA,aAAa,GAAG1Y,IAAI,CAACkS,GAAG,CAAC,CAAC,EAAElS,IAAI,CAACmP,GAAG,CAAC,CAAC,EAAEuJ,aAAa,CAAC,CAAC;;IAEzD,MAAMtd,GAAG,GAAI,GAAEmd,WAAY,IAAGG,aAAc,EAAC;IAE7C,IAAI,IAAI,CAACf,gBAAgB,CAACvc,GAAG,CAAC,EAAE;MAC9B,CAACmK,UAAU,EAAE7I,IAAI,CAAC,GAAG,IAAI,CAACib,gBAAgB,CAACvc,GAAG,CAAC;KAChD,MAAM;MACLmK,UAAU,GAAG;QAAEC,IAAI,EAAE;OAAa;MAElC,IAAI+S,WAAW,IAAI,IAAI,EAAE;QACvBhT,UAAU,CAACqT,EAAE,GAAGL,WAAW;;MAE7B,IAAIG,aAAa,IAAI,IAAI,EAAE;QACzBnT,UAAU,CAACsT,EAAE,GAAGH,aAAa;;MAG/BnT,UAAU,GAAG,IAAI,CAACrI,GAAG,CAACqI,UAAU,CAAC;MACjCA,UAAU,CAAC5H,GAAG,EAAE;MAChB,MAAMZ,EAAE,GAAG,EAAE,IAAI,CAAC6a,aAAa;MAC/Blb,IAAI,GAAI,KAAIK,EAAG,EAAC;MAChB,IAAI,CAAC4a,gBAAgB,CAACvc,GAAG,CAAC,GAAG,CAACmK,UAAU,EAAE7I,IAAI,CAAC;;IAGjD,IAAI,CAACmX,IAAI,CAACzN,WAAW,CAAC1J,IAAI,CAAC,GAAG6I,UAAU;IACxC,OAAO,IAAI,CAACkQ,UAAU,CAAE,IAAG/Y,IAAK,KAAI,CAAC;GACtC;EAEDoc,cAAcA,CAACnD,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IAC7B,OAAO,IAAIJ,mBAAiB,CAAC,IAAI,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;GACnD;EAEDiD,cAAcA,CAACpD,EAAE,EAAEC,EAAE,EAAEQ,EAAE,EAAEP,EAAE,EAAEC,EAAE,EAAEO,EAAE,EAAE;IACrC,OAAO,IAAIF,mBAAiB,CAAC,IAAI,EAAER,EAAE,EAAEC,EAAE,EAAEQ,EAAE,EAAEP,EAAE,EAAEC,EAAE,EAAEO,EAAE,CAAC;GAC3D;EAED/C,OAAOA,CAAC0F,IAAI,EAAEvC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAClC,OAAO,IAAIJ,kBAAgB,CAAC,IAAI,EAAEyC,IAAI,EAAEvC,KAAK,EAAEC,KAAK,EAAEC,MAAM,CAAC;GAC9D;EAEDsC,YAAYA,CAACvc,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IAC7B,MAAMqK,KAAK,GAAG,IAAI3K,SAAS,CAAC,IAAI,EAAEE,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACnD,IAAI,CAACE,UAAU,CAACN,IAAI,CAAC,GAAGyK,KAAK;IAC7B,OAAO,IAAI;;AAEf,CAAC;AAED,IAAI6Q,WAAW,GAAG;EAChBkB,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC7BC,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;EACnBC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3BC,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACtBC,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACtBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACvBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChBC,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/BC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;EACjBC,UAAU,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EAC1BC,KAAK,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;EACpBC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EACzBC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACzBC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;EACzBC,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;EACrBC,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/BC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACzBC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;EACtBC,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;EACnBC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;EACrBC,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;EACvBC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;EAC7BC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACzBC,SAAS,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;EACtBC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACzBC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;EAC1BC,cAAc,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;EAC7BC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACzBC,UAAU,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EAC1BC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EACpBC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3BC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC7BC,aAAa,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAC5BC,aAAa,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC3BC,aAAa,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC3BC,aAAa,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;EACzBC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EACxBC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACxBC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACxBC,UAAU,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;EACxBC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;EAC1BC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;EACtBC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3BC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACnBC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;EACzBC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACrBC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACrBC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;EAClBC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;EAC3BC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACzBC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACxBC,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;EACxBC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC;EACpBC,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACtBC,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACtBC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACzBC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC9BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACxBC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC7BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,oBAAoB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACrCC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,aAAa,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EAC7BC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC7BC,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/BC,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/BC,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/BC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;EACjBC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;EACxBC,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACtBC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;EACtBC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EACnBC,gBAAgB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACjCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;EACvBC,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EAC5BC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC7BC,cAAc,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EAC9BC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAChCC,iBAAiB,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;EAChCC,eAAe,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/BC,eAAe,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EAC/BC,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACzBC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;EACjBC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACxBC,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACpBC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;EACzBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACrBC,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;EACvBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACvBC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC9BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC9BC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC9BC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;EACpBC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACrBC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACrBC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3BC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;EACrBC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAChBC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EACzBC,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;EAC1BC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACvBC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;EAC1BC,QAAQ,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;EACvBC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACzBC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;EACrBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACvBC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACxBC,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;EACzBC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACrBC,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1BC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EACzBC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpBC,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;EACnBC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACxBC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;EACrBC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EACzBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACvBC,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACtBC,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACtBC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3BC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACrBC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;AAC5B,CAAC;;AC9UD,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;AAE1BL,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,CAAC;AAE/B,MAAMC,UAAU,GAAG;EACjBC,CAAC,EAAE,CAAC;EACJjnB,CAAC,EAAE,CAAC;EACJgB,CAAC,EAAE,CAAC;EACJsC,CAAC,EAAE,CAAC;EACJ4jB,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJ3kB,CAAC,EAAE,CAAC;EACJxB,CAAC,EAAE,CAAC;EACJ+V,CAAC,EAAE,CAAC;EACJqQ,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJ9O,CAAC,EAAE,CAAC;EACJ+O,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJlW,CAAC,EAAE,CAAC;EACJjB,CAAC,EAAE,CAAC;EACJoX,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE;AACL,CAAC;AAED,MAAMC,KAAK,GAAG,UAASC,IAAI,EAAE;EAC3B,IAAIC,GAAG;EACP,MAAMC,GAAG,GAAG,EAAE;EACd,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,MAAM,GAAG,CAAC;EAEd,KAAK,IAAI7kB,CAAC,IAAIukB,IAAI,EAAE;IAClB,IAAIb,UAAU,CAAC1jB,CAAC,CAAC,IAAI,IAAI,EAAE;MACzB6kB,MAAM,GAAGnB,UAAU,CAAC1jB,CAAC,CAAC;MACtB,IAAIwkB,GAAG,EAAE;;QAEP,IAAIG,MAAM,CAAC7nB,MAAM,GAAG,CAAC,EAAE;UACrB4nB,IAAI,CAACA,IAAI,CAAC5nB,MAAM,CAAC,GAAG,CAAC6nB,MAAM;;QAE7BF,GAAG,CAACA,GAAG,CAAC3nB,MAAM,CAAC,GAAG;UAAE0nB,GAAG;UAAEE;SAAM;QAE/BA,IAAI,GAAG,EAAE;QACTC,MAAM,GAAG,EAAE;QACXC,YAAY,GAAG,KAAK;;MAGtBJ,GAAG,GAAGxkB,CAAC;KACR,MAAM,IACL,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC8kB,QAAQ,CAAC9kB,CAAC,CAAC,IACrBA,CAAC,KAAK,GAAG,IAAI2kB,MAAM,CAAC7nB,MAAM,GAAG,CAAC,IAAI6nB,MAAM,CAACA,MAAM,CAAC7nB,MAAM,GAAG,CAAC,CAAC,KAAK,GAAI,IACpEkD,CAAC,KAAK,GAAG,IAAI4kB,YAAa,EAC3B;MACA,IAAID,MAAM,CAAC7nB,MAAM,KAAK,CAAC,EAAE;QACvB;;MAGF,IAAI4nB,IAAI,CAAC5nB,MAAM,KAAK+nB,MAAM,EAAE;;QAE1BJ,GAAG,CAACA,GAAG,CAAC3nB,MAAM,CAAC,GAAG;UAAE0nB,GAAG;UAAEE;SAAM;QAC/BA,IAAI,GAAG,CAAC,CAACC,MAAM,CAAC;;;QAGhB,IAAIH,GAAG,KAAK,GAAG,EAAE;UACfA,GAAG,GAAG,GAAG;;QAEX,IAAIA,GAAG,KAAK,GAAG,EAAE;UACfA,GAAG,GAAG,GAAG;;OAEZ,MAAM;QACLE,IAAI,CAACA,IAAI,CAAC5nB,MAAM,CAAC,GAAG,CAAC6nB,MAAM;;MAG7BC,YAAY,GAAG5kB,CAAC,KAAK,GAAG;;;MAGxB2kB,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAACG,QAAQ,CAAC9kB,CAAC,CAAC,GAAGA,CAAC,GAAG,EAAE;KACzC,MAAM;MACL2kB,MAAM,IAAI3kB,CAAC;MACX,IAAIA,CAAC,KAAK,GAAG,EAAE;QACb4kB,YAAY,GAAG,IAAI;;;;;;EAMzB,IAAID,MAAM,CAAC7nB,MAAM,GAAG,CAAC,EAAE;IACrB,IAAI4nB,IAAI,CAAC5nB,MAAM,KAAK+nB,MAAM,EAAE;;MAE1BJ,GAAG,CAACA,GAAG,CAAC3nB,MAAM,CAAC,GAAG;QAAE0nB,GAAG;QAAEE;OAAM;MAC/BA,IAAI,GAAG,CAAC,CAACC,MAAM,CAAC;;;MAGhB,IAAIH,GAAG,KAAK,GAAG,EAAE;QACfA,GAAG,GAAG,GAAG;;MAEX,IAAIA,GAAG,KAAK,GAAG,EAAE;QACfA,GAAG,GAAG,GAAG;;KAEZ,MAAM;MACLE,IAAI,CAACA,IAAI,CAAC5nB,MAAM,CAAC,GAAG,CAAC6nB,MAAM;;;EAI/BF,GAAG,CAACA,GAAG,CAAC3nB,MAAM,CAAC,GAAG;IAAE0nB,GAAG;IAAEE;GAAM;EAE/B,OAAOD,GAAG;AACZ,CAAC;AAED,MAAM5Z,KAAK,GAAG,UAASka,QAAQ,EAAEvnB,GAAG,EAAE;;EAEpC4lB,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,CAAC;;;EAG/B,KAAK,IAAIrkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2lB,QAAQ,CAACjoB,MAAM,EAAEsC,CAAC,EAAE,EAAE;IACxC,MAAMY,CAAC,GAAG+kB,QAAQ,CAAC3lB,CAAC,CAAC;IACrB,IAAI,OAAO4lB,OAAO,CAAChlB,CAAC,CAACwkB,GAAG,CAAC,KAAK,UAAU,EAAE;MACxCQ,OAAO,CAAChlB,CAAC,CAACwkB,GAAG,CAAC,CAAChnB,GAAG,EAAEwC,CAAC,CAAC0kB,IAAI,CAAC;;;AAGjC,CAAC;AAED,MAAMM,OAAO,GAAG;EACdrnB,CAACA,CAACH,GAAG,EAAEd,CAAC,EAAE;IACR0mB,EAAE,GAAG1mB,CAAC,CAAC,CAAC,CAAC;IACT2mB,EAAE,GAAG3mB,CAAC,CAAC,CAAC,CAAC;IACT4mB,EAAE,GAAGC,EAAE,GAAG,IAAI;IACdC,EAAE,GAAGJ,EAAE;IACPK,EAAE,GAAGJ,EAAE;IACP,OAAO7lB,GAAG,CAACynB,MAAM,CAAC7B,EAAE,EAAEC,EAAE,CAAC;GAC1B;EAED3P,CAACA,CAAClW,GAAG,EAAEd,CAAC,EAAE;IACR0mB,EAAE,IAAI1mB,CAAC,CAAC,CAAC,CAAC;IACV2mB,EAAE,IAAI3mB,CAAC,CAAC,CAAC,CAAC;IACV4mB,EAAE,GAAGC,EAAE,GAAG,IAAI;IACdC,EAAE,GAAGJ,EAAE;IACPK,EAAE,GAAGJ,EAAE;IACP,OAAO7lB,GAAG,CAACynB,MAAM,CAAC7B,EAAE,EAAEC,EAAE,CAAC;GAC1B;EAED3lB,CAACA,CAACF,GAAG,EAAEd,CAAC,EAAE;IACR0mB,EAAE,GAAG1mB,CAAC,CAAC,CAAC,CAAC;IACT2mB,EAAE,GAAG3mB,CAAC,CAAC,CAAC,CAAC;IACT4mB,EAAE,GAAG5mB,CAAC,CAAC,CAAC,CAAC;IACT6mB,EAAE,GAAG7mB,CAAC,CAAC,CAAC,CAAC;IACT,OAAOc,GAAG,CAAC0nB,aAAa,CAAC,GAAGxoB,CAAC,CAAC;GAC/B;EAEDsD,CAACA,CAACxC,GAAG,EAAEd,CAAC,EAAE;IACRc,GAAG,CAAC0nB,aAAa,CACfxoB,CAAC,CAAC,CAAC,CAAC,GAAG0mB,EAAE,EACT1mB,CAAC,CAAC,CAAC,CAAC,GAAG2mB,EAAE,EACT3mB,CAAC,CAAC,CAAC,CAAC,GAAG0mB,EAAE,EACT1mB,CAAC,CAAC,CAAC,CAAC,GAAG2mB,EAAE,EACT3mB,CAAC,CAAC,CAAC,CAAC,GAAG0mB,EAAE,EACT1mB,CAAC,CAAC,CAAC,CAAC,GAAG2mB,EACT,CAAC;IACDC,EAAE,GAAGF,EAAE,GAAG1mB,CAAC,CAAC,CAAC,CAAC;IACd6mB,EAAE,GAAGF,EAAE,GAAG3mB,CAAC,CAAC,CAAC,CAAC;IACd0mB,EAAE,IAAI1mB,CAAC,CAAC,CAAC,CAAC;IACV,OAAQ2mB,EAAE,IAAI3mB,CAAC,CAAC,CAAC,CAAC;GACnB;EAEDwY,CAACA,CAAC1X,GAAG,EAAEd,CAAC,EAAE;IACR,IAAI4mB,EAAE,KAAK,IAAI,EAAE;MACfA,EAAE,GAAGF,EAAE;MACPG,EAAE,GAAGF,EAAE;;IAGT7lB,GAAG,CAAC0nB,aAAa,CAAC9B,EAAE,IAAIE,EAAE,GAAGF,EAAE,CAAC,EAAEC,EAAE,IAAIE,EAAE,GAAGF,EAAE,CAAC,EAAE3mB,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE4mB,EAAE,GAAG5mB,CAAC,CAAC,CAAC,CAAC;IACT6mB,EAAE,GAAG7mB,CAAC,CAAC,CAAC,CAAC;IACT0mB,EAAE,GAAG1mB,CAAC,CAAC,CAAC,CAAC;IACT,OAAQ2mB,EAAE,GAAG3mB,CAAC,CAAC,CAAC,CAAC;GAClB;EAEDunB,CAACA,CAACzmB,GAAG,EAAEd,CAAC,EAAE;IACR,IAAI4mB,EAAE,KAAK,IAAI,EAAE;MACfA,EAAE,GAAGF,EAAE;MACPG,EAAE,GAAGF,EAAE;;IAGT7lB,GAAG,CAAC0nB,aAAa,CACf9B,EAAE,IAAIE,EAAE,GAAGF,EAAE,CAAC,EACdC,EAAE,IAAIE,EAAE,GAAGF,EAAE,CAAC,EACdD,EAAE,GAAG1mB,CAAC,CAAC,CAAC,CAAC,EACT2mB,EAAE,GAAG3mB,CAAC,CAAC,CAAC,CAAC,EACT0mB,EAAE,GAAG1mB,CAAC,CAAC,CAAC,CAAC,EACT2mB,EAAE,GAAG3mB,CAAC,CAAC,CAAC,CACV,CAAC;IACD4mB,EAAE,GAAGF,EAAE,GAAG1mB,CAAC,CAAC,CAAC,CAAC;IACd6mB,EAAE,GAAGF,EAAE,GAAG3mB,CAAC,CAAC,CAAC,CAAC;IACd0mB,EAAE,IAAI1mB,CAAC,CAAC,CAAC,CAAC;IACV,OAAQ2mB,EAAE,IAAI3mB,CAAC,CAAC,CAAC,CAAC;GACnB;EAEDqnB,CAACA,CAACvmB,GAAG,EAAEd,CAAC,EAAE;IACR4mB,EAAE,GAAG5mB,CAAC,CAAC,CAAC,CAAC;IACT6mB,EAAE,GAAG7mB,CAAC,CAAC,CAAC,CAAC;IACT0mB,EAAE,GAAG1mB,CAAC,CAAC,CAAC,CAAC;IACT2mB,EAAE,GAAG3mB,CAAC,CAAC,CAAC,CAAC;IACT,OAAOc,GAAG,CAAC2nB,gBAAgB,CAACzoB,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAE0mB,EAAE,EAAEC,EAAE,CAAC;GAChD;EAEDW,CAACA,CAACxmB,GAAG,EAAEd,CAAC,EAAE;IACRc,GAAG,CAAC2nB,gBAAgB,CAACzoB,CAAC,CAAC,CAAC,CAAC,GAAG0mB,EAAE,EAAE1mB,CAAC,CAAC,CAAC,CAAC,GAAG2mB,EAAE,EAAE3mB,CAAC,CAAC,CAAC,CAAC,GAAG0mB,EAAE,EAAE1mB,CAAC,CAAC,CAAC,CAAC,GAAG2mB,EAAE,CAAC;IAChEC,EAAE,GAAGF,EAAE,GAAG1mB,CAAC,CAAC,CAAC,CAAC;IACd6mB,EAAE,GAAGF,EAAE,GAAG3mB,CAAC,CAAC,CAAC,CAAC;IACd0mB,EAAE,IAAI1mB,CAAC,CAAC,CAAC,CAAC;IACV,OAAQ2mB,EAAE,IAAI3mB,CAAC,CAAC,CAAC,CAAC;GACnB;EAEDwnB,CAACA,CAAC1mB,GAAG,EAAEd,CAAC,EAAE;IACR,IAAI4mB,EAAE,KAAK,IAAI,EAAE;MACfA,EAAE,GAAGF,EAAE;MACPG,EAAE,GAAGF,EAAE;KACR,MAAM;MACLC,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAE,CAAC;MACnBG,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAE,CAAC;;IAGrB7lB,GAAG,CAAC2nB,gBAAgB,CAAC7B,EAAE,EAAEC,EAAE,EAAE7mB,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC4mB,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAE,CAAC;IACnBG,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAE,CAAC;IACnBD,EAAE,GAAG1mB,CAAC,CAAC,CAAC,CAAC;IACT,OAAQ2mB,EAAE,GAAG3mB,CAAC,CAAC,CAAC,CAAC;GAClB;EAEDynB,CAACA,CAAC3mB,GAAG,EAAEd,CAAC,EAAE;IACR,IAAI4mB,EAAE,KAAK,IAAI,EAAE;MACfA,EAAE,GAAGF,EAAE;MACPG,EAAE,GAAGF,EAAE;KACR,MAAM;MACLC,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAE,CAAC;MACnBG,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAE,CAAC;;IAGrB7lB,GAAG,CAAC2nB,gBAAgB,CAAC7B,EAAE,EAAEC,EAAE,EAAEH,EAAE,GAAG1mB,CAAC,CAAC,CAAC,CAAC,EAAE2mB,EAAE,GAAG3mB,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD0mB,EAAE,IAAI1mB,CAAC,CAAC,CAAC,CAAC;IACV,OAAQ2mB,EAAE,IAAI3mB,CAAC,CAAC,CAAC,CAAC;GACnB;EAEDinB,CAACA,CAACnmB,GAAG,EAAEd,CAAC,EAAE;IACR0oB,QAAQ,CAAC5nB,GAAG,EAAE4lB,EAAE,EAAEC,EAAE,EAAE3mB,CAAC,CAAC;IACxB0mB,EAAE,GAAG1mB,CAAC,CAAC,CAAC,CAAC;IACT,OAAQ2mB,EAAE,GAAG3mB,CAAC,CAAC,CAAC,CAAC;GAClB;EAEDA,CAACA,CAACc,GAAG,EAAEd,CAAC,EAAE;IACRA,CAAC,CAAC,CAAC,CAAC,IAAI0mB,EAAE;IACV1mB,CAAC,CAAC,CAAC,CAAC,IAAI2mB,EAAE;IACV+B,QAAQ,CAAC5nB,GAAG,EAAE4lB,EAAE,EAAEC,EAAE,EAAE3mB,CAAC,CAAC;IACxB0mB,EAAE,GAAG1mB,CAAC,CAAC,CAAC,CAAC;IACT,OAAQ2mB,EAAE,GAAG3mB,CAAC,CAAC,CAAC,CAAC;GAClB;EAEDonB,CAACA,CAACtmB,GAAG,EAAEd,CAAC,EAAE;IACR0mB,EAAE,GAAG1mB,CAAC,CAAC,CAAC,CAAC;IACT2mB,EAAE,GAAG3mB,CAAC,CAAC,CAAC,CAAC;IACT4mB,EAAE,GAAGC,EAAE,GAAG,IAAI;IACd,OAAO/lB,GAAG,CAAC6nB,MAAM,CAACjC,EAAE,EAAEC,EAAE,CAAC;GAC1B;EAEDlkB,CAACA,CAAC3B,GAAG,EAAEd,CAAC,EAAE;IACR0mB,EAAE,IAAI1mB,CAAC,CAAC,CAAC,CAAC;IACV2mB,EAAE,IAAI3mB,CAAC,CAAC,CAAC,CAAC;IACV4mB,EAAE,GAAGC,EAAE,GAAG,IAAI;IACd,OAAO/lB,GAAG,CAAC6nB,MAAM,CAACjC,EAAE,EAAEC,EAAE,CAAC;GAC1B;EAEDO,CAACA,CAACpmB,GAAG,EAAEd,CAAC,EAAE;IACR0mB,EAAE,GAAG1mB,CAAC,CAAC,CAAC,CAAC;IACT4mB,EAAE,GAAGC,EAAE,GAAG,IAAI;IACd,OAAO/lB,GAAG,CAAC6nB,MAAM,CAACjC,EAAE,EAAEC,EAAE,CAAC;GAC1B;EAEDQ,CAACA,CAACrmB,GAAG,EAAEd,CAAC,EAAE;IACR0mB,EAAE,IAAI1mB,CAAC,CAAC,CAAC,CAAC;IACV4mB,EAAE,GAAGC,EAAE,GAAG,IAAI;IACd,OAAO/lB,GAAG,CAAC6nB,MAAM,CAACjC,EAAE,EAAEC,EAAE,CAAC;GAC1B;EAEDpV,CAACA,CAACzQ,GAAG,EAAEd,CAAC,EAAE;IACR2mB,EAAE,GAAG3mB,CAAC,CAAC,CAAC,CAAC;IACT4mB,EAAE,GAAGC,EAAE,GAAG,IAAI;IACd,OAAO/lB,GAAG,CAAC6nB,MAAM,CAACjC,EAAE,EAAEC,EAAE,CAAC;GAC1B;EAEDrW,CAACA,CAACxP,GAAG,EAAEd,CAAC,EAAE;IACR2mB,EAAE,IAAI3mB,CAAC,CAAC,CAAC,CAAC;IACV4mB,EAAE,GAAGC,EAAE,GAAG,IAAI;IACd,OAAO/lB,GAAG,CAAC6nB,MAAM,CAACjC,EAAE,EAAEC,EAAE,CAAC;GAC1B;EAEDe,CAACA,CAAC5mB,GAAG,EAAE;IACLA,GAAG,CAAC8nB,SAAS,EAAE;IACflC,EAAE,GAAGI,EAAE;IACP,OAAQH,EAAE,GAAGI,EAAE;GAChB;EAEDY,CAACA,CAAC7mB,GAAG,EAAE;IACLA,GAAG,CAAC8nB,SAAS,EAAE;IACflC,EAAE,GAAGI,EAAE;IACP,OAAQH,EAAE,GAAGI,EAAE;;AAEnB,CAAC;AAED,MAAM2B,QAAQ,GAAG,UAAS5nB,GAAG,EAAEwM,CAAC,EAAEub,CAAC,EAAEC,MAAM,EAAE;EAC3C,MAAM,CAACC,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGP,MAAM;EAClD,MAAMQ,IAAI,GAAGC,aAAa,CAACH,EAAE,EAAEC,EAAE,EAAEN,EAAE,EAAEC,EAAE,EAAEE,KAAK,EAAEC,KAAK,EAAEF,GAAG,EAAE3b,CAAC,EAAEub,CAAC,CAAC;EAEnE,KAAK,IAAIW,GAAG,IAAIF,IAAI,EAAE;IACpB,MAAMG,GAAG,GAAGC,eAAe,CAAC,GAAGF,GAAG,CAAC;IACnC1oB,GAAG,CAAC0nB,aAAa,CAAC,GAAGiB,GAAG,CAAC;;AAE7B,CAAC;;AAED;AACA,MAAMF,aAAa,GAAG,UAASjc,CAAC,EAAEub,CAAC,EAAEE,EAAE,EAAEC,EAAE,EAAEE,KAAK,EAAEC,KAAK,EAAEQ,OAAO,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC1E,MAAMC,EAAE,GAAGH,OAAO,IAAItlB,IAAI,CAAC0lB,EAAE,GAAG,GAAG,CAAC;EACpC,MAAMC,MAAM,GAAG3lB,IAAI,CAAC4lB,GAAG,CAACH,EAAE,CAAC;EAC3B,MAAMI,MAAM,GAAG7lB,IAAI,CAAC8lB,GAAG,CAACL,EAAE,CAAC;EAC3Bf,EAAE,GAAG1kB,IAAI,CAAC+lB,GAAG,CAACrB,EAAE,CAAC;EACjBC,EAAE,GAAG3kB,IAAI,CAAC+lB,GAAG,CAACpB,EAAE,CAAC;EACjBpC,EAAE,GAAGsD,MAAM,IAAIN,EAAE,GAAGtc,CAAC,CAAC,GAAG,GAAG,GAAG0c,MAAM,IAAIH,EAAE,GAAGhB,CAAC,CAAC,GAAG,GAAG;EACtDhC,EAAE,GAAGqD,MAAM,IAAIL,EAAE,GAAGhB,CAAC,CAAC,GAAG,GAAG,GAAGmB,MAAM,IAAIJ,EAAE,GAAGtc,CAAC,CAAC,GAAG,GAAG;EACtD,IAAI+c,EAAE,GAAIzD,EAAE,GAAGA,EAAE,IAAKmC,EAAE,GAAGA,EAAE,CAAC,GAAIlC,EAAE,GAAGA,EAAE,IAAKmC,EAAE,GAAGA,EAAE,CAAC;EACtD,IAAIqB,EAAE,GAAG,CAAC,EAAE;IACVA,EAAE,GAAGhmB,IAAI,CAACimB,IAAI,CAACD,EAAE,CAAC;IAClBtB,EAAE,IAAIsB,EAAE;IACRrB,EAAE,IAAIqB,EAAE;;EAGV,MAAME,GAAG,GAAGL,MAAM,GAAGnB,EAAE;EACvB,MAAMyB,GAAG,GAAGR,MAAM,GAAGjB,EAAE;EACvB,MAAM0B,GAAG,GAAG,CAACT,MAAM,GAAGhB,EAAE;EACxB,MAAM0B,GAAG,GAAGR,MAAM,GAAGlB,EAAE;EACvB,MAAM2B,EAAE,GAAGJ,GAAG,GAAGX,EAAE,GAAGY,GAAG,GAAGX,EAAE;EAC9B,MAAMe,EAAE,GAAGH,GAAG,GAAGb,EAAE,GAAGc,GAAG,GAAGb,EAAE;EAC9B,MAAM7P,EAAE,GAAGuQ,GAAG,GAAGjd,CAAC,GAAGkd,GAAG,GAAG3B,CAAC;EAC5B,MAAM5O,EAAE,GAAGwQ,GAAG,GAAGnd,CAAC,GAAGod,GAAG,GAAG7B,CAAC;EAE5B,MAAMgC,CAAC,GAAG,CAAC7Q,EAAE,GAAG2Q,EAAE,KAAK3Q,EAAE,GAAG2Q,EAAE,CAAC,GAAG,CAAC1Q,EAAE,GAAG2Q,EAAE,KAAK3Q,EAAE,GAAG2Q,EAAE,CAAC;EACvD,IAAIE,UAAU,GAAG,CAAC,GAAGD,CAAC,GAAG,IAAI;EAC7B,IAAIC,UAAU,GAAG,CAAC,EAAE;IAClBA,UAAU,GAAG,CAAC;;EAEhB,IAAIC,OAAO,GAAG1mB,IAAI,CAACimB,IAAI,CAACQ,UAAU,CAAC;EACnC,IAAI3B,KAAK,KAAKD,KAAK,EAAE;IACnB6B,OAAO,GAAG,CAACA,OAAO;;EAGpB,MAAMC,EAAE,GAAG,GAAG,IAAIL,EAAE,GAAG3Q,EAAE,CAAC,GAAG+Q,OAAO,IAAI9Q,EAAE,GAAG2Q,EAAE,CAAC;EAChD,MAAMK,EAAE,GAAG,GAAG,IAAIL,EAAE,GAAG3Q,EAAE,CAAC,GAAG8Q,OAAO,IAAI/Q,EAAE,GAAG2Q,EAAE,CAAC;EAEhD,MAAMO,GAAG,GAAG7mB,IAAI,CAAC8mB,KAAK,CAACP,EAAE,GAAGK,EAAE,EAAEN,EAAE,GAAGK,EAAE,CAAC;EACxC,MAAMI,GAAG,GAAG/mB,IAAI,CAAC8mB,KAAK,CAAClR,EAAE,GAAGgR,EAAE,EAAEjR,EAAE,GAAGgR,EAAE,CAAC;EAExC,IAAIK,MAAM,GAAGD,GAAG,GAAGF,GAAG;EACtB,IAAIG,MAAM,GAAG,CAAC,IAAIlC,KAAK,KAAK,CAAC,EAAE;IAC7BkC,MAAM,IAAI,CAAC,GAAGhnB,IAAI,CAAC0lB,EAAE;GACtB,MAAM,IAAIsB,MAAM,GAAG,CAAC,IAAIlC,KAAK,KAAK,CAAC,EAAE;IACpCkC,MAAM,IAAI,CAAC,GAAGhnB,IAAI,CAAC0lB,EAAE;;EAGvB,MAAMuB,QAAQ,GAAGjnB,IAAI,CAACwQ,IAAI,CAACxQ,IAAI,CAAC+lB,GAAG,CAACiB,MAAM,IAAIhnB,IAAI,CAAC0lB,EAAE,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;EACtE,MAAMwB,MAAM,GAAG,EAAE;EAEjB,KAAK,IAAI7oB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4oB,QAAQ,EAAE5oB,CAAC,EAAE,EAAE;IACjC,MAAM8oB,GAAG,GAAGN,GAAG,GAAIxoB,CAAC,GAAG2oB,MAAM,GAAIC,QAAQ;IACzC,MAAMG,GAAG,GAAGP,GAAG,GAAI,CAACxoB,CAAC,GAAG,CAAC,IAAI2oB,MAAM,GAAIC,QAAQ;IAC/CC,MAAM,CAAC7oB,CAAC,CAAC,GAAG,CAACsoB,EAAE,EAAEC,EAAE,EAAEO,GAAG,EAAEC,GAAG,EAAE1C,EAAE,EAAEC,EAAE,EAAEgB,MAAM,EAAEE,MAAM,CAAC;;EAGxD,OAAOqB,MAAM;AACf,CAAC;AAED,MAAM7B,eAAe,GAAG,UAAShD,EAAE,EAAEC,EAAE,EAAEuE,GAAG,EAAEE,GAAG,EAAErC,EAAE,EAAEC,EAAE,EAAEgB,MAAM,EAAEE,MAAM,EAAE;EACzE,MAAMK,GAAG,GAAGL,MAAM,GAAGnB,EAAE;EACvB,MAAMyB,GAAG,GAAG,CAACR,MAAM,GAAGhB,EAAE;EACxB,MAAMyB,GAAG,GAAGT,MAAM,GAAGjB,EAAE;EACvB,MAAM2B,GAAG,GAAGR,MAAM,GAAGlB,EAAE;EAEvB,MAAM0C,OAAO,GAAG,GAAG,IAAIN,GAAG,GAAGF,GAAG,CAAC;EACjC,MAAMzD,CAAC,GACH,CAAC,GAAG,CAAC,GAAIpjB,IAAI,CAAC4lB,GAAG,CAACyB,OAAO,GAAG,GAAG,CAAC,GAAGrnB,IAAI,CAAC4lB,GAAG,CAACyB,OAAO,GAAG,GAAG,CAAC,GAC5DrnB,IAAI,CAAC4lB,GAAG,CAACyB,OAAO,CAAC;EACnB,MAAM1R,EAAE,GAAG0M,EAAE,GAAGriB,IAAI,CAAC8lB,GAAG,CAACe,GAAG,CAAC,GAAGzD,CAAC,GAAGpjB,IAAI,CAAC4lB,GAAG,CAACiB,GAAG,CAAC;EACjD,MAAMjR,EAAE,GAAG0M,EAAE,GAAGtiB,IAAI,CAAC4lB,GAAG,CAACiB,GAAG,CAAC,GAAGzD,CAAC,GAAGpjB,IAAI,CAAC8lB,GAAG,CAACe,GAAG,CAAC;EACjD,MAAMS,EAAE,GAAGjF,EAAE,GAAGriB,IAAI,CAAC8lB,GAAG,CAACiB,GAAG,CAAC;EAC7B,MAAMQ,EAAE,GAAGjF,EAAE,GAAGtiB,IAAI,CAAC4lB,GAAG,CAACmB,GAAG,CAAC;EAC7B,MAAMlR,EAAE,GAAGyR,EAAE,GAAGlE,CAAC,GAAGpjB,IAAI,CAAC4lB,GAAG,CAACmB,GAAG,CAAC;EACjC,MAAMjR,EAAE,GAAGyR,EAAE,GAAGnE,CAAC,GAAGpjB,IAAI,CAAC8lB,GAAG,CAACiB,GAAG,CAAC;EAEjC,OAAO,CACLb,GAAG,GAAGvQ,EAAE,GAAGwQ,GAAG,GAAGvQ,EAAE,EACnBwQ,GAAG,GAAGzQ,EAAE,GAAG0Q,GAAG,GAAGzQ,EAAE,EACnBsQ,GAAG,GAAGrQ,EAAE,GAAGsQ,GAAG,GAAGrQ,EAAE,EACnBsQ,GAAG,GAAGvQ,EAAE,GAAGwQ,GAAG,GAAGvQ,EAAE,EACnBoQ,GAAG,GAAGoB,EAAE,GAAGnB,GAAG,GAAGoB,EAAE,EACnBnB,GAAG,GAAGkB,EAAE,GAAGjB,GAAG,GAAGkB,EAAE,CACpB;AACH,CAAC;AAED,MAAMC,OAAO,CAAC;EACZ,OAAO1d,KAAKA,CAACrN,GAAG,EAAE+mB,IAAI,EAAE;IACtB,MAAMQ,QAAQ,GAAGT,KAAK,CAACC,IAAI,CAAC;IAC5B1Z,KAAK,CAACka,QAAQ,EAAEvnB,GAAG,CAAC;;AAExB;;AC1ZA,MAAM;UAAEqD;AAAO,CAAC,GAAG3D,SAAS;;AAE5B;AACA;AACA,MAAMsrB,KAAK,GAAG,GAAG,IAAI,CAACznB,IAAI,CAACimB,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC;AAChD,kBAAe;EACbyB,UAAUA,GAAG;IACX,IAAI,CAACpS,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,OAAQ,IAAI,CAACqS,SAAS,GAAG,EAAE;GAC5B;EAEDC,IAAIA,GAAG;IACL,IAAI,CAACD,SAAS,CAACzrB,IAAI,CAAC,IAAI,CAACoZ,IAAI,CAACvX,KAAK,EAAE,CAAC;;IAEtC,OAAO,IAAI,CAAC0X,UAAU,CAAC,GAAG,CAAC;GAC5B;EAEDoS,OAAOA,GAAG;IACR,IAAI,CAACvS,IAAI,GAAG,IAAI,CAACqS,SAAS,CAACG,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtD,OAAO,IAAI,CAACrS,UAAU,CAAC,GAAG,CAAC;GAC5B;EAED8O,SAASA,GAAG;IACV,OAAO,IAAI,CAAC9O,UAAU,CAAC,GAAG,CAAC;GAC5B;EAEDsS,SAASA,CAACC,CAAC,EAAE;IACX,OAAO,IAAI,CAACvS,UAAU,CAAE,GAAE3V,QAAM,CAACkoB,CAAC,CAAE,IAAG,CAAC;GACzC;EAEDC,WAAW,EAAE;IACXC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;GACT;EAEDC,OAAOA,CAACppB,CAAC,EAAE;IACT,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACzBA,CAAC,GAAG,IAAI,CAACgpB,WAAW,CAAChpB,CAAC,CAACgG,WAAW,EAAE,CAAC;;IAEvC,OAAO,IAAI,CAACwQ,UAAU,CAAE,GAAExW,CAAE,IAAG,CAAC;GACjC;EAEDqpB,YAAY,EAAE;IACZC,KAAK,EAAE,CAAC;IACRJ,KAAK,EAAE,CAAC;IACRK,KAAK,EAAE;GACR;EAEDC,QAAQA,CAAChY,CAAC,EAAE;IACV,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACzBA,CAAC,GAAG,IAAI,CAAC6X,YAAY,CAAC7X,CAAC,CAACxL,WAAW,EAAE,CAAC;;IAExC,OAAO,IAAI,CAACwQ,UAAU,CAAE,GAAEhF,CAAE,IAAG,CAAC;GACjC;EAEDiY,UAAUA,CAAC/V,CAAC,EAAE;IACZ,OAAO,IAAI,CAAC8C,UAAU,CAAE,GAAE3V,QAAM,CAAC6S,CAAC,CAAE,IAAG,CAAC;GACzC;EAEDgW,IAAIA,CAAC5sB,MAAM,EAAEf,OAAO,GAAG,EAAE,EAAE;IACzB,MAAM4tB,cAAc,GAAG7sB,MAAM;IAC7B,IAAI,CAAC+B,KAAK,CAAC4B,OAAO,CAAC3D,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,EAAEf,OAAO,CAACod,KAAK,IAAIrc,MAAM,CAAC;;IAG5C,MAAM8sB,KAAK,GAAG9sB,MAAM,CAAC+sB,KAAK,CAAC7f,CAAC,IAAI8f,MAAM,CAACC,QAAQ,CAAC/f,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;IAC5D,IAAI,CAAC4f,KAAK,EAAE;MACV,MAAM,IAAIhuB,KAAK,CACZ,QAAOouB,IAAI,CAACC,SAAS,CAACN,cAAc,CAAE,KAAIK,IAAI,CAACC,SAAS,CACvDluB,OACF,CAAE,0DACJ,CAAC;;IAGHe,MAAM,GAAGA,MAAM,CAACuB,GAAG,CAACwC,QAAM,CAAC,CAACvD,IAAI,CAAC,GAAG,CAAC;IACrC,OAAO,IAAI,CAACkZ,UAAU,CAAE,IAAG1Z,MAAO,KAAI+D,QAAM,CAAC9E,OAAO,CAACmuB,KAAK,IAAI,CAAC,CAAE,IAAG,CAAC;GACtE;EAEDC,MAAMA,GAAG;IACP,OAAO,IAAI,CAAC3T,UAAU,CAAC,QAAQ,CAAC;GACjC;EAEDyO,MAAMA,CAACjb,CAAC,EAAEub,CAAC,EAAE;IACX,OAAO,IAAI,CAAC/O,UAAU,CAAE,GAAE3V,QAAM,CAACmJ,CAAC,CAAE,IAAGnJ,QAAM,CAAC0kB,CAAC,CAAE,IAAG,CAAC;GACtD;EAEDF,MAAMA,CAACrb,CAAC,EAAEub,CAAC,EAAE;IACX,OAAO,IAAI,CAAC/O,UAAU,CAAE,GAAE3V,QAAM,CAACmJ,CAAC,CAAE,IAAGnJ,QAAM,CAAC0kB,CAAC,CAAE,IAAG,CAAC;GACtD;EAEDL,aAAaA,CAACkF,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEvgB,CAAC,EAAEub,CAAC,EAAE;IAC1C,OAAO,IAAI,CAAC/O,UAAU,CACnB,GAAE3V,QAAM,CAACupB,IAAI,CAAE,IAAGvpB,QAAM,CAACwpB,IAAI,CAAE,IAAGxpB,QAAM,CAACypB,IAAI,CAAE,IAAGzpB,QAAM,CAAC0pB,IAAI,CAAE,IAAG1pB,QAAM,CACvEmJ,CACF,CAAE,IAAGnJ,QAAM,CAAC0kB,CAAC,CAAE,IACjB,CAAC;GACF;EAEDJ,gBAAgBA,CAACqF,GAAG,EAAEC,GAAG,EAAEzgB,CAAC,EAAEub,CAAC,EAAE;IAC/B,OAAO,IAAI,CAAC/O,UAAU,CACnB,GAAE3V,QAAM,CAAC2pB,GAAG,CAAE,IAAG3pB,QAAM,CAAC4pB,GAAG,CAAE,IAAG5pB,QAAM,CAACmJ,CAAC,CAAE,IAAGnJ,QAAM,CAAC0kB,CAAC,CAAE,IAC1D,CAAC;GACF;EAEDmF,IAAIA,CAAC1gB,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE;IACf,OAAO,IAAI,CAACrN,UAAU,CACnB,GAAE3V,QAAM,CAACmJ,CAAC,CAAE,IAAGnJ,QAAM,CAAC0kB,CAAC,CAAE,IAAG1kB,QAAM,CAACkoB,CAAC,CAAE,IAAGloB,QAAM,CAACgjB,CAAC,CAAE,KACtD,CAAC;GACF;EAED8G,WAAWA,CAAC3gB,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE5W,CAAC,EAAE;IACzB,IAAIA,CAAC,IAAI,IAAI,EAAE;MACbA,CAAC,GAAG,CAAC;;IAEPA,CAAC,GAAGlM,IAAI,CAACmP,GAAG,CAACjD,CAAC,EAAE,GAAG,GAAG8b,CAAC,EAAE,GAAG,GAAGlF,CAAC,CAAC;;;IAGjC,MAAM7jB,CAAC,GAAGiN,CAAC,IAAI,GAAG,GAAGub,KAAK,CAAC;IAE3B,IAAI,CAACvD,MAAM,CAACjb,CAAC,GAAGiD,CAAC,EAAEsY,CAAC,CAAC;IACrB,IAAI,CAACF,MAAM,CAACrb,CAAC,GAAG+e,CAAC,GAAG9b,CAAC,EAAEsY,CAAC,CAAC;IACzB,IAAI,CAACL,aAAa,CAAClb,CAAC,GAAG+e,CAAC,GAAG/oB,CAAC,EAAEulB,CAAC,EAAEvb,CAAC,GAAG+e,CAAC,EAAExD,CAAC,GAAGvlB,CAAC,EAAEgK,CAAC,GAAG+e,CAAC,EAAExD,CAAC,GAAGtY,CAAC,CAAC;IAC5D,IAAI,CAACoY,MAAM,CAACrb,CAAC,GAAG+e,CAAC,EAAExD,CAAC,GAAG1B,CAAC,GAAG5W,CAAC,CAAC;IAC7B,IAAI,CAACiY,aAAa,CAAClb,CAAC,GAAG+e,CAAC,EAAExD,CAAC,GAAG1B,CAAC,GAAG7jB,CAAC,EAAEgK,CAAC,GAAG+e,CAAC,GAAG/oB,CAAC,EAAEulB,CAAC,GAAG1B,CAAC,EAAE7Z,CAAC,GAAG+e,CAAC,GAAG9b,CAAC,EAAEsY,CAAC,GAAG1B,CAAC,CAAC;IACxE,IAAI,CAACwB,MAAM,CAACrb,CAAC,GAAGiD,CAAC,EAAEsY,CAAC,GAAG1B,CAAC,CAAC;IACzB,IAAI,CAACqB,aAAa,CAAClb,CAAC,GAAGhK,CAAC,EAAEulB,CAAC,GAAG1B,CAAC,EAAE7Z,CAAC,EAAEub,CAAC,GAAG1B,CAAC,GAAG7jB,CAAC,EAAEgK,CAAC,EAAEub,CAAC,GAAG1B,CAAC,GAAG5W,CAAC,CAAC;IAC5D,IAAI,CAACoY,MAAM,CAACrb,CAAC,EAAEub,CAAC,GAAGtY,CAAC,CAAC;IACrB,IAAI,CAACiY,aAAa,CAAClb,CAAC,EAAEub,CAAC,GAAGvlB,CAAC,EAAEgK,CAAC,GAAGhK,CAAC,EAAEulB,CAAC,EAAEvb,CAAC,GAAGiD,CAAC,EAAEsY,CAAC,CAAC;IAChD,OAAO,IAAI,CAACD,SAAS,EAAE;GACxB;EAEDsF,OAAOA,CAAC5gB,CAAC,EAAEub,CAAC,EAAEpO,EAAE,EAAEC,EAAE,EAAE;;IAEpB,IAAIA,EAAE,IAAI,IAAI,EAAE;MACdA,EAAE,GAAGD,EAAE;;IAETnN,CAAC,IAAImN,EAAE;IACPoO,CAAC,IAAInO,EAAE;IACP,MAAMkP,EAAE,GAAGnP,EAAE,GAAGqR,KAAK;IACrB,MAAMjC,EAAE,GAAGnP,EAAE,GAAGoR,KAAK;IACrB,MAAMqC,EAAE,GAAG7gB,CAAC,GAAGmN,EAAE,GAAG,CAAC;IACrB,MAAM2T,EAAE,GAAGvF,CAAC,GAAGnO,EAAE,GAAG,CAAC;IACrB,MAAM2T,EAAE,GAAG/gB,CAAC,GAAGmN,EAAE;IACjB,MAAM6T,EAAE,GAAGzF,CAAC,GAAGnO,EAAE;IAEjB,IAAI,CAAC6N,MAAM,CAACjb,CAAC,EAAEghB,EAAE,CAAC;IAClB,IAAI,CAAC9F,aAAa,CAAClb,CAAC,EAAEghB,EAAE,GAAGzE,EAAE,EAAEwE,EAAE,GAAGzE,EAAE,EAAEf,CAAC,EAAEwF,EAAE,EAAExF,CAAC,CAAC;IACjD,IAAI,CAACL,aAAa,CAAC6F,EAAE,GAAGzE,EAAE,EAAEf,CAAC,EAAEsF,EAAE,EAAEG,EAAE,GAAGzE,EAAE,EAAEsE,EAAE,EAAEG,EAAE,CAAC;IACnD,IAAI,CAAC9F,aAAa,CAAC2F,EAAE,EAAEG,EAAE,GAAGzE,EAAE,EAAEwE,EAAE,GAAGzE,EAAE,EAAEwE,EAAE,EAAEC,EAAE,EAAED,EAAE,CAAC;IACpD,IAAI,CAAC5F,aAAa,CAAC6F,EAAE,GAAGzE,EAAE,EAAEwE,EAAE,EAAE9gB,CAAC,EAAEghB,EAAE,GAAGzE,EAAE,EAAEvc,CAAC,EAAEghB,EAAE,CAAC;IAClD,OAAO,IAAI,CAAC1F,SAAS,EAAE;GACxB;EAED2F,MAAMA,CAACjhB,CAAC,EAAEub,CAAC,EAAE2F,MAAM,EAAE;IACnB,OAAO,IAAI,CAACN,OAAO,CAAC5gB,CAAC,EAAEub,CAAC,EAAE2F,MAAM,CAAC;GAClC;EAEDC,GAAGA,CAACnhB,CAAC,EAAEub,CAAC,EAAE2F,MAAM,EAAEE,UAAU,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IACrD,IAAIA,aAAa,IAAI,IAAI,EAAE;MACzBA,aAAa,GAAG,KAAK;;IAEvB,MAAMC,MAAM,GAAG,GAAG,GAAGxqB,IAAI,CAAC0lB,EAAE;IAC5B,MAAM+E,OAAO,GAAG,GAAG,GAAGzqB,IAAI,CAAC0lB,EAAE;IAE7B,IAAIgF,QAAQ,GAAGJ,QAAQ,GAAGD,UAAU;IAEpC,IAAIrqB,IAAI,CAAC+lB,GAAG,CAAC2E,QAAQ,CAAC,GAAGF,MAAM,EAAE;;MAE/BE,QAAQ,GAAGF,MAAM;KAClB,MAAM,IAAIE,QAAQ,KAAK,CAAC,IAAIH,aAAa,KAAKG,QAAQ,GAAG,CAAC,EAAE;;MAE3D,MAAMC,GAAG,GAAGJ,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC;MAClCG,QAAQ,GAAGC,GAAG,GAAGH,MAAM,GAAGE,QAAQ;;IAGpC,MAAME,OAAO,GAAG5qB,IAAI,CAACwQ,IAAI,CAACxQ,IAAI,CAAC+lB,GAAG,CAAC2E,QAAQ,CAAC,GAAGD,OAAO,CAAC;IACvD,MAAMI,MAAM,GAAGH,QAAQ,GAAGE,OAAO;IACjC,MAAME,SAAS,GAAID,MAAM,GAAGJ,OAAO,GAAIhD,KAAK,GAAG0C,MAAM;IACrD,IAAIY,MAAM,GAAGV,UAAU;;;IAGvB,IAAIW,OAAO,GAAG,CAAChrB,IAAI,CAAC4lB,GAAG,CAACmF,MAAM,CAAC,GAAGD,SAAS;IAC3C,IAAIG,OAAO,GAAGjrB,IAAI,CAAC8lB,GAAG,CAACiF,MAAM,CAAC,GAAGD,SAAS;;;IAG1C,IAAII,EAAE,GAAGjiB,CAAC,GAAGjJ,IAAI,CAAC8lB,GAAG,CAACiF,MAAM,CAAC,GAAGZ,MAAM;IACtC,IAAIgB,EAAE,GAAG3G,CAAC,GAAGxkB,IAAI,CAAC4lB,GAAG,CAACmF,MAAM,CAAC,GAAGZ,MAAM;;;IAGtC,IAAI,CAACjG,MAAM,CAACgH,EAAE,EAAEC,EAAE,CAAC;IAEnB,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGR,OAAO,EAAEQ,MAAM,EAAE,EAAE;;MAE/C,MAAM/B,IAAI,GAAG6B,EAAE,GAAGF,OAAO;MACzB,MAAM1B,IAAI,GAAG6B,EAAE,GAAGF,OAAO;;;MAGzBF,MAAM,IAAIF,MAAM;;;MAGhBK,EAAE,GAAGjiB,CAAC,GAAGjJ,IAAI,CAAC8lB,GAAG,CAACiF,MAAM,CAAC,GAAGZ,MAAM;MAClCgB,EAAE,GAAG3G,CAAC,GAAGxkB,IAAI,CAAC4lB,GAAG,CAACmF,MAAM,CAAC,GAAGZ,MAAM;;;MAGlCa,OAAO,GAAG,CAAChrB,IAAI,CAAC4lB,GAAG,CAACmF,MAAM,CAAC,GAAGD,SAAS;MACvCG,OAAO,GAAGjrB,IAAI,CAAC8lB,GAAG,CAACiF,MAAM,CAAC,GAAGD,SAAS;;;MAGtC,MAAMvB,IAAI,GAAG2B,EAAE,GAAGF,OAAO;MACzB,MAAMxB,IAAI,GAAG2B,EAAE,GAAGF,OAAO;;;MAGzB,IAAI,CAAC9G,aAAa,CAACkF,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE0B,EAAE,EAAEC,EAAE,CAAC;;IAGpD,OAAO,IAAI;GACZ;EAEDE,OAAOA,CAAC,GAAGC,MAAM,EAAE;IACjB,IAAI,CAACpH,MAAM,CAAC,IAAIoH,MAAM,CAACC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;IACtC,KAAK,IAAIC,KAAK,IAAIF,MAAM,EAAE;MACxB,IAAI,CAAChH,MAAM,CAAC,IAAIkH,KAAK,IAAI,EAAE,CAAC,CAAC;;IAE/B,OAAO,IAAI,CAACjH,SAAS,EAAE;GACxB;EAEDf,IAAIA,CAACA,IAAI,EAAE;IACTgE,OAAO,CAAC1d,KAAK,CAAC,IAAI,EAAE0Z,IAAI,CAAC;IACzB,OAAO,IAAI;GACZ;EAEDiI,YAAYA,CAACC,IAAI,EAAE;IACjB,IAAI,WAAW,CAACC,IAAI,CAACD,IAAI,CAAC,EAAE;MAC1B,OAAO,GAAG;;IAGZ,OAAO,EAAE;GACV;EAEDE,IAAIA,CAACzkB,KAAK,EAAEukB,IAAI,EAAE;IAChB,IAAI,yBAAyB,CAACC,IAAI,CAACxkB,KAAK,CAAC,EAAE;MACzCukB,IAAI,GAAGvkB,KAAK;MACZA,KAAK,GAAG,IAAI;;IAGd,IAAIA,KAAK,EAAE;MACT,IAAI,CAACkR,SAAS,CAAClR,KAAK,CAAC;;IAEvB,OAAO,IAAI,CAACsO,UAAU,CAAE,IAAG,IAAI,CAACgW,YAAY,CAACC,IAAI,CAAE,EAAC,CAAC;GACtD;EAED3W,MAAMA,CAAC5N,KAAK,EAAE;IACZ,IAAIA,KAAK,EAAE;MACT,IAAI,CAACsR,WAAW,CAACtR,KAAK,CAAC;;IAEzB,OAAO,IAAI,CAACsO,UAAU,CAAC,GAAG,CAAC;GAC5B;EAEDoW,aAAaA,CAACxT,SAAS,EAAEI,WAAW,EAAEiT,IAAI,EAAE;IAC1C,IAAIjT,WAAW,IAAI,IAAI,EAAE;MACvBA,WAAW,GAAGJ,SAAS;;IAEzB,MAAMyT,UAAU,GAAG,yBAAyB;IAC5C,IAAIA,UAAU,CAACH,IAAI,CAACtT,SAAS,CAAC,EAAE;MAC9BqT,IAAI,GAAGrT,SAAS;MAChBA,SAAS,GAAG,IAAI;;IAGlB,IAAIyT,UAAU,CAACH,IAAI,CAAClT,WAAW,CAAC,EAAE;MAChCiT,IAAI,GAAGjT,WAAW;MAClBA,WAAW,GAAGJ,SAAS;;IAGzB,IAAIA,SAAS,EAAE;MACb,IAAI,CAACA,SAAS,CAACA,SAAS,CAAC;MACzB,IAAI,CAACI,WAAW,CAACA,WAAW,CAAC;;IAG/B,OAAO,IAAI,CAAChD,UAAU,CAAE,IAAG,IAAI,CAACgW,YAAY,CAACC,IAAI,CAAE,EAAC,CAAC;GACtD;EAEDK,IAAIA,CAACL,IAAI,EAAE;IACT,OAAO,IAAI,CAACjW,UAAU,CAAE,IAAG,IAAI,CAACgW,YAAY,CAACC,IAAI,CAAE,IAAG,CAAC;GACxD;EAED9Z,SAASA,CAACQ,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAE;;IAEpC,IAAIL,GAAG,KAAK,CAAC,IAAIC,GAAG,KAAK,CAAC,IAAIC,GAAG,KAAK,CAAC,IAAIC,GAAG,KAAK,CAAC,IAAIC,EAAE,KAAK,CAAC,IAAIC,EAAE,KAAK,CAAC,EAAE;;MAE5E,OAAO,IAAI;;IAEb,MAAME,CAAC,GAAG,IAAI,CAAC2C,IAAI;IACnB,MAAM,CAACN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAG1C,CAAC;IAClCA,CAAC,CAAC,CAAC,CAAC,GAAGqC,EAAE,GAAG5C,GAAG,GAAG8C,EAAE,GAAG7C,GAAG;IAC1BM,CAAC,CAAC,CAAC,CAAC,GAAGsC,EAAE,GAAG7C,GAAG,GAAG+C,EAAE,GAAG9C,GAAG;IAC1BM,CAAC,CAAC,CAAC,CAAC,GAAGqC,EAAE,GAAG1C,GAAG,GAAG4C,EAAE,GAAG3C,GAAG;IAC1BI,CAAC,CAAC,CAAC,CAAC,GAAGsC,EAAE,GAAG3C,GAAG,GAAG6C,EAAE,GAAG5C,GAAG;IAC1BI,CAAC,CAAC,CAAC,CAAC,GAAGqC,EAAE,GAAGxC,EAAE,GAAG0C,EAAE,GAAGzC,EAAE,GAAG2C,EAAE;IAC7BzC,CAAC,CAAC,CAAC,CAAC,GAAGsC,EAAE,GAAGzC,EAAE,GAAG2C,EAAE,GAAG1C,EAAE,GAAG4C,EAAE;IAE7B,MAAMpY,MAAM,GAAG,CAACmV,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAACnV,GAAG,CAAC2O,CAAC,IAAInM,QAAM,CAACmM,CAAC,CAAC,CAAC,CAAC1P,IAAI,CAAC,GAAG,CAAC;IACzE,OAAO,IAAI,CAACkZ,UAAU,CAAE,GAAExY,MAAO,KAAI,CAAC;GACvC;EAED+uB,SAASA,CAAC/iB,CAAC,EAAEub,CAAC,EAAE;IACd,OAAO,IAAI,CAAC5S,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE3I,CAAC,EAAEub,CAAC,CAAC;GACxC;EAEDyH,MAAMA,CAACC,KAAK,EAAElxB,OAAO,GAAG,EAAE,EAAE;IAC1B,IAAIwpB,CAAC;IACL,MAAM2H,GAAG,GAAID,KAAK,GAAGlsB,IAAI,CAAC0lB,EAAE,GAAI,GAAG;IACnC,MAAMI,GAAG,GAAG9lB,IAAI,CAAC8lB,GAAG,CAACqG,GAAG,CAAC;IACzB,MAAMvG,GAAG,GAAG5lB,IAAI,CAAC4lB,GAAG,CAACuG,GAAG,CAAC;IACzB,IAAIljB,CAAC,GAAIub,CAAC,GAAG,CAAE;IAEf,IAAIxpB,OAAO,CAACoxB,MAAM,IAAI,IAAI,EAAE;MAC1B,CAACnjB,CAAC,EAAEub,CAAC,CAAC,GAAGxpB,OAAO,CAACoxB,MAAM;MACvB,MAAMzW,EAAE,GAAG1M,CAAC,GAAG6c,GAAG,GAAGtB,CAAC,GAAGoB,GAAG;MAC5B,MAAMhQ,EAAE,GAAG3M,CAAC,GAAG2c,GAAG,GAAGpB,CAAC,GAAGsB,GAAG;MAC5B7c,CAAC,IAAI0M,EAAE;MACP6O,CAAC,IAAI5O,EAAE;;IAGT,OAAO,IAAI,CAAChE,SAAS,CAACkU,GAAG,EAAEF,GAAG,EAAE,CAACA,GAAG,EAAEE,GAAG,EAAE7c,CAAC,EAAEub,CAAC,CAAC;GACjD;EAED6H,KAAKA,CAACC,OAAO,EAAEC,OAAO,EAAEvxB,OAAO,GAAG,EAAE,EAAE;IACpC,IAAIwpB,CAAC;IACL,IAAI+H,OAAO,IAAI,IAAI,EAAE;MACnBA,OAAO,GAAGD,OAAO;;IAEnB,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;MAC/BvxB,OAAO,GAAGuxB,OAAO;MACjBA,OAAO,GAAGD,OAAO;;IAGnB,IAAIrjB,CAAC,GAAIub,CAAC,GAAG,CAAE;IACf,IAAIxpB,OAAO,CAACoxB,MAAM,IAAI,IAAI,EAAE;MAC1B,CAACnjB,CAAC,EAAEub,CAAC,CAAC,GAAGxpB,OAAO,CAACoxB,MAAM;MACvBnjB,CAAC,IAAIqjB,OAAO,GAAGrjB,CAAC;MAChBub,CAAC,IAAI+H,OAAO,GAAG/H,CAAC;;IAGlB,OAAO,IAAI,CAAC5S,SAAS,CAAC0a,OAAO,EAAE,CAAC,EAAE,CAAC,EAAEC,OAAO,EAAEtjB,CAAC,EAAEub,CAAC,CAAC;;AAEvD,CAAC;;AC3VD,MAAMgI,YAAY,GAAG;EACnB,GAAG,EAAE,GAAG;EACR,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,GAAG,EAAE,GAAG;EACR,IAAI,EAAE,GAAG;EACT,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE,GAAG;EACR,GAAG,EAAE;AACP,CAAC;AAED,MAAMC,UAAU,GAAI;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC;AAEd,MAAMC,OAAO,CAAC;EACZ,OAAOC,IAAIA,CAACC,QAAQ,EAAE;IACpB,OAAO,IAAIF,OAAO,CAACG,EAAE,CAACC,YAAY,CAACF,QAAQ,EAAE,MAAM,CAAC,CAAC;;EAGvD9xB,WAAWA,CAACiyB,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,SAAS,GAAG,EAAE;IAEnB,IAAI,CAAC7J,KAAK,EAAE;;IAEZ,IAAI,CAAC8J,UAAU,GAAG,IAAIvvB,KAAK,CAAC,GAAG,CAAC;IAChC,KAAK,IAAIwvB,IAAI,GAAG,CAAC,EAAEA,IAAI,IAAI,GAAG,EAAEA,IAAI,EAAE,EAAE;MACtC,IAAI,CAACD,UAAU,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,WAAW,CAACT,UAAU,CAACa,IAAI,CAAC,CAAC;;IAG5D,IAAI,CAACtU,IAAI,GAAG,IAAI,CAACiU,UAAU,CAAC,UAAU,CAAC,CAACP,KAAK,CAAC,KAAK,CAAC,CAACpvB,GAAG,CAACsC,CAAC,IAAI,CAACA,CAAC,CAAC;IACjE,IAAI,CAAC2tB,QAAQ,GAAG,EAAE,IAAI,CAACN,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACnD,IAAI,CAACO,SAAS,GAAG,EAAE,IAAI,CAACP,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACrD,IAAI,CAACQ,OAAO,GAAG,EAAE,IAAI,CAACR,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACjD,IAAI,CAACS,SAAS,GAAG,EAAE,IAAI,CAACT,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACrD,IAAI,CAACU,OAAO,GACV,IAAI,CAAC3U,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAACuU,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAC;;EAGlEjK,KAAKA,GAAG;IACN,IAAIqK,OAAO,GAAG,EAAE;IAChB,KAAK,IAAIC,IAAI,IAAI,IAAI,CAACb,QAAQ,CAACN,KAAK,CAAC,IAAI,CAAC,EAAE;MAC1C,IAAIoB,KAAK;MACT,IAAInyB,CAAC;MACL,IAAKmyB,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,EAAG;QACvCF,OAAO,GAAGE,KAAK,CAAC,CAAC,CAAC;QAClB;OACD,MAAM,IAAKA,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,WAAW,CAAC,EAAG;QAC5CF,OAAO,GAAG,EAAE;QACZ;;MAGF,QAAQA,OAAO;QACb,KAAK,aAAa;UAChBE,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,eAAe,CAAC;UACnC,IAAI1yB,GAAG,GAAG0yB,KAAK,CAAC,CAAC,CAAC;UAClB,IAAIvwB,KAAK,GAAGuwB,KAAK,CAAC,CAAC,CAAC;UAEpB,IAAKnyB,CAAC,GAAG,IAAI,CAACsxB,UAAU,CAAC7xB,GAAG,CAAC,EAAG;YAC9B,IAAI,CAAC0C,KAAK,CAAC4B,OAAO,CAAC/D,CAAC,CAAC,EAAE;cACrBA,CAAC,GAAG,IAAI,CAACsxB,UAAU,CAAC7xB,GAAG,CAAC,GAAG,CAACO,CAAC,CAAC;;YAEhCA,CAAC,CAACO,IAAI,CAACqB,KAAK,CAAC;WACd,MAAM;YACL,IAAI,CAAC0vB,UAAU,CAAC7xB,GAAG,CAAC,GAAGmC,KAAK;;UAE9B;QAEF,KAAK,aAAa;UAChB,IAAI,CAAC,QAAQ,CAACouB,IAAI,CAACkC,IAAI,CAAC,EAAE;YACxB;;UAEF,IAAInxB,IAAI,GAAGmxB,IAAI,CAACC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;UAC9C,IAAI,CAACZ,WAAW,CAACxwB,IAAI,CAAC,GAAG,CAACmxB,IAAI,CAACC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;UAC3D;QAEF,KAAK,WAAW;UACdA,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,sCAAsC,CAAC;UAC1D,IAAIA,KAAK,EAAE;YACT,IAAI,CAACV,SAAS,CAACU,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG/V,QAAQ,CAAC+V,KAAK,CAAC,CAAC,CAAC,CAAC;;UAEjE;;;;EAKRC,UAAUA,CAACC,IAAI,EAAE;IACf,MAAMC,GAAG,GAAG,EAAE;IACd,KAAK,IAAI5vB,CAAC,GAAG,CAAC,EAAE6vB,GAAG,GAAGF,IAAI,CAACjyB,MAAM,EAAEsC,CAAC,GAAG6vB,GAAG,EAAE7vB,CAAC,EAAE,EAAE;MAC/C,IAAIivB,IAAI,GAAGU,IAAI,CAACrvB,UAAU,CAACN,CAAC,CAAC;MAC7BivB,IAAI,GAAGd,YAAY,CAACc,IAAI,CAAC,IAAIA,IAAI;MACjCW,GAAG,CAAC/xB,IAAI,CAACoxB,IAAI,CAAC1yB,QAAQ,CAAC,EAAE,CAAC,CAAC;;IAG7B,OAAOqzB,GAAG;;EAGZE,eAAeA,CAAC1vB,MAAM,EAAE;IACtB,MAAM2vB,MAAM,GAAG,EAAE;IAEjB,KAAK,IAAI/vB,CAAC,GAAG,CAAC,EAAE6vB,GAAG,GAAGzvB,MAAM,CAAC1C,MAAM,EAAEsC,CAAC,GAAG6vB,GAAG,EAAE7vB,CAAC,EAAE,EAAE;MACjD,MAAMgwB,QAAQ,GAAG5vB,MAAM,CAACE,UAAU,CAACN,CAAC,CAAC;MACrC+vB,MAAM,CAAClyB,IAAI,CAAC,IAAI,CAACoyB,gBAAgB,CAACD,QAAQ,CAAC,CAAC;;IAG9C,OAAOD,MAAM;;EAGfE,gBAAgBA,CAACtmB,SAAS,EAAE;IAC1B,OAAOykB,UAAU,CAACD,YAAY,CAACxkB,SAAS,CAAC,IAAIA,SAAS,CAAC,IAAI,SAAS;;EAGtEumB,YAAYA,CAACC,KAAK,EAAE;IAClB,OAAO,IAAI,CAACtB,WAAW,CAACsB,KAAK,CAAC,IAAI,CAAC;;EAGrCC,WAAWA,CAAChtB,IAAI,EAAEE,KAAK,EAAE;IACvB,OAAO,IAAI,CAACyrB,SAAS,CAAC3rB,IAAI,GAAG,IAAI,GAAGE,KAAK,CAAC,IAAI,CAAC;;EAGjD+sB,iBAAiBA,CAACN,MAAM,EAAE;IACxB,MAAMO,QAAQ,GAAG,EAAE;IAEnB,KAAK,IAAIzd,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGkd,MAAM,CAACryB,MAAM,EAAEmV,KAAK,EAAE,EAAE;MAClD,MAAMzP,IAAI,GAAG2sB,MAAM,CAACld,KAAK,CAAC;MAC1B,MAAMvP,KAAK,GAAGysB,MAAM,CAACld,KAAK,GAAG,CAAC,CAAC;MAC/Byd,QAAQ,CAACzyB,IAAI,CAAC,IAAI,CAACqyB,YAAY,CAAC9sB,IAAI,CAAC,GAAG,IAAI,CAACgtB,WAAW,CAAChtB,IAAI,EAAEE,KAAK,CAAC,CAAC;;IAGxE,OAAOgtB,QAAQ;;AAEnB;;AClOA,MAAMC,OAAO,CAAC;EACZ7zB,WAAWA,GAAG;EAEdiY,MAAMA,GAAG;IACP,MAAM,IAAInY,KAAK,CAAC,mCAAmC,CAAC;;EAGtDg0B,aAAaA,GAAG;IACd,MAAM,IAAIh0B,KAAK,CAAC,mCAAmC,CAAC;;EAGtDqC,GAAGA,GAAG;IACJ,OAAO,IAAI,CAACqI,UAAU,IAAI,IAAI,GAC1B,IAAI,CAACA,UAAU,GACd,IAAI,CAACA,UAAU,GAAG,IAAI,CAACpF,QAAQ,CAACjD,GAAG,EAAG;;EAG7C2D,QAAQA,GAAG;IACT,IAAI,IAAI,CAAC8Q,QAAQ,IAAI,IAAI,CAACpM,UAAU,IAAI,IAAI,EAAE;MAC5C;;IAGF,IAAI,CAACmN,KAAK,EAAE;IACZ,OAAQ,IAAI,CAACf,QAAQ,GAAG,IAAI;;EAG9Be,KAAKA,GAAG;IACN,MAAM,IAAI7X,KAAK,CAAC,mCAAmC,CAAC;;EAGtDi0B,UAAUA,CAAClqB,IAAI,EAAEmqB,UAAU,EAAE;IAC3B,IAAIA,UAAU,IAAI,IAAI,EAAE;MACtBA,UAAU,GAAG,KAAK;;IAEpB,MAAMC,GAAG,GAAGD,UAAU,GAAG,IAAI,CAACpB,OAAO,GAAG,CAAC;IACzC,OAAQ,CAAC,IAAI,CAACJ,QAAQ,GAAGyB,GAAG,GAAG,IAAI,CAACxB,SAAS,IAAI,IAAI,GAAI5oB,IAAI;;AAEjE;;ACjCA;AACA,MAAMqqB,cAAc,GAAG;EACrBC,OAAOA,GAAG;IACR,OAAOpC,EAAE,CAACC,YAAY,CAACoC,SAAS,GAAG,mBAAmB,EAAE,MAAM,CAAC;GAChE;EACD,cAAcC,GAAG;IACf,OAAOtC,EAAE,CAACC,YAAY,CAACoC,SAAS,GAAG,wBAAwB,EAAE,MAAM,CAAC;GACrE;EACD,iBAAiBE,GAAG;IAClB,OAAOvC,EAAE,CAACC,YAAY,CAACoC,SAAS,GAAG,2BAA2B,EAAE,MAAM,CAAC;GACxE;EACD,qBAAqBG,GAAG;IACtB,OAAOxC,EAAE,CAACC,YAAY,CAACoC,SAAS,GAAG,+BAA+B,EAAE,MAAM,CAAC;GAC5E;EACDI,SAASA,GAAG;IACV,OAAOzC,EAAE,CAACC,YAAY,CAACoC,SAAS,GAAG,qBAAqB,EAAE,MAAM,CAAC;GAClE;EACD,gBAAgBK,GAAG;IACjB,OAAO1C,EAAE,CAACC,YAAY,CAACoC,SAAS,GAAG,0BAA0B,EAAE,MAAM,CAAC;GACvE;EACD,mBAAmBM,GAAG;IACpB,OAAO3C,EAAE,CAACC,YAAY,CAACoC,SAAS,GAAG,6BAA6B,EAAE,MAAM,CAAC;GAC1E;EACD,uBAAuBO,GAAG;IACxB,OAAO5C,EAAE,CAACC,YAAY,CACpBoC,SAAS,GAAG,iCAAiC,EAC7C,MACF,CAAC;GACF;EACD,aAAaQ,GAAG;IACd,OAAO7C,EAAE,CAACC,YAAY,CAACoC,SAAS,GAAG,uBAAuB,EAAE,MAAM,CAAC;GACpE;EACD,YAAYS,GAAG;IACb,OAAO9C,EAAE,CAACC,YAAY,CAACoC,SAAS,GAAG,sBAAsB,EAAE,MAAM,CAAC;GACnE;EACD,cAAcU,GAAG;IACf,OAAO/C,EAAE,CAACC,YAAY,CAACoC,SAAS,GAAG,wBAAwB,EAAE,MAAM,CAAC;GACrE;EACD,kBAAkBW,GAAG;IACnB,OAAOhD,EAAE,CAACC,YAAY,CAACoC,SAAS,GAAG,4BAA4B,EAAE,MAAM,CAAC;GACzE;EACDY,MAAMA,GAAG;IACP,OAAOjD,EAAE,CAACC,YAAY,CAACoC,SAAS,GAAG,kBAAkB,EAAE,MAAM,CAAC;GAC/D;EACDa,YAAYA,GAAG;IACb,OAAOlD,EAAE,CAACC,YAAY,CAACoC,SAAS,GAAG,wBAAwB,EAAE,MAAM,CAAC;;AAExE,CAAC;AAED,MAAMc,YAAY,SAASrB,OAAO,CAAC;EACjC7zB,WAAWA,CAACoF,QAAQ,EAAEzD,IAAI,EAAEK,EAAE,EAAE;IAC9B,KAAK,EAAE;IACP,IAAI,CAACoD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACzD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACK,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACmzB,IAAI,GAAG,IAAIvD,OAAO,CAACsC,cAAc,CAAC,IAAI,CAACvyB,IAAI,CAAC,EAAE,CAAC;IACpD,CAAC;MACC6wB,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBxU,IAAI,EAAE,IAAI,CAACA,IAAI;MACf2U,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBF,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,SAAS,EAAE,IAAI,CAACA;KACjB,GAAG,IAAI,CAACwC,IAAI;;EAGfxd,KAAKA,GAAG;IACN,IAAI,CAACnN,UAAU,CAACnF,IAAI,GAAG;MACrBoF,IAAI,EAAE,MAAM;MACZ2qB,QAAQ,EAAE,IAAI,CAACzzB,IAAI;MACnBqX,OAAO,EAAE,OAAO;MAChBqc,QAAQ,EAAE;KACX;IAED,OAAO,IAAI,CAAC7qB,UAAU,CAAC5H,GAAG,EAAE;;EAG9BqV,MAAMA,CAACgb,IAAI,EAAE;IACX,MAAMqC,OAAO,GAAG,IAAI,CAACH,IAAI,CAACnC,UAAU,CAACC,IAAI,CAAC;IAC1C,MAAMI,MAAM,GAAG,IAAI,CAAC8B,IAAI,CAAC/B,eAAe,CAAE,GAAEH,IAAK,EAAC,CAAC;IACnD,MAAMW,QAAQ,GAAG,IAAI,CAACuB,IAAI,CAACxB,iBAAiB,CAACN,MAAM,CAAC;IACpD,MAAMkC,SAAS,GAAG,EAAE;IACpB,KAAK,IAAIjyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+vB,MAAM,CAACryB,MAAM,EAAEsC,CAAC,EAAE,EAAE;MACtC,MAAMmwB,KAAK,GAAGJ,MAAM,CAAC/vB,CAAC,CAAC;MACvBiyB,SAAS,CAACp0B,IAAI,CAAC;QACbq0B,QAAQ,EAAE5B,QAAQ,CAACtwB,CAAC,CAAC;QACrBmyB,QAAQ,EAAE,CAAC;QACXC,OAAO,EAAE,CAAC;QACVC,OAAO,EAAE,CAAC;QACVC,YAAY,EAAE,IAAI,CAACT,IAAI,CAAC3B,YAAY,CAACC,KAAK;OAC3C,CAAC;;IAGJ,OAAO,CAAC6B,OAAO,EAAEC,SAAS,CAAC;;EAG7BzB,aAAaA,CAACpwB,MAAM,EAAEmG,IAAI,EAAE;IAC1B,MAAMwpB,MAAM,GAAG,IAAI,CAAC8B,IAAI,CAAC/B,eAAe,CAAE,GAAE1vB,MAAO,EAAC,CAAC;IACrD,MAAMkwB,QAAQ,GAAG,IAAI,CAACuB,IAAI,CAACxB,iBAAiB,CAACN,MAAM,CAAC;IAEpD,IAAIlpB,KAAK,GAAG,CAAC;IACb,KAAK,IAAI0rB,OAAO,IAAIjC,QAAQ,EAAE;MAC5BzpB,KAAK,IAAI0rB,OAAO;;IAGlB,MAAMvE,KAAK,GAAGznB,IAAI,GAAG,IAAI;IACzB,OAAOM,KAAK,GAAGmnB,KAAK;;EAGtB,OAAOwE,cAAcA,CAACn0B,IAAI,EAAE;IAC1B,OAAOA,IAAI,IAAIuyB,cAAc;;AAEjC;;AClHA,MAAM6B,KAAK,GAAG,UAASC,GAAG,EAAE;EAC1B,OAAQ,OAAMA,GAAG,CAACn2B,QAAQ,CAAC,EAAE,CAAE,EAAC,CAACmD,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5C,CAAC;AAED,MAAMizB,YAAY,SAASpC,OAAO,CAAC;EACjC7zB,WAAWA,CAACoF,QAAQ,EAAE+vB,IAAI,EAAEnzB,EAAE,EAAE;IAC9B,KAAK,EAAE;IACP,IAAI,CAACoD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC+vB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACnzB,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACk0B,MAAM,GAAG,IAAI,CAACf,IAAI,CAACgB,YAAY,EAAE;IACtC,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,IAAI,CAACC,MAAM,GAAG,CAAC,IAAI,CAAClB,IAAI,CAACmB,QAAQ,CAAC,CAAC,CAAC,CAACV,YAAY,CAAC;IAElD,IAAI,CAACj0B,IAAI,GAAG,IAAI,CAACwzB,IAAI,CAACoB,cAAc;IACpC,IAAI,CAACjF,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC6D,IAAI,CAACqB,UAAU;IACxC,IAAI,CAAChE,QAAQ,GAAG,IAAI,CAAC2C,IAAI,CAACsB,MAAM,GAAG,IAAI,CAACnF,KAAK;IAC7C,IAAI,CAACmB,SAAS,GAAG,IAAI,CAAC0C,IAAI,CAACuB,OAAO,GAAG,IAAI,CAACpF,KAAK;IAC/C,IAAI,CAACoB,OAAO,GAAG,IAAI,CAACyC,IAAI,CAACzC,OAAO,GAAG,IAAI,CAACpB,KAAK;IAC7C,IAAI,CAACqB,SAAS,GAAG,IAAI,CAACwC,IAAI,CAACxC,SAAS,GAAG,IAAI,CAACrB,KAAK;IACjD,IAAI,CAACsB,OAAO,GAAG,IAAI,CAACuC,IAAI,CAACvC,OAAO,GAAG,IAAI,CAACtB,KAAK;IAC7C,IAAI,CAACrT,IAAI,GAAG,IAAI,CAACkX,IAAI,CAAClX,IAAI;IAE1B,IAAI7Y,QAAQ,CAACnF,OAAO,CAAC02B,eAAe,KAAK,KAAK,EAAE;MAC9C,IAAI,CAACC,WAAW,GAAGn2B,MAAM,CAACgQ,MAAM,CAAC,IAAI,CAAC;;;EAI1ComB,SAASA,CAAC5D,IAAI,EAAE6D,QAAQ,EAAE;IACxB,MAAMC,GAAG,GAAG,IAAI,CAAC5B,IAAI,CAACrrB,MAAM,CAACmpB,IAAI,EAAE6D,QAAQ,CAAC;;;IAG5C,KAAK,IAAIxzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyzB,GAAG,CAACxB,SAAS,CAACv0B,MAAM,EAAEsC,CAAC,EAAE,EAAE;MAC7C,MAAM0zB,QAAQ,GAAGD,GAAG,CAACxB,SAAS,CAACjyB,CAAC,CAAC;MACjC,KAAK,IAAIjD,GAAG,IAAI22B,QAAQ,EAAE;QACxBA,QAAQ,CAAC32B,GAAG,CAAC,IAAI,IAAI,CAACixB,KAAK;;MAG7B0F,QAAQ,CAACpB,YAAY,GAAGmB,GAAG,CAAC1D,MAAM,CAAC/vB,CAAC,CAAC,CAACsyB,YAAY,GAAG,IAAI,CAACtE,KAAK;;IAGjE,OAAOyF,GAAG;;EAGZE,YAAYA,CAAChE,IAAI,EAAE;IACjB,IAAI,CAAC,IAAI,CAAC2D,WAAW,EAAE;MACrB,OAAO,IAAI,CAACC,SAAS,CAAC5D,IAAI,CAAC;;IAE7B,IAAIiE,MAAM;IACV,IAAKA,MAAM,GAAG,IAAI,CAACN,WAAW,CAAC3D,IAAI,CAAC,EAAG;MACrC,OAAOiE,MAAM;;IAGf,MAAMH,GAAG,GAAG,IAAI,CAACF,SAAS,CAAC5D,IAAI,CAAC;IAChC,IAAI,CAAC2D,WAAW,CAAC3D,IAAI,CAAC,GAAG8D,GAAG;IAC5B,OAAOA,GAAG;;EAGZjtB,MAAMA,CAACmpB,IAAI,EAAE6D,QAAQ,EAAEK,SAAS,EAAE;;IAEhC,IAAIL,QAAQ,EAAE;MACZ,OAAO,IAAI,CAACD,SAAS,CAAC5D,IAAI,EAAE6D,QAAQ,CAAC;;IAGvC,IAAIzD,MAAM,GAAG8D,SAAS,GAAG,IAAI,GAAG,EAAE;IAClC,IAAI5B,SAAS,GAAG4B,SAAS,GAAG,IAAI,GAAG,EAAE;IACrC,IAAIvB,YAAY,GAAG,CAAC;;;;IAIpB,IAAI10B,IAAI,GAAG,CAAC;IACZ,IAAIiV,KAAK,GAAG,CAAC;IACb,OAAOA,KAAK,IAAI8c,IAAI,CAACjyB,MAAM,EAAE;MAC3B,IAAIo2B,MAAM;MACV,IACGjhB,KAAK,KAAK8c,IAAI,CAACjyB,MAAM,IAAIE,IAAI,GAAGiV,KAAK,KACpCihB,MAAM,GAAGnE,IAAI,CAACnW,MAAM,CAAC3G,KAAK,CAAC,EAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC6S,QAAQ,CAACoO,MAAM,CAAC,CAAC,EAC7D;QACA,MAAML,GAAG,GAAG,IAAI,CAACE,YAAY,CAAChE,IAAI,CAACjwB,KAAK,CAAC9B,IAAI,EAAE,EAAEiV,KAAK,CAAC,CAAC;QACxD,IAAI,CAACghB,SAAS,EAAE;UACd9D,MAAM,GAAGA,MAAM,CAACltB,MAAM,CAAC4wB,GAAG,CAAC1D,MAAM,CAAC;UAClCkC,SAAS,GAAGA,SAAS,CAACpvB,MAAM,CAAC4wB,GAAG,CAACxB,SAAS,CAAC;;QAG7CK,YAAY,IAAImB,GAAG,CAACnB,YAAY;QAChC10B,IAAI,GAAGiV,KAAK;OACb,MAAM;QACLA,KAAK,EAAE;;;IAIX,OAAO;MAAEkd,MAAM;MAAEkC,SAAS;MAAEK;KAAc;;EAG5C3d,MAAMA,CAACgb,IAAI,EAAE6D,QAAQ,EAAE;IACrB,MAAM;MAAEzD,MAAM;MAAEkC;KAAW,GAAG,IAAI,CAACzrB,MAAM,CAACmpB,IAAI,EAAE6D,QAAQ,CAAC;IAEzD,MAAM5D,GAAG,GAAG,EAAE;IACd,KAAK,IAAI5vB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+vB,MAAM,CAACryB,MAAM,EAAEsC,CAAC,EAAE,EAAE;MACtC,MAAMmwB,KAAK,GAAGJ,MAAM,CAAC/vB,CAAC,CAAC;MACvB,MAAM+zB,GAAG,GAAG,IAAI,CAACnB,MAAM,CAACoB,YAAY,CAAC7D,KAAK,CAACzxB,EAAE,CAAC;MAC9CkxB,GAAG,CAAC/xB,IAAI,CAAE,OAAMk2B,GAAG,CAACx3B,QAAQ,CAAC,EAAE,CAAE,EAAC,CAACmD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAE7C,IAAI,IAAI,CAACqzB,MAAM,CAACgB,GAAG,CAAC,IAAI,IAAI,EAAE;QAC5B,IAAI,CAAChB,MAAM,CAACgB,GAAG,CAAC,GAAG5D,KAAK,CAACmC,YAAY,GAAG,IAAI,CAACtE,KAAK;;MAEpD,IAAI,IAAI,CAAC8E,OAAO,CAACiB,GAAG,CAAC,IAAI,IAAI,EAAE;QAC7B,IAAI,CAACjB,OAAO,CAACiB,GAAG,CAAC,GAAG5D,KAAK,CAAC8D,UAAU;;;IAIxC,OAAO,CAACrE,GAAG,EAAEqC,SAAS,CAAC;;EAGzBzB,aAAaA,CAACpwB,MAAM,EAAEmG,IAAI,EAAEitB,QAAQ,EAAE;IACpC,MAAM3sB,KAAK,GAAG,IAAI,CAACL,MAAM,CAACpG,MAAM,EAAEozB,QAAQ,EAAE,IAAI,CAAC,CAAClB,YAAY;IAC9D,MAAMtE,KAAK,GAAGznB,IAAI,GAAG,IAAI;IACzB,OAAOM,KAAK,GAAGmnB,KAAK;;EAGtB3Z,KAAKA,GAAG;IACN,MAAM6f,KAAK,GAAG,IAAI,CAACtB,MAAM,CAACuB,GAAG,IAAI,IAAI;IACrC,MAAMC,QAAQ,GAAG,IAAI,CAACtyB,QAAQ,CAACjD,GAAG,EAAE;IAEpC,IAAIq1B,KAAK,EAAE;MACTE,QAAQ,CAACryB,IAAI,CAAC2T,OAAO,GAAG,eAAe;;IAGzC,IAAI,CAACkd,MAAM,CACRyB,YAAY,EAAE,CACdC,EAAE,CAAC,MAAM,EAAEvyB,IAAI,IAAIqyB,QAAQ,CAAC/xB,KAAK,CAACN,IAAI,CAAC,CAAC,CACxCuyB,EAAE,CAAC,KAAK,EAAE,MAAMF,QAAQ,CAAC90B,GAAG,EAAE,CAAC;IAElC,MAAMi1B,WAAW,GACf,CAAC,CAAC,IAAI,CAAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,GACvB,IAAI,CAACA,IAAI,CAAC,MAAM,CAAC,CAAC2C,YAAY,GAC9BC,SAAS,KAAK,CAAC,KAAK,CAAC;IAC3B,IAAIC,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAAC7C,IAAI,CAAC8C,IAAI,CAACC,YAAY,EAAE;MAC/BF,KAAK,IAAI,CAAC,IAAI,CAAC;;IAEjB,IAAI,CAAC,IAAIH,WAAW,IAAIA,WAAW,IAAI,CAAC,EAAE;MACxCG,KAAK,IAAI,CAAC,IAAI,CAAC;;IAEjBA,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC;IAChB,IAAIH,WAAW,KAAK,EAAE,EAAE;MACtBG,KAAK,IAAI,CAAC,IAAI,CAAC;;IAEjB,IAAI,IAAI,CAAC7C,IAAI,CAACgD,IAAI,CAACC,QAAQ,CAACC,MAAM,EAAE;MAClCL,KAAK,IAAI,CAAC,IAAI,CAAC;;;;IAIjB,MAAMM,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAC3B/1B,GAAG,CAACe,CAAC,IAAIG,MAAM,CAAC80B,YAAY,CAAC,CAAC,IAAI,CAACv2B,EAAE,CAAC4B,UAAU,CAACN,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CACjE9B,IAAI,CAAC,EAAE,CAAC;IACX,MAAMG,IAAI,GAAG22B,GAAG,GAAG,GAAG,GAAG,IAAI,CAACnD,IAAI,CAACoB,cAAc,CAACiC,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC;IAEtE,MAAM;MAAEva;KAAM,GAAG,IAAI,CAACkX,IAAI;IAC1B,MAAMsD,UAAU,GAAG,IAAI,CAACrzB,QAAQ,CAACjD,GAAG,CAAC;MACnCsI,IAAI,EAAE,gBAAgB;MACtBiuB,QAAQ,EAAE/2B,IAAI;MACdg3B,KAAK,EAAEX,KAAK;MACZY,QAAQ,EAAE,CACR3a,IAAI,CAAC4a,IAAI,GAAG,IAAI,CAACvH,KAAK,EACtBrT,IAAI,CAAC6a,IAAI,GAAG,IAAI,CAACxH,KAAK,EACtBrT,IAAI,CAAC8a,IAAI,GAAG,IAAI,CAACzH,KAAK,EACtBrT,IAAI,CAACjS,IAAI,GAAG,IAAI,CAACslB,KAAK,CACvB;MACD0H,WAAW,EAAE,IAAI,CAAC7D,IAAI,CAAC8D,WAAW;MAClCC,MAAM,EAAE,IAAI,CAAC1G,QAAQ;MACrB2G,OAAO,EAAE,IAAI,CAAC1G,SAAS;MACvB2G,SAAS,EAAE,CAAC,IAAI,CAACjE,IAAI,CAACxC,SAAS,IAAI,IAAI,CAACwC,IAAI,CAACsB,MAAM,IAAI,IAAI,CAACnF,KAAK;MACjE+H,OAAO,EAAE,CAAC,IAAI,CAAClE,IAAI,CAACzC,OAAO,IAAI,CAAC,IAAI,IAAI,CAACpB,KAAK;MAC9CgI,KAAK,EAAE;KACR,CAAC,CAAC;;IAEH,IAAI9B,KAAK,EAAE;MACTiB,UAAU,CAACpzB,IAAI,CAACk0B,SAAS,GAAG7B,QAAQ;KACrC,MAAM;MACLe,UAAU,CAACpzB,IAAI,CAACm0B,SAAS,GAAG9B,QAAQ;;IAGtC,IAAI,IAAI,CAACtyB,QAAQ,CAAC8wB,MAAM,IAAI,IAAI,CAAC9wB,QAAQ,CAAC8wB,MAAM,KAAK,CAAC,EAAE;MACtD,MAAMuD,MAAM,GAAG31B,MAAM,CAACC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC;MAC/C,MAAM21B,SAAS,GAAG,IAAI,CAACt0B,QAAQ,CAACjD,GAAG,EAAE;MACrCu3B,SAAS,CAAC/zB,KAAK,CAAC8zB,MAAM,CAAC;MACvBC,SAAS,CAAC92B,GAAG,EAAE;MAEf61B,UAAU,CAACpzB,IAAI,CAACo0B,MAAM,GAAGC,SAAS;;IAGpCjB,UAAU,CAAC71B,GAAG,EAAE;IAEhB,MAAM+2B,kBAAkB,GAAG;MACzBlvB,IAAI,EAAE,MAAM;MACZuO,OAAO,EAAE,cAAc;MACvBoc,QAAQ,EAAEzzB,IAAI;MACdi4B,aAAa,EAAE;QACbC,QAAQ,EAAE,IAAIp2B,MAAM,CAAC,OAAO,CAAC;QAC7Bq2B,QAAQ,EAAE,IAAIr2B,MAAM,CAAC,UAAU,CAAC;QAChCs2B,UAAU,EAAE;OACb;MACDC,cAAc,EAAEvB,UAAU;MAC1BwB,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC5D,MAAM;KACnB;IAED,IAAI,CAACmB,KAAK,EAAE;MACVmC,kBAAkB,CAAC3gB,OAAO,GAAG,cAAc;MAC3C2gB,kBAAkB,CAACO,WAAW,GAAG,UAAU;;IAG7C,MAAMC,cAAc,GAAG,IAAI,CAAC/0B,QAAQ,CAACjD,GAAG,CAACw3B,kBAAkB,CAAC;IAE5DQ,cAAc,CAACv3B,GAAG,EAAE;IAEpB,IAAI,CAAC4H,UAAU,CAACnF,IAAI,GAAG;MACrBoF,IAAI,EAAE,MAAM;MACZuO,OAAO,EAAE,OAAO;MAChBoc,QAAQ,EAAEzzB,IAAI;MACd0zB,QAAQ,EAAE,YAAY;MACtB+E,eAAe,EAAE,CAACD,cAAc,CAAC;MACjCE,SAAS,EAAE,IAAI,CAACC,aAAa;KAC9B;IAED,OAAO,IAAI,CAAC9vB,UAAU,CAAC5H,GAAG,EAAE;;;;;;EAM9B03B,aAAaA,GAAG;IACd,MAAMC,IAAI,GAAG,IAAI,CAACn1B,QAAQ,CAACjD,GAAG,EAAE;IAEhC,MAAMq4B,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIjD,UAAU,IAAI,IAAI,CAACnB,OAAO,EAAE;MACnC,MAAMd,OAAO,GAAG,EAAE;;;MAGlB,KAAK,IAAI9yB,KAAK,IAAI+0B,UAAU,EAAE;QAC5B,IAAI/0B,KAAK,GAAG,MAAM,EAAE;UAClBA,KAAK,IAAI,OAAO;UAChB8yB,OAAO,CAACn0B,IAAI,CAAC40B,KAAK,CAAGvzB,KAAK,KAAK,EAAE,GAAI,KAAK,GAAI,MAAM,CAAC,CAAC;UACtDA,KAAK,GAAG,MAAM,GAAIA,KAAK,GAAG,KAAM;;QAGlC8yB,OAAO,CAACn0B,IAAI,CAAC40B,KAAK,CAACvzB,KAAK,CAAC,CAAC;;MAG5Bg4B,OAAO,CAACr5B,IAAI,CAAE,IAAGm0B,OAAO,CAAC9zB,IAAI,CAAC,GAAG,CAAE,GAAE,CAAC;;IAGxC,MAAMi5B,SAAS,GAAG,GAAG;IACrB,MAAMC,MAAM,GAAGz1B,IAAI,CAACwQ,IAAI,CAAC+kB,OAAO,CAACx5B,MAAM,GAAGy5B,SAAS,CAAC;IACpD,MAAME,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIr3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGo3B,MAAM,EAAEp3B,CAAC,EAAE,EAAE;MAC/B,MAAMs3B,KAAK,GAAGt3B,CAAC,GAAGm3B,SAAS;MAC3B,MAAM73B,GAAG,GAAGqC,IAAI,CAACmP,GAAG,CAAC,CAAC9Q,CAAC,GAAG,CAAC,IAAIm3B,SAAS,EAAED,OAAO,CAACx5B,MAAM,CAAC;MACzD25B,MAAM,CAACx5B,IAAI,CAAE,IAAG40B,KAAK,CAAC6E,KAAK,CAAE,MAAK7E,KAAK,CAACnzB,GAAG,GAAG,CAAC,CAAE,MAAK43B,OAAO,CAACx3B,KAAK,CAAC43B,KAAK,EAAEh4B,GAAG,CAAC,CAACpB,IAAI,CAAC,GAAG,CAAE,GAAE,CAAC;;IAG/F+4B,IAAI,CAAC33B,GAAG,CAAE;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE+3B,MAAM,CAACn5B,IAAI,CAAC,IAAI,CAAE;AACpB;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;IAEE,OAAO+4B,IAAI;;AAEf;;AC3RA,MAAMM,cAAc,CAAC;EACnB,OAAOhJ,IAAIA,CAACzsB,QAAQ,EAAE01B,GAAG,EAAEC,MAAM,EAAE/4B,EAAE,EAAE;IACrC,IAAImzB,IAAI;IACR,IAAI,OAAO2F,GAAG,KAAK,QAAQ,EAAE;MAC3B,IAAI5F,YAAY,CAACY,cAAc,CAACgF,GAAG,CAAC,EAAE;QACpC,OAAO,IAAI5F,YAAY,CAAC9vB,QAAQ,EAAE01B,GAAG,EAAE94B,EAAE,CAAC;;MAG5C84B,GAAG,GAAG/I,EAAE,CAACC,YAAY,CAAC8I,GAAG,CAAC;;IAE5B,IAAIh3B,MAAM,CAACK,QAAQ,CAAC22B,GAAG,CAAC,EAAE;MACxB3F,IAAI,GAAG6F,OAAO,CAACvqB,MAAM,CAACqqB,GAAG,EAAEC,MAAM,CAAC;KACnC,MAAM,IAAID,GAAG,YAAYG,UAAU,EAAE;MACpC9F,IAAI,GAAG6F,OAAO,CAACvqB,MAAM,CAAC3M,MAAM,CAACC,IAAI,CAAC+2B,GAAG,CAAC,EAAEC,MAAM,CAAC;KAChD,MAAM,IAAID,GAAG,YAAYI,WAAW,EAAE;MACrC/F,IAAI,GAAG6F,OAAO,CAACvqB,MAAM,CAAC3M,MAAM,CAACC,IAAI,CAAC,IAAIk3B,UAAU,CAACH,GAAG,CAAC,CAAC,EAAEC,MAAM,CAAC;;IAGjE,IAAI5F,IAAI,IAAI,IAAI,EAAE;MAChB,MAAM,IAAIr1B,KAAK,CAAC,mDAAmD,CAAC;;IAGtE,OAAO,IAAIm2B,YAAY,CAAC7wB,QAAQ,EAAE+vB,IAAI,EAAEnzB,EAAE,CAAC;;AAE/C;;AC3BA,MAAMm5B,WAAW,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;;EAEpC,IAAID,KAAK,CAACjG,IAAI,CAACmG,OAAO,EAAEnD,IAAI,EAAEoD,kBAAkB,KAAKF,KAAK,CAAClG,IAAI,CAACmG,OAAO,EAAEnD,IAAI,EAAEoD,kBAAkB,EAAE;IACjG,OAAO,KAAK;;;;EAId,IAAIrN,IAAI,CAACC,SAAS,CAACiN,KAAK,CAACjG,IAAI,CAACmG,OAAO,EAAE35B,IAAI,EAAE65B,OAAO,CAAC,KAAKtN,IAAI,CAACC,SAAS,CAACkN,KAAK,CAAClG,IAAI,CAACmG,OAAO,EAAE35B,IAAI,EAAE65B,OAAO,CAAC,EAAE;IAC3G,OAAO,KAAK;;EAGd,OAAO,IAAI;AACb,CAAC;AAED,iBAAe;EACbC,SAASA,CAACC,WAAW,GAAG,WAAW,EAAE;;IAEnC,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,UAAU,GAAG,CAAC;;;IAGnB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACC,gBAAgB,GAAG,EAAE;;;IAG1B,IAAIL,WAAW,EAAE;MACf,IAAI,CAACvG,IAAI,CAACuG,WAAW,CAAC;;GAEzB;EAEDvG,IAAIA,CAAC2F,GAAG,EAAEC,MAAM,EAAElxB,IAAI,EAAE;IACtB,IAAImyB,QAAQ,EAAE7G,IAAI;IAClB,IAAI,OAAO4F,MAAM,KAAK,QAAQ,EAAE;MAC9BlxB,IAAI,GAAGkxB,MAAM;MACbA,MAAM,GAAG,IAAI;;;;IAIf,IAAI,OAAOD,GAAG,KAAK,QAAQ,IAAI,IAAI,CAACiB,gBAAgB,CAACjB,GAAG,CAAC,EAAE;MACzDkB,QAAQ,GAAGlB,GAAG;MACd,CAAC;QAAEA,GAAG;QAAEC;OAAQ,GAAG,IAAI,CAACgB,gBAAgB,CAACjB,GAAG,CAAC;KAC9C,MAAM;MACLkB,QAAQ,GAAGjB,MAAM,IAAID,GAAG;MACxB,IAAI,OAAOkB,QAAQ,KAAK,QAAQ,EAAE;QAChCA,QAAQ,GAAG,IAAI;;;IAInB,IAAInyB,IAAI,IAAI,IAAI,EAAE;MAChB,IAAI,CAACoyB,QAAQ,CAACpyB,IAAI,CAAC;;;;IAIrB,IAAKsrB,IAAI,GAAG,IAAI,CAACwG,aAAa,CAACK,QAAQ,CAAC,EAAG;MACzC,IAAI,CAACF,KAAK,GAAG3G,IAAI;MACjB,OAAO,IAAI;;;;IAIb,MAAMnzB,EAAE,GAAI,IAAG,EAAE,IAAI,CAAC45B,UAAW,EAAC;IAClC,IAAI,CAACE,KAAK,GAAGjB,cAAc,CAAChJ,IAAI,CAAC,IAAI,EAAEiJ,GAAG,EAAEC,MAAM,EAAE/4B,EAAE,CAAC;;;;IAIvD,IAAI,CAACmzB,IAAI,GAAG,IAAI,CAACwG,aAAa,CAAC,IAAI,CAACG,KAAK,CAACn6B,IAAI,CAAC,KAAKw5B,WAAW,CAAC,IAAI,CAACW,KAAK,EAAE3G,IAAI,CAAC,EAAE;MACjF,IAAI,CAAC2G,KAAK,GAAG3G,IAAI;MACjB,OAAO,IAAI;;;;IAIb,IAAI6G,QAAQ,EAAE;MACZ,IAAI,CAACL,aAAa,CAACK,QAAQ,CAAC,GAAG,IAAI,CAACF,KAAK;;IAG3C,IAAI,IAAI,CAACA,KAAK,CAACn6B,IAAI,EAAE;MACnB,IAAI,CAACg6B,aAAa,CAAC,IAAI,CAACG,KAAK,CAACn6B,IAAI,CAAC,GAAG,IAAI,CAACm6B,KAAK;;IAGlD,OAAO,IAAI;GACZ;EAEDG,QAAQA,CAACJ,SAAS,EAAE;IAClB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,OAAO,IAAI;GACZ;EAEDK,iBAAiBA,CAAClI,UAAU,EAAE;IAC5B,IAAIA,UAAU,IAAI,IAAI,EAAE;MACtBA,UAAU,GAAG,KAAK;;IAEpB,OAAO,IAAI,CAAC8H,KAAK,CAAC/H,UAAU,CAAC,IAAI,CAAC8H,SAAS,EAAE7H,UAAU,CAAC;GACzD;EAEDmI,YAAYA,CAACx6B,IAAI,EAAEm5B,GAAG,EAAEC,MAAM,EAAE;IAC9B,IAAI,CAACgB,gBAAgB,CAACp6B,IAAI,CAAC,GAAG;MAC5Bm5B,GAAG;MACHC;KACD;IAED,OAAO,IAAI;;AAEf,CAAC;;ACtGD,MAAMqB,WAAW,GAAG,QAAQ;AAC5B,MAAMC,MAAM,GAAG,GAAG;AAElB,MAAMC,WAAW,SAASC,mBAAY,CAAC;EACrCv8B,WAAWA,CAACoF,QAAQ,EAAEnF,OAAO,EAAE;IAC7B,KAAK,EAAE;IACP,IAAI,CAACmF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACo3B,iBAAiB,GAAGv8B,OAAO,CAACu8B,iBAAiB,IAAI,GAAG;IACzD,IAAI,CAACC,MAAM,GAAI,CAACx8B,OAAO,CAACw8B,MAAM,IAAI,CAAC,IAAI,IAAI,CAACD,iBAAiB,GAAI,GAAG;IACpE,IAAI,CAACE,gBAAgB,GAAI,CAACz8B,OAAO,CAACy8B,gBAAgB,IAAI,CAAC,IAAI,IAAI,CAACF,iBAAiB,GAAI,GAAG;IACxF,IAAI,CAACG,WAAW,GAAI,CAAC18B,OAAO,CAAC08B,WAAW,KAAK,CAAC,IAAI,IAAI,CAACH,iBAAiB,GAAI,GAAG;IAC/E,IAAI,CAACI,OAAO,GAAG38B,OAAO,CAAC28B,OAAO,IAAI,CAAC;IACnC,IAAI,CAACC,SAAS,GAAI,CAAC58B,OAAO,CAAC48B,SAAS,IAAI,IAAI,GAAG58B,OAAO,CAAC48B,SAAS,GAAG,EAAE,IAAI,IAAI,CAACL,iBAAiB,GAAI,GAAG,CAAC;IACvG,IAAI,CAACxP,SAAS,GAAG,CAAG/sB,OAAO,CAACkK,KAAK,GAAG,IAAI,CAACqyB,iBAAiB,GAAI,GAAG,GAAK,IAAI,CAACK,SAAS,IAAI,IAAI,CAACD,OAAO,GAAG,CAAC,CAAE,IAAI,IAAI,CAACA,OAAO;IAC1H,IAAI,CAACE,SAAS,GAAG,IAAI,CAAC9P,SAAS;IAC/B,IAAI,CAAC+P,MAAM,GAAG,IAAI,CAAC33B,QAAQ,CAAC8I,CAAC;IAC7B,IAAI,CAAC8uB,MAAM,GAAG,IAAI,CAAC53B,QAAQ,CAACqkB,CAAC;IAC7B,IAAI,CAACwT,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,QAAQ,GAAGj9B,OAAO,CAACi9B,QAAQ;IAChC,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACrG,QAAQ,GAAG72B,OAAO,CAAC62B,QAAQ;;;IAGhC,IAAI72B,OAAO,CAACmK,MAAM,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACA,MAAM,GAAGnK,OAAO,CAACmK,MAAM;MAC5B,IAAI,CAAC4B,IAAI,GAAG,IAAI,CAACgxB,MAAM,GAAG/8B,OAAO,CAACmK,MAAM;KACzC,MAAM;MACL,IAAI,CAAC4B,IAAI,GAAG,IAAI,CAAC5G,QAAQ,CAAC0T,IAAI,CAAC9M,IAAI,EAAE;;;;IAIvC,IAAI,CAAC4rB,EAAE,CAAC,WAAW,EAAE33B,OAAO,IAAI;;;;MAI9B,MAAMw8B,MAAM,GAAG,IAAI,CAACU,UAAU,IAAI,IAAI,CAACV,MAAM;MAC7C,IAAI,CAACr3B,QAAQ,CAAC8I,CAAC,IAAIuuB,MAAM;MACzB,IAAI,CAACzP,SAAS,IAAIyP,MAAM;;;;MAIxB,IAAIx8B,OAAO,CAACm9B,cAAc,EAAE;QAC1B;;;;MAIF,OAAO,IAAI,CAACC,IAAI,CAAC,MAAM,EAAE,MAAM;QAC7B,IAAI,CAACj4B,QAAQ,CAAC8I,CAAC,IAAIuuB,MAAM;QACzB,IAAI,CAACzP,SAAS,IAAIyP,MAAM;QACxB,IAAIx8B,OAAO,CAACq9B,SAAS,IAAI,CAAC,IAAI,CAACH,UAAU,EAAE;UACzC,IAAI,CAACA,UAAU,GAAG,IAAI,CAACV,MAAM;;QAE/B,IAAI,CAACx8B,OAAO,CAACq9B,SAAS,EAAE;UACtB,OAAQ,IAAI,CAACH,UAAU,GAAG,CAAC;;OAE9B,CAAC;KACH,CAAC;;;IAGF,IAAI,CAACvF,EAAE,CAAC,UAAU,EAAE33B,OAAO,IAAI;MAC7B,MAAM;QAAEs9B;OAAO,GAAGt9B,OAAO;MACzB,IAAIs9B,KAAK,KAAK,SAAS,EAAE;QACvBt9B,OAAO,CAACs9B,KAAK,GAAG,MAAM;;MAExB,IAAI,CAACC,QAAQ,GAAG,IAAI;MAEpB,OAAO,IAAI,CAACH,IAAI,CAAC,MAAM,EAAE,MAAM;QAC7B,IAAI,CAACj4B,QAAQ,CAACqkB,CAAC,IAAIxpB,OAAO,CAACw9B,YAAY,IAAI,CAAC;QAC5Cx9B,OAAO,CAACs9B,KAAK,GAAGA,KAAK;QACrB,OAAQ,IAAI,CAACC,QAAQ,GAAG,KAAK;OAC9B,CAAC;KACH,CAAC;;EAGJE,SAASA,CAACC,IAAI,EAAE;IACd,OACE,IAAI,CAACv4B,QAAQ,CAAC0uB,aAAa,CAAC6J,IAAI,EAAE,IAAI,CAAC,GACvC,IAAI,CAACjB,gBAAgB,GACrB,IAAI,CAACC,WAAW;;EAIpBiB,MAAMA,CAACD,IAAI,EAAE1Q,CAAC,EAAE;IACd,IAAI0Q,IAAI,CAACA,IAAI,CAAC38B,MAAM,GAAG,CAAC,CAAC,IAAIo7B,WAAW,EAAE;MACxC,OAAOnP,CAAC,IAAI,IAAI,CAAC6P,SAAS;;IAE5B,OAAO7P,CAAC,GAAG,IAAI,CAACyQ,SAAS,CAACrB,MAAM,CAAC,IAAI,IAAI,CAACS,SAAS;;EAGrDe,QAAQA,CAAC5K,IAAI,EAAEpb,EAAE,EAAE;;IAEjB,IAAIimB,EAAE;IACN,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC/K,IAAI,CAAC;IACrC,IAAI/xB,IAAI,GAAG,IAAI;IACf,MAAM+8B,UAAU,GAAGx9B,MAAM,CAACgQ,MAAM,CAAC,IAAI,CAAC;IAEtC,OAAQqtB,EAAE,GAAGC,OAAO,CAACG,SAAS,EAAE,EAAG;MACjC,IAAIC,cAAc;MAClB,IAAIR,IAAI,GAAG1K,IAAI,CAACjwB,KAAK,CACnB,CAAC9B,IAAI,IAAI,IAAI,GAAGA,IAAI,CAAC81B,QAAQ,GAAGe,SAAS,KAAK,CAAC,EAC/C+F,EAAE,CAAC9G,QACL,CAAC;MACD,IAAI/J,CAAC,GACHgR,UAAU,CAACN,IAAI,CAAC,IAAI,IAAI,GACpBM,UAAU,CAACN,IAAI,CAAC,GACfM,UAAU,CAACN,IAAI,CAAC,GAAG,IAAI,CAACD,SAAS,CAACC,IAAI,CAAE;;;;MAI/C,IAAI1Q,CAAC,GAAG,IAAI,CAACD,SAAS,GAAG,IAAI,CAACmQ,UAAU,EAAE;;QAExC,IAAIiB,GAAG,GAAGl9B,IAAI;QACd,MAAMm9B,GAAG,GAAG,EAAE;QAEd,OAAOV,IAAI,CAAC38B,MAAM,EAAE;;UAElB,IAAIqC,CAAC,EAAEi7B,SAAS;UAChB,IAAIrR,CAAC,GAAG,IAAI,CAAC6P,SAAS,EAAE;;;YAGtBz5B,CAAC,GAAG4B,IAAI,CAACwQ,IAAI,CAAC,IAAI,CAACqnB,SAAS,IAAI7P,CAAC,GAAG0Q,IAAI,CAAC38B,MAAM,CAAC,CAAC;YACjDisB,CAAC,GAAG,IAAI,CAACyQ,SAAS,CAACC,IAAI,CAAC36B,KAAK,CAAC,CAAC,EAAEK,CAAC,CAAC,CAAC;YACpCi7B,SAAS,GAAGrR,CAAC,IAAI,IAAI,CAAC6P,SAAS,IAAIz5B,CAAC,GAAGs6B,IAAI,CAAC38B,MAAM;WACnD,MAAM;YACLqC,CAAC,GAAGs6B,IAAI,CAAC38B,MAAM;;UAEjB,IAAIu9B,UAAU,GAAGtR,CAAC,GAAG,IAAI,CAAC6P,SAAS,IAAIz5B,CAAC,GAAG,CAAC;;UAE5C,OAAOk7B,UAAU,IAAID,SAAS,EAAE;YAC9B,IAAIC,UAAU,EAAE;cACdtR,CAAC,GAAG,IAAI,CAACyQ,SAAS,CAACC,IAAI,CAAC36B,KAAK,CAAC,CAAC,EAAE,EAAEK,CAAC,CAAC,CAAC;cACtCk7B,UAAU,GAAGtR,CAAC,GAAG,IAAI,CAAC6P,SAAS,IAAIz5B,CAAC,GAAG,CAAC;aACzC,MAAM;cACL4pB,CAAC,GAAG,IAAI,CAACyQ,SAAS,CAACC,IAAI,CAAC36B,KAAK,CAAC,CAAC,EAAE,EAAEK,CAAC,CAAC,CAAC;cACtCk7B,UAAU,GAAGtR,CAAC,GAAG,IAAI,CAAC6P,SAAS,IAAIz5B,CAAC,GAAG,CAAC;cACxCi7B,SAAS,GAAGrR,CAAC,IAAI,IAAI,CAAC6P,SAAS,IAAIz5B,CAAC,GAAGs6B,IAAI,CAAC38B,MAAM;;;;;UAKtD,IAAIqC,CAAC,KAAK,CAAC,IAAI,IAAI,CAACy5B,SAAS,KAAK,IAAI,CAAC9P,SAAS,EAAE;YAChD3pB,CAAC,GAAG,CAAC;;;;UAIPg7B,GAAG,CAACG,QAAQ,GAAGV,EAAE,CAACU,QAAQ,IAAIn7B,CAAC,GAAGs6B,IAAI,CAAC38B,MAAM;UAC7Cm9B,cAAc,GAAGtmB,EAAE,CAAC8lB,IAAI,CAAC36B,KAAK,CAAC,CAAC,EAAEK,CAAC,CAAC,EAAE4pB,CAAC,EAAEoR,GAAG,EAAED,GAAG,CAAC;UAClDA,GAAG,GAAG;YAAEI,QAAQ,EAAE;WAAO;;;UAGzBb,IAAI,GAAGA,IAAI,CAAC36B,KAAK,CAACK,CAAC,CAAC;UACpB4pB,CAAC,GAAG,IAAI,CAACyQ,SAAS,CAACC,IAAI,CAAC;UAExB,IAAIQ,cAAc,KAAK,KAAK,EAAE;YAC5B;;;OAGL,MAAM;;QAELA,cAAc,GAAGtmB,EAAE,CAAC8lB,IAAI,EAAE1Q,CAAC,EAAE6Q,EAAE,EAAE58B,IAAI,CAAC;;MAGxC,IAAIi9B,cAAc,KAAK,KAAK,EAAE;QAC5B;;MAEFj9B,IAAI,GAAG48B,EAAE;;;EAIbW,IAAIA,CAACxL,IAAI,EAAEhzB,OAAO,EAAE;;IAElB,IAAI,CAACu8B,iBAAiB,GAAGv8B,OAAO,CAACu8B,iBAAiB,IAAI,GAAG;IACzD,IAAIv8B,OAAO,CAACw8B,MAAM,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACA,MAAM,GAAIx8B,OAAO,CAACw8B,MAAM,GAAG,IAAI,CAACD,iBAAiB,GAAI,GAAG;;IAE/D,IAAIv8B,OAAO,CAACy8B,gBAAgB,IAAI,IAAI,EAAE;MACpC,IAAI,CAACA,gBAAgB,GAAIz8B,OAAO,CAACy8B,gBAAgB,GAAG,IAAI,CAACF,iBAAiB,GAAI,GAAG;;IAEnF,IAAIv8B,OAAO,CAAC08B,WAAW,IAAI,IAAI,EAAE;MAC/B,IAAI,CAACA,WAAW,GAAI18B,OAAO,CAAC08B,WAAW,GAAG,IAAI,CAACH,iBAAiB,GAAI,GAAG;;IAEzE,IAAIv8B,OAAO,CAACi9B,QAAQ,IAAI,IAAI,EAAE;MAC5B,IAAI,CAACA,QAAQ,GAAGj9B,OAAO,CAACi9B,QAAQ;;;;;;IAMlC,MAAMwB,KAAK,GAAG,IAAI,CAACt5B,QAAQ,CAACqkB,CAAC,GAAG,IAAI,CAACrkB,QAAQ,CAAC82B,iBAAiB,CAAC,IAAI,CAAC;IACrE,IAAI,IAAI,CAAC92B,QAAQ,CAACqkB,CAAC,GAAG,IAAI,CAACzd,IAAI,IAAI0yB,KAAK,GAAG,IAAI,CAAC1yB,IAAI,EAAE;MACpD,IAAI,CAAC2yB,WAAW,EAAE;;IAGpB,IAAIj5B,MAAM,GAAG,EAAE;IACf,IAAIk5B,SAAS,GAAG,CAAC;IACjB,IAAIC,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAG,CAAC;IAEV,IAAI;MAAErV;KAAG,GAAG,IAAI,CAACrkB,QAAQ,CAAC;IAC1B,MAAM25B,QAAQ,GAAGA,MAAM;MACrB9+B,OAAO,CAAC2+B,SAAS,GAAGA,SAAS,GAAG,IAAI,CAACjC,WAAW,IAAIkC,EAAE,GAAG,CAAC,CAAC;MAC3D5+B,OAAO,CAAC++B,SAAS,GAAGH,EAAE;MACtB5+B,OAAO,CAAC+sB,SAAS,GAAG,IAAI,CAACA,SAAS;MAClC,CAAC;QAAEvD;OAAG,GAAG,IAAI,CAACrkB,QAAQ;MACtB,IAAI,CAAC65B,IAAI,CAAC,MAAM,EAAEv5B,MAAM,EAAEzF,OAAO,EAAE,IAAI,CAAC;MACxC,OAAO6+B,EAAE,EAAE;KACZ;IAED,IAAI,CAACG,IAAI,CAAC,cAAc,EAAEh/B,OAAO,EAAE,IAAI,CAAC;IAExC,IAAI,CAAC49B,QAAQ,CAAC5K,IAAI,EAAE,CAAC0K,IAAI,EAAE1Q,CAAC,EAAE6Q,EAAE,EAAE58B,IAAI,KAAK;MACzC,IAAIA,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACs9B,QAAQ,EAAE;QACjC,IAAI,CAACS,IAAI,CAAC,WAAW,EAAEh/B,OAAO,EAAE,IAAI,CAAC;QACrC,IAAI,CAAC68B,SAAS,GAAG,IAAI,CAAC9P,SAAS;;MAGjC,IAAI,IAAI,CAAC4Q,MAAM,CAACD,IAAI,EAAE1Q,CAAC,CAAC,EAAE;QACxBvnB,MAAM,IAAIi4B,IAAI;QACdiB,SAAS,IAAI3R,CAAC;QACd4R,EAAE,EAAE;;MAGN,IAAIf,EAAE,CAACU,QAAQ,IAAI,CAAC,IAAI,CAACZ,MAAM,CAACD,IAAI,EAAE1Q,CAAC,CAAC,EAAE;;;QAGxC,MAAMiS,EAAE,GAAG,IAAI,CAAC95B,QAAQ,CAAC82B,iBAAiB,CAAC,IAAI,CAAC;QAChD,IACE,IAAI,CAAC9xB,MAAM,IAAI,IAAI,IACnB,IAAI,CAAC8yB,QAAQ,IACb,IAAI,CAAC93B,QAAQ,CAACqkB,CAAC,GAAGyV,EAAE,GAAG,CAAC,GAAG,IAAI,CAAClzB,IAAI,IACpC,IAAI,CAACixB,MAAM,IAAI,IAAI,CAACL,OAAO,EAC3B;UACA,IAAI,IAAI,CAACM,QAAQ,KAAK,IAAI,EAAE;YAC1B,IAAI,CAACA,QAAQ,GAAG,GAAG;WACpB;UACDx3B,MAAM,GAAGA,MAAM,CAACzB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;UACnC26B,SAAS,GAAG,IAAI,CAAClB,SAAS,CAACh4B,MAAM,GAAG,IAAI,CAACw3B,QAAQ,CAAC;;;;UAIlD,OAAOx3B,MAAM,IAAIk5B,SAAS,GAAG,IAAI,CAAC5R,SAAS,EAAE;YAC3CtnB,MAAM,GAAGA,MAAM,CAAC1C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACiB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YAChD26B,SAAS,GAAG,IAAI,CAAClB,SAAS,CAACh4B,MAAM,GAAG,IAAI,CAACw3B,QAAQ,CAAC;;;UAGpD,IAAI0B,SAAS,IAAI,IAAI,CAAC5R,SAAS,EAAE;YAC/BtnB,MAAM,GAAGA,MAAM,GAAG,IAAI,CAACw3B,QAAQ;;UAGjC0B,SAAS,GAAG,IAAI,CAAClB,SAAS,CAACh4B,MAAM,CAAC;;QAGpC,IAAIo4B,EAAE,CAACU,QAAQ,EAAE;UACf,IAAIvR,CAAC,GAAG,IAAI,CAAC6P,SAAS,EAAE;YACtBiC,QAAQ,EAAE;YACVr5B,MAAM,GAAGi4B,IAAI;YACbiB,SAAS,GAAG3R,CAAC;YACb4R,EAAE,GAAG,CAAC;;UAGR,IAAI,CAACI,IAAI,CAAC,UAAU,EAAEh/B,OAAO,EAAE,IAAI,CAAC;;;;QAItC,IAAIyF,MAAM,CAACA,MAAM,CAAC1E,MAAM,GAAG,CAAC,CAAC,IAAIo7B,WAAW,EAAE;UAC5C12B,MAAM,GAAGA,MAAM,CAAC1C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGq5B,MAAM;UACrC,IAAI,CAACS,SAAS,IAAI,IAAI,CAACY,SAAS,CAACrB,MAAM,CAAC;;QAG1C0C,QAAQ,EAAE;;;;QAIV,IAAI,IAAI,CAAC35B,QAAQ,CAACqkB,CAAC,GAAGyV,EAAE,GAAG,IAAI,CAAClzB,IAAI,EAAE;UACpC,MAAMmyB,cAAc,GAAG,IAAI,CAACQ,WAAW,EAAE;;;UAGzC,IAAI,CAACR,cAAc,EAAE;YACnBU,EAAE,GAAG,CAAC;YACNn5B,MAAM,GAAG,EAAE;YACX,OAAO,KAAK;;;;;QAKhB,IAAIo4B,EAAE,CAACU,QAAQ,EAAE;UACf,IAAI,CAAC1B,SAAS,GAAG,IAAI,CAAC9P,SAAS;UAC/BtnB,MAAM,GAAG,EAAE;UACXk5B,SAAS,GAAG,CAAC;UACb,OAAQC,EAAE,GAAG,CAAC;SACf,MAAM;;UAEL,IAAI,CAAC/B,SAAS,GAAG,IAAI,CAAC9P,SAAS,GAAGC,CAAC;UACnCvnB,MAAM,GAAGi4B,IAAI;UACbiB,SAAS,GAAG3R,CAAC;UACb,OAAQ4R,EAAE,GAAG,CAAC;;OAEjB,MAAM;QACL,OAAQ,IAAI,CAAC/B,SAAS,IAAI7P,CAAC;;KAE9B,CAAC;IAEF,IAAI4R,EAAE,GAAG,CAAC,EAAE;MACV,IAAI,CAACI,IAAI,CAAC,UAAU,EAAEh/B,OAAO,EAAE,IAAI,CAAC;MACpC8+B,QAAQ,EAAE;;IAGZ,IAAI,CAACE,IAAI,CAAC,YAAY,EAAEh/B,OAAO,EAAE,IAAI,CAAC;;;;;IAKtC,IAAIA,OAAO,CAACq9B,SAAS,KAAK,IAAI,EAAE;MAC9B,IAAIwB,EAAE,GAAG,CAAC,EAAE;QACV,IAAI,CAAC3B,UAAU,GAAG,CAAC;;MAErB,IAAI,CAACA,UAAU,IAAIl9B,OAAO,CAAC2+B,SAAS,IAAI,CAAC;MACzC,OAAQ,IAAI,CAACx5B,QAAQ,CAACqkB,CAAC,GAAGA,CAAC;KAC5B,MAAM;MACL,OAAQ,IAAI,CAACrkB,QAAQ,CAAC8I,CAAC,GAAG,IAAI,CAAC6uB,MAAM;;;EAIzC4B,WAAWA,CAAC1+B,OAAO,EAAE;IACnB,IAAI,CAACg/B,IAAI,CAAC,YAAY,EAAEh/B,OAAO,EAAE,IAAI,CAAC;IAEtC,IAAI,EAAE,IAAI,CAACg9B,MAAM,GAAG,IAAI,CAACL,OAAO,EAAE;;;MAGhC,IAAI,IAAI,CAACxyB,MAAM,IAAI,IAAI,EAAE;QACvB,OAAO,KAAK;;MAGd,IAAI,CAAChF,QAAQ,CAAC+5B,iBAAiB,EAAE;MACjC,IAAI,CAAClC,MAAM,GAAG,CAAC;MACf,IAAI,CAACD,MAAM,GAAG,IAAI,CAAC53B,QAAQ,CAAC0T,IAAI,CAAC9O,OAAO,CAACvD,GAAG;MAC5C,IAAI,CAACuF,IAAI,GAAG,IAAI,CAAC5G,QAAQ,CAAC0T,IAAI,CAAC9M,IAAI,EAAE;MACrC,IAAI,CAAC5G,QAAQ,CAAC8I,CAAC,GAAG,IAAI,CAAC6uB,MAAM;MAC7B,IAAI,IAAI,CAAC33B,QAAQ,CAACqY,UAAU,EAAE;QAC5B,IAAI,CAACrY,QAAQ,CAACkY,SAAS,CAAC,GAAG,IAAI,CAAClY,QAAQ,CAACqY,UAAU,CAAC;;MAEtD,IAAI,CAACwhB,IAAI,CAAC,WAAW,EAAEh/B,OAAO,EAAE,IAAI,CAAC;KACtC,MAAM;MACL,IAAI,CAACmF,QAAQ,CAAC8I,CAAC,IAAI,IAAI,CAAC8e,SAAS,GAAG,IAAI,CAAC6P,SAAS;MAClD,IAAI,CAACz3B,QAAQ,CAACqkB,CAAC,GAAG,IAAI,CAACuT,MAAM;MAC7B,IAAI,CAACiC,IAAI,CAAC,aAAa,EAAEh/B,OAAO,EAAE,IAAI,CAAC;;IAGzC,IAAI,CAACg/B,IAAI,CAAC,cAAc,EAAEh/B,OAAO,EAAE,IAAI,CAAC;IACxC,OAAO,IAAI;;AAEf;;AC/VA,MAAM;UAAE8E;AAAO,CAAC,GAAG3D,SAAS;AAE5B,gBAAe;EACbg+B,QAAQA,GAAG;IACT,IAAI,CAACC,KAAK,GAAG,IAAI,CAACA,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;;IAElC,IAAI,CAACpxB,CAAC,GAAG,CAAC;IACV,IAAI,CAACub,CAAC,GAAG,CAAC;IACV,OAAQ,IAAI,CAAC8V,QAAQ,GAAG,CAAC;GAC1B;EAED3M,OAAOA,CAAC2M,QAAQ,EAAE;IAChB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,OAAO,IAAI;GACZ;EAEDC,QAAQA,CAACC,KAAK,EAAE;IACd,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjBA,KAAK,GAAG,CAAC;;IAEX,IAAI,CAAChW,CAAC,IAAI,IAAI,CAACyS,iBAAiB,CAAC,IAAI,CAAC,GAAGuD,KAAK,GAAG,IAAI,CAACF,QAAQ;IAC9D,OAAO,IAAI;GACZ;EAEDG,MAAMA,CAACD,KAAK,EAAE;IACZ,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjBA,KAAK,GAAG,CAAC;;IAEX,IAAI,CAAChW,CAAC,IAAI,IAAI,CAACyS,iBAAiB,CAAC,IAAI,CAAC,GAAGuD,KAAK,GAAG,IAAI,CAACF,QAAQ;IAC9D,OAAO,IAAI;GACZ;EAEDI,KAAKA,CAAC1M,IAAI,EAAE/kB,CAAC,EAAEub,CAAC,EAAExpB,OAAO,EAAE2/B,YAAY,EAAE;IACvC3/B,OAAO,GAAG,IAAI,CAAC4/B,YAAY,CAAC3xB,CAAC,EAAEub,CAAC,EAAExpB,OAAO,CAAC;;;IAG1CgzB,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAG,EAAE,GAAI,GAAEA,IAAK,EAAC;;;IAGpC,IAAIhzB,OAAO,CAAC08B,WAAW,EAAE;MACvB1J,IAAI,GAAGA,IAAI,CAAChvB,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;;IAGrC,MAAM67B,YAAY,GAAGA,MAAM;MACzB,IAAI7/B,OAAO,CAAC8/B,YAAY,EAAE;QACxB9/B,OAAO,CAAC8/B,YAAY,CAAC3/B,GAAG,CAAC,IAAI,CAAC4/B,MAAM,CAAC//B,OAAO,CAACggC,UAAU,IAAI,GAAG,EAC5D,CAAC,IAAI,CAACC,oBAAoB,CAACjgC,OAAO,CAACggC,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;;KAE7D;;;IAGD,IAAIhgC,OAAO,CAACkK,KAAK,EAAE;MACjB,IAAIg2B,OAAO,GAAG,IAAI,CAACC,QAAQ;MAC3B,IAAI,CAACD,OAAO,EAAE;QACZA,OAAO,GAAG,IAAI7D,WAAW,CAAC,IAAI,EAAEr8B,OAAO,CAAC;QACxCkgC,OAAO,CAACvI,EAAE,CAAC,MAAM,EAAEgI,YAAY,CAAC;QAChCO,OAAO,CAACvI,EAAE,CAAC,WAAW,EAAEkI,YAAY,CAAC;;MAGvC,IAAI,CAACM,QAAQ,GAAGngC,OAAO,CAACq9B,SAAS,GAAG6C,OAAO,GAAG,IAAI;MAClD,IAAI,CAACE,YAAY,GAAGpgC,OAAO,CAACq9B,SAAS,GAAGr9B,OAAO,GAAG,IAAI;MACtDkgC,OAAO,CAAC1B,IAAI,CAACxL,IAAI,EAAEhzB,OAAO,CAAC;;;KAG5B,MAAM;MACL,KAAK,IAAI6yB,IAAI,IAAIG,IAAI,CAACtB,KAAK,CAAC,IAAI,CAAC,EAAE;QACjCmO,YAAY,EAAE;QACdF,YAAY,CAAC9M,IAAI,EAAE7yB,OAAO,CAAC;;;IAI/B,OAAO,IAAI;GACZ;EAEDgzB,IAAIA,CAACA,IAAI,EAAE/kB,CAAC,EAAEub,CAAC,EAAExpB,OAAO,EAAE;IACxB,OAAO,IAAI,CAAC0/B,KAAK,CAAC1M,IAAI,EAAE/kB,CAAC,EAAEub,CAAC,EAAExpB,OAAO,EAAE,IAAI,CAACo/B,KAAK,CAAC;GACnD;EAEDvL,aAAaA,CAACpwB,MAAM,EAAEzD,OAAO,GAAG,EAAE,EAAE;IAClC,MAAMu8B,iBAAiB,GAAGv8B,OAAO,CAACu8B,iBAAiB,IAAI,GAAG;IAC1D,OAAQ,CAAC,IAAI,CAACV,KAAK,CAAChI,aAAa,CAACpwB,MAAM,EAAE,IAAI,CAACm4B,SAAS,EAAE57B,OAAO,CAAC62B,QAAQ,CAAC,GAAG,CAAC72B,OAAO,CAACy8B,gBAAgB,IAAI,CAAC,KAAKh5B,MAAM,CAAC1C,MAAM,GAAG,CAAC,CAAC,IAAIw7B,iBAAiB,GAAI,GAAG;GAChK;EAED8D,cAAcA,CAACrN,IAAI,EAAEhzB,OAAO,EAAE;IAC5B,MAAM;MAAEiO,CAAC;MAAEub;KAAG,GAAG,IAAI;IAErBxpB,OAAO,GAAG,IAAI,CAAC4/B,YAAY,CAAC5/B,OAAO,CAAC;IACpCA,OAAO,CAACmK,MAAM,GAAGm2B,QAAQ,CAAC;;IAE1B,MAAM3N,OAAO,GAAG3yB,OAAO,CAAC2yB,OAAO,IAAI,IAAI,CAAC2M,QAAQ,IAAI,CAAC;IACrD,IAAI,CAACI,KAAK,CAAC1M,IAAI,EAAE,IAAI,CAAC/kB,CAAC,EAAE,IAAI,CAACub,CAAC,EAAExpB,OAAO,EAAE,MAAM;MAC9C,OAAQ,IAAI,CAACwpB,CAAC,IAAI,IAAI,CAACyS,iBAAiB,CAAC,IAAI,CAAC,GAAGtJ,OAAO;KACzD,CAAC;IAEF,MAAMxoB,MAAM,GAAG,IAAI,CAACqf,CAAC,GAAGA,CAAC;IACzB,IAAI,CAACvb,CAAC,GAAGA,CAAC;IACV,IAAI,CAACub,CAAC,GAAGA,CAAC;IAEV,OAAOrf,MAAM;GACd;EAEDo2B,IAAIA,CAACA,IAAI,EAAEtyB,CAAC,EAAEub,CAAC,EAAExpB,OAAO,EAAEkgC,OAAO,EAAE;IACjClgC,OAAO,GAAG,IAAI,CAAC4/B,YAAY,CAAC3xB,CAAC,EAAEub,CAAC,EAAExpB,OAAO,CAAC;IAE1C,MAAMwgC,QAAQ,GAAGxgC,OAAO,CAACwgC,QAAQ,IAAI,QAAQ;IAC7C,MAAMC,IAAI,GAAGz7B,IAAI,CAACC,KAAK,CAAE,IAAI,CAAC42B,KAAK,CAACtJ,QAAQ,GAAG,IAAI,GAAI,IAAI,CAACqJ,SAAS,CAAC;IACtE,MAAM8E,OAAO,GAAGD,IAAI,GAAG,CAAC;IACxB,MAAMvvB,CAAC,GAAGlR,OAAO,CAAC2gC,YAAY,IAAIF,IAAI,GAAG,CAAC;IAC1C,MAAMjE,MAAM,GACVx8B,OAAO,CAAC4gC,UAAU,KAAKJ,QAAQ,KAAK,QAAQ,GAAGtvB,CAAC,GAAG,CAAC,GAAGuvB,IAAI,GAAG,CAAC,CAAC;IAClE,MAAMI,UAAU,GACd7gC,OAAO,CAAC8gC,YAAY,KAAKN,QAAQ,KAAK,QAAQ,GAAGtvB,CAAC,GAAG,CAAC,GAAGuvB,IAAI,GAAG,CAAC,CAAC;IAEpE,IAAIM,KAAK,GAAG,CAAC;IACb,MAAMp8B,KAAK,GAAG,EAAE;IAChB,MAAMq8B,MAAM,GAAG,EAAE;IACjB,MAAMC,OAAO,GAAG,EAAE;IAElB,IAAIC,OAAO,GAAG,UAAUX,IAAI,EAAE;MAC5B,IAAIx7B,CAAC,GAAG,CAAC;MACT,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGk9B,IAAI,CAACx/B,MAAM,EAAEsC,CAAC,EAAE,EAAE;QACpC,MAAM89B,IAAI,GAAGZ,IAAI,CAACl9B,CAAC,CAAC;QACpB,IAAIP,KAAK,CAAC4B,OAAO,CAACy8B,IAAI,CAAC,EAAE;UACvBJ,KAAK,EAAE;UACPG,OAAO,CAACC,IAAI,CAAC;UACbJ,KAAK,EAAE;SACR,MAAM;UACLp8B,KAAK,CAACzD,IAAI,CAACigC,IAAI,CAAC;UAChBH,MAAM,CAAC9/B,IAAI,CAAC6/B,KAAK,CAAC;UAClB,IAAIP,QAAQ,KAAK,QAAQ,EAAE;YACzBS,OAAO,CAAC//B,IAAI,CAAC6D,CAAC,EAAE,CAAC;;;;KAIxB;IAEDm8B,OAAO,CAACX,IAAI,CAAC;IAEb,MAAMa,KAAK,GAAG,UAAUr8B,CAAC,EAAE;MACzB,QAAQy7B,QAAQ;QACd,KAAK,UAAU;UACb,OAAQ,GAAEz7B,CAAE,GAAE;QAChB,KAAK,UAAU;UACb,IAAIs8B,MAAM,GAAG79B,MAAM,CAAC80B,YAAY,CAAE,CAACvzB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAI,EAAE,CAAC;UACrD,IAAIu8B,KAAK,GAAGt8B,IAAI,CAAC4H,KAAK,CAAC,CAAC7H,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;UACxC,IAAIiuB,IAAI,GAAGlwB,KAAK,CAACw+B,KAAK,GAAG,CAAC,CAAC,CAAC//B,IAAI,CAAC8/B,MAAM,CAAC;UACxC,OAAQ,GAAErO,IAAK,GAAE;;KAEtB;IAED,MAAMuO,YAAY,GAAG,UAAUC,QAAQ,EAAEn+B,CAAC,EAAE;MAC1C68B,OAAO,GAAG,IAAI7D,WAAW,CAAC,IAAI,EAAEr8B,OAAO,CAAC;MACxCkgC,OAAO,CAACvI,EAAE,CAAC,MAAM,EAAE,IAAI,CAACyH,KAAK,CAAC;MAE9B2B,KAAK,GAAG,CAAC;MACTb,OAAO,CAAC9C,IAAI,CAAC,WAAW,EAAE,MAAM;QAC9B,IAAI+D,IAAI,EAAEM,QAAQ,EAAEC,SAAS,EAAEC,QAAQ;QACvC,IAAI3hC,OAAO,CAAC8/B,YAAY,EAAE;UACxB,IAAI9/B,OAAO,CAAC4hC,WAAW,EAAE;YACvB,CAACH,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,CAAC,GAAG3hC,OAAO,CAAC4hC,WAAW;WACtD,MAAM;YACL,CAACH,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC;;;QAI5D,IAAIF,QAAQ,EAAE;UACZN,IAAI,GAAG,IAAI,CAACpB,MAAM,CAAC0B,QAAQ,CAAC;UAC5BzhC,OAAO,CAAC8/B,YAAY,CAAC3/B,GAAG,CAACghC,IAAI,CAAC;SAC/B,MAAM,IAAInhC,OAAO,CAAC8/B,YAAY,EAAE;UAC/BqB,IAAI,GAAGnhC,OAAO,CAAC8/B,YAAY;;QAG7B,IAAI18B,CAAC;QACL,IAAI,CAACA,CAAC,GAAG49B,MAAM,CAAC39B,CAAC,EAAE,CAAC,MAAM09B,KAAK,EAAE;UAC/B,MAAMc,IAAI,GAAGhB,UAAU,IAAIz9B,CAAC,GAAG29B,KAAK,CAAC;UACrC,IAAI,CAAC9yB,CAAC,IAAI4zB,IAAI;UACd3B,OAAO,CAACnT,SAAS,IAAI8U,IAAI;UACzBd,KAAK,GAAG39B,CAAC;;QAGX,IAAI+9B,IAAI,KAAKO,SAAS,IAAIC,QAAQ,CAAC,EAAE;UACnCR,IAAI,CAAChhC,GAAG,CAAC,IAAI,CAAC4/B,MAAM,CAAC2B,SAAS,IAAIC,QAAQ,EACxC,CAAC,IAAI,CAAC1B,oBAAoB,CAACyB,SAAS,IAAIC,QAAQ,CAAC,CAAC,CAAC,CAAC;;QAExD,QAAQnB,QAAQ;UACd,KAAK,QAAQ;YACX,IAAI,CAACtR,MAAM,CAAC,IAAI,CAACjhB,CAAC,GAAGuuB,MAAM,GAAGtrB,CAAC,EAAE,IAAI,CAACsY,CAAC,GAAGkX,OAAO,EAAExvB,CAAC,CAAC;YACrD,IAAI,CAAC0f,IAAI,EAAE;YACX;UACF,KAAK,UAAU;UACf,KAAK,UAAU;YACb,IAAIoC,IAAI,GAAGoO,KAAK,CAACH,OAAO,CAAC59B,CAAC,GAAG,CAAC,CAAC,CAAC;YAChC,IAAI,CAACy+B,SAAS,CAAC9O,IAAI,EAAE,IAAI,CAAC/kB,CAAC,GAAGuuB,MAAM,EAAE,IAAI,CAAChT,CAAC,EAAExpB,OAAO,CAAC;YACtD;;QAGJ,IAAImhC,IAAI,IAAIO,SAAS,IAAIC,QAAQ,EAAE;UACjCR,IAAI,CAAChhC,GAAG,CAAC,IAAI,CAAC4/B,MAAM,CAAC4B,QAAQ,EAAE,CAAC,IAAI,CAAC1B,oBAAoB,CAAC0B,QAAQ,CAAC,CAAC,CAAC,CAAC;;QAExE,IAAIR,IAAI,IAAIA,IAAI,KAAKnhC,OAAO,CAAC8/B,YAAY,EAAE;UACzCqB,IAAI,CAACx+B,GAAG,EAAE;;OAEb,CAAC;MAEFu9B,OAAO,CAACvI,EAAE,CAAC,cAAc,EAAE,MAAM;QAC/B,MAAM7gB,GAAG,GAAG0lB,MAAM,GAAGqE,UAAU,IAAIE,KAAK,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC9yB,CAAC,IAAI6I,GAAG;QACb,OAAQopB,OAAO,CAACnT,SAAS,IAAIjW,GAAG;OACjC,CAAC;MAEFopB,OAAO,CAACvI,EAAE,CAAC,YAAY,EAAE,MAAM;QAC7B,MAAM7gB,GAAG,GAAG0lB,MAAM,GAAGqE,UAAU,IAAIE,KAAK,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC9yB,CAAC,IAAI6I,GAAG;QACb,OAAQopB,OAAO,CAACnT,SAAS,IAAIjW,GAAG;OACjC,CAAC;MAEFopB,OAAO,CAAC1B,IAAI,CAACgD,QAAQ,EAAExhC,OAAO,CAAC;KAChC;IAGD,KAAK,IAAIqD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,KAAK,CAAC5D,MAAM,EAAEsC,CAAC,EAAE,EAAE;MACrCk+B,YAAY,CAAC18B,IAAI,CAAC,IAAI,EAAEF,KAAK,CAACtB,CAAC,CAAC,EAAEA,CAAC,CAAC;;IAGtC,OAAO,IAAI;GACZ;EAEDu8B,YAAYA,CAAC3xB,CAAC,GAAG,EAAE,EAAEub,CAAC,EAAExpB,OAAO,GAAG,EAAE,EAAE;IACpC,IAAI,OAAOiO,CAAC,KAAK,QAAQ,EAAE;MACzBjO,OAAO,GAAGiO,CAAC;MACXA,CAAC,GAAG,IAAI;;;;IAIV,MAAMie,MAAM,GAAG1rB,MAAM,CAACuhC,MAAM,CAAC,EAAE,EAAE/hC,OAAO,CAAC;;;IAGzC,IAAI,IAAI,CAACogC,YAAY,EAAE;MACrB,KAAK,IAAIhgC,GAAG,IAAI,IAAI,CAACggC,YAAY,EAAE;QACjC,MAAM//B,GAAG,GAAG,IAAI,CAAC+/B,YAAY,CAAChgC,GAAG,CAAC;QAClC,IAAIA,GAAG,KAAK,WAAW,EAAE;UACvB,IAAI8rB,MAAM,CAAC9rB,GAAG,CAAC,KAAK03B,SAAS,EAAE;YAC7B5L,MAAM,CAAC9rB,GAAG,CAAC,GAAGC,GAAG;;;;;;;IAOzB,IAAI4N,CAAC,IAAI,IAAI,EAAE;MACb,IAAI,CAACA,CAAC,GAAGA,CAAC;;IAEZ,IAAIub,CAAC,IAAI,IAAI,EAAE;MACb,IAAI,CAACA,CAAC,GAAGA,CAAC;;;;IAIZ,IAAI0C,MAAM,CAAC8V,SAAS,KAAK,KAAK,EAAE;MAC9B,IAAI9V,MAAM,CAAChiB,KAAK,IAAI,IAAI,EAAE;QACxBgiB,MAAM,CAAChiB,KAAK,GAAG,IAAI,CAAC2O,IAAI,CAAC3O,KAAK,GAAG,IAAI,CAAC+D,CAAC,GAAG,IAAI,CAAC4K,IAAI,CAAC9O,OAAO,CAACpD,KAAK;;MAEnEulB,MAAM,CAAChiB,KAAK,GAAGlF,IAAI,CAACkS,GAAG,CAACgV,MAAM,CAAChiB,KAAK,EAAE,CAAC,CAAC;;IAG1C,IAAI,CAACgiB,MAAM,CAACyQ,OAAO,EAAE;MACnBzQ,MAAM,CAACyQ,OAAO,GAAG,CAAC;;IAEpB,IAAIzQ,MAAM,CAAC0Q,SAAS,IAAI,IAAI,EAAE;MAC5B1Q,MAAM,CAAC0Q,SAAS,GAAG,EAAE;KACtB;;IAED,OAAO1Q,MAAM;GACd;EAEDkT,KAAKA,CAACpM,IAAI,EAAEhzB,OAAO,GAAG,EAAE,EAAEkgC,OAAO,EAAE;IACjC,IAAI,CAAC4B,SAAS,CAAC9O,IAAI,EAAE,IAAI,CAAC/kB,CAAC,EAAE,IAAI,CAACub,CAAC,EAAExpB,OAAO,CAAC;IAC7C,MAAM2yB,OAAO,GAAG3yB,OAAO,CAAC2yB,OAAO,IAAI,IAAI,CAAC2M,QAAQ,IAAI,CAAC;IAErD,IAAI,CAACY,OAAO,EAAE;MACZ,OAAQ,IAAI,CAACjyB,CAAC,IAAI,IAAI,CAAC4lB,aAAa,CAACb,IAAI,EAAEhzB,OAAO,CAAC;KACpD,MAAM;MACL,OAAQ,IAAI,CAACwpB,CAAC,IAAI,IAAI,CAACyS,iBAAiB,CAAC,IAAI,CAAC,GAAGtJ,OAAO;;GAE3D;EAEDmP,SAASA,CAAC9O,IAAI,EAAE/kB,CAAC,EAAEub,CAAC,EAAExpB,OAAO,EAAE;IAC7B,IAAIyX,EAAE,EAAE4d,OAAO,EAAEhyB,CAAC,EAAEiyB,SAAS,EAAEqJ,SAAS,EAAExrB,KAAK;IAC/C6f,IAAI,GAAI,GAAEA,IAAK,EAAC,CAAChvB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IACnC,IAAIgvB,IAAI,CAACjyB,MAAM,KAAK,CAAC,EAAE;MACrB;;;;IAIF,MAAMu8B,KAAK,GAAGt9B,OAAO,CAACs9B,KAAK,IAAI,MAAM;IACrC,IAAIZ,WAAW,GAAG18B,OAAO,CAAC08B,WAAW,IAAI,CAAC;IAC1C,MAAMD,gBAAgB,GAAGz8B,OAAO,CAACy8B,gBAAgB,IAAI,CAAC;IACtD,MAAMF,iBAAiB,GAAGv8B,OAAO,CAACu8B,iBAAiB,IAAI,GAAG;;;IAG1D,IAAIv8B,OAAO,CAACkK,KAAK,EAAE;MACjB,QAAQozB,KAAK;QACX,KAAK,OAAO;UACVqB,SAAS,GAAG,IAAI,CAAC9K,aAAa,CAACb,IAAI,CAAChvB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAEhE,OAAO,CAAC;UACjEiO,CAAC,IAAIjO,OAAO,CAAC+sB,SAAS,GAAG4R,SAAS;UAClC;QAEF,KAAK,QAAQ;UACX1wB,CAAC,IAAIjO,OAAO,CAAC+sB,SAAS,GAAG,CAAC,GAAG/sB,OAAO,CAAC2+B,SAAS,GAAG,CAAC;UAClD;QAEF,KAAK,SAAS;;UAEZxrB,KAAK,GAAG6f,IAAI,CAACiP,IAAI,EAAE,CAACvQ,KAAK,CAAC,KAAK,CAAC;UAChCiN,SAAS,GAAG,IAAI,CAAC9K,aAAa,CAACb,IAAI,CAAChvB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAEhE,OAAO,CAAC;UACjE,IAAIkiC,UAAU,GAAG,IAAI,CAACrO,aAAa,CAAC,GAAG,CAAC,GAAG4I,gBAAgB;UAC3DC,WAAW,GAAG13B,IAAI,CAACkS,GAAG,CACpB,CAAC,EACD,CAAClX,OAAO,CAAC+sB,SAAS,GAAG4R,SAAS,IAAI35B,IAAI,CAACkS,GAAG,CAAC,CAAC,EAAE/D,KAAK,CAACpS,MAAM,GAAG,CAAC,CAAC,GAC/DmhC,UACF,CAAC;UACD;;;;;IAKN,IAAI,OAAOliC,OAAO,CAACmiC,QAAQ,KAAK,QAAQ,EAAE;MACxC1qB,EAAE,GAAG,CAACzX,OAAO,CAACmiC,QAAQ;KACvB,MAAM;MACL,QAAQniC,OAAO,CAACmiC,QAAQ;QACtB,KAAK,YAAY;UACf1qB,EAAE,GAAG,GAAG,GAAG,IAAI,CAACokB,KAAK,CAACpJ,OAAO;UAC7B;QACF,KAAK,QAAQ;QACb,KAAK,aAAa;UAChBhb,EAAE,GAAG,GAAG,IAAI,IAAI,CAACokB,KAAK,CAACrJ,SAAS,GAAG,IAAI,CAACqJ,KAAK,CAACtJ,QAAQ,CAAC;UACvD;QACF,KAAK,QAAQ;QACb,KAAK,aAAa;UAChB9a,EAAE,GAAG,IAAI,CAACokB,KAAK,CAACrJ,SAAS;UACzB;QACF,KAAK,YAAY;UACf/a,EAAE,GAAG,CAAC;UACN;QACF,KAAK,cAAc;UACjBA,EAAE,GAAG,GAAG,GAAG,IAAI,CAACokB,KAAK,CAACtJ,QAAQ;UAC9B;QACF,KAAK,SAAS;UACZ9a,EAAE,GAAG,GAAG,GAAG,IAAI,CAACokB,KAAK,CAACtJ,QAAQ;UAC9B;QACF,KAAK,KAAK;UACR9a,EAAE,GAAG,IAAI,CAACokB,KAAK,CAACtJ,QAAQ;UACxB;QACF;UACE9a,EAAE,GAAG,IAAI,CAACokB,KAAK,CAACtJ,QAAQ;;MAE5B9a,EAAE,GAAIA,EAAE,GAAG,IAAI,GAAI,IAAI,CAACmkB,SAAS;;;;IAInC,MAAMwG,aAAa,GACjBpiC,OAAO,CAAC2+B,SAAS,GACjBjC,WAAW,IAAI18B,OAAO,CAAC++B,SAAS,GAAG,CAAC,CAAC,GACrCtC,gBAAgB,IAAIzJ,IAAI,CAACjyB,MAAM,GAAG,CAAC,CAAC;;;IAGtC,IAAIf,OAAO,CAACqiC,IAAI,IAAI,IAAI,EAAE;MACxB,IAAI,CAACA,IAAI,CAACp0B,CAAC,EAAEub,CAAC,EAAE4Y,aAAa,EAAE,IAAI,CAACnG,iBAAiB,EAAE,EAAEj8B,OAAO,CAACqiC,IAAI,CAAC;;IAExE,IAAIriC,OAAO,CAACsiC,IAAI,IAAI,IAAI,EAAE;MACxB,IAAI,CAACA,IAAI,CAACr0B,CAAC,EAAEub,CAAC,EAAE4Y,aAAa,EAAE,IAAI,CAACnG,iBAAiB,EAAE,EAAEj8B,OAAO,CAACsiC,IAAI,CAAC;;IAExE,IAAItiC,OAAO,CAACuiC,WAAW,IAAI,IAAI,EAAE;MAC/B,IAAI,CAACC,mBAAmB,CAACxiC,OAAO,CAACuiC,WAAW,EAAE,KAAK,EAAEt0B,CAAC,EAAEub,CAAC,EAAE,IAAI,CAAC;;;;IAIlE,IAAIxpB,OAAO,CAACyiC,SAAS,EAAE;MACrB,IAAI,CAAC7V,IAAI,EAAE;MACX,IAAI,CAAC5sB,OAAO,CAAC+Z,MAAM,EAAE;QACnB,IAAI,CAAC0D,WAAW,CAAC,IAAI,IAAI,CAACD,UAAU,IAAI,EAAE,CAAC,CAAC;;MAG9C,MAAMuP,SAAS,GACb,IAAI,CAAC6O,SAAS,GAAG,EAAE,GAAG,GAAG,GAAG52B,IAAI,CAAC4H,KAAK,CAAC,IAAI,CAACgvB,SAAS,GAAG,EAAE,CAAC;MAC7D,IAAI,CAAC7O,SAAS,CAACA,SAAS,CAAC;MAEzB,IAAI2V,KAAK,GAAIlZ,CAAC,GAAG,IAAI,CAACyS,iBAAiB,EAAE,GAAIlP,SAAS;MACtD,IAAI,CAAC7D,MAAM,CAACjb,CAAC,EAAEy0B,KAAK,CAAC;MACrB,IAAI,CAACpZ,MAAM,CAACrb,CAAC,GAAGm0B,aAAa,EAAEM,KAAK,CAAC;MACrC,IAAI,CAAC3oB,MAAM,EAAE;MACb,IAAI,CAAC8S,OAAO,EAAE;;;;IAIhB,IAAI7sB,OAAO,CAAC2iC,MAAM,EAAE;MAClB,IAAI,CAAC/V,IAAI,EAAE;MACX,IAAI,CAAC5sB,OAAO,CAAC+Z,MAAM,EAAE;QACnB,IAAI,CAAC0D,WAAW,CAAC,IAAI,IAAI,CAACD,UAAU,IAAI,EAAE,CAAC,CAAC;;MAG9C,MAAMuP,SAAS,GACb,IAAI,CAAC6O,SAAS,GAAG,EAAE,GAAG,GAAG,GAAG52B,IAAI,CAAC4H,KAAK,CAAC,IAAI,CAACgvB,SAAS,GAAG,EAAE,CAAC;MAC7D,IAAI,CAAC7O,SAAS,CAACA,SAAS,CAAC;MAEzB,IAAI2V,KAAK,GAAGlZ,CAAC,GAAG,IAAI,CAACyS,iBAAiB,EAAE,GAAG,CAAC;MAC5C,IAAI,CAAC/S,MAAM,CAACjb,CAAC,EAAEy0B,KAAK,CAAC;MACrB,IAAI,CAACpZ,MAAM,CAACrb,CAAC,GAAGm0B,aAAa,EAAEM,KAAK,CAAC;MACrC,IAAI,CAAC3oB,MAAM,EAAE;MACb,IAAI,CAAC8S,OAAO,EAAE;;IAGhB,IAAI,CAACD,IAAI,EAAE;;;IAGX,IAAI5sB,OAAO,CAAC4iC,OAAO,EAAE;MACnB,IAAIC,IAAI;MACR,IAAI,OAAO7iC,OAAO,CAAC4iC,OAAO,KAAK,QAAQ,EAAE;QACvCC,IAAI,GAAG,CAAC79B,IAAI,CAAC0hB,GAAG,CAAE1mB,OAAO,CAAC4iC,OAAO,GAAG59B,IAAI,CAAC0lB,EAAE,GAAI,GAAG,CAAC;OACpD,MAAM;QACLmY,IAAI,GAAG,CAAC,IAAI;;MAEd,IAAI,CAACjsB,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE3I,CAAC,EAAEub,CAAC,CAAC;MAChC,IAAI,CAAC5S,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEisB,IAAI,EAAE,CAAC,EAAE,CAACA,IAAI,GAAGprB,EAAE,EAAE,CAAC,CAAC;MAC5C,IAAI,CAACb,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC3I,CAAC,EAAE,CAACub,CAAC,CAAC;;;;IAIpC,IAAI,CAAC5S,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACiC,IAAI,CAAC1O,MAAM,CAAC;IAChDqf,CAAC,GAAG,IAAI,CAAC3Q,IAAI,CAAC1O,MAAM,GAAGqf,CAAC,GAAG/R,EAAE;;;IAG7B,IAAI,IAAI,CAACoB,IAAI,CAAC7N,KAAK,CAAC,IAAI,CAAC6wB,KAAK,CAAC95B,EAAE,CAAC,IAAI,IAAI,EAAE;MAC1C,IAAI,CAAC8W,IAAI,CAAC7N,KAAK,CAAC,IAAI,CAAC6wB,KAAK,CAAC95B,EAAE,CAAC,GAAG,IAAI,CAAC85B,KAAK,CAAC35B,GAAG,EAAE;;;;IAInD,IAAI,CAACuY,UAAU,CAAC,IAAI,CAAC;;;IAGrB,IAAI,CAACA,UAAU,CAAE,WAAU3V,QAAM,CAACmJ,CAAC,CAAE,IAAGnJ,QAAM,CAAC0kB,CAAC,CAAE,KAAI,CAAC;;;IAGvD,IAAI,CAAC/O,UAAU,CAAE,IAAG,IAAI,CAACohB,KAAK,CAAC95B,EAAG,IAAG+C,QAAM,CAAC,IAAI,CAAC82B,SAAS,CAAE,KAAI,CAAC;;;IAGjE,MAAMpnB,IAAI,GAAGxU,OAAO,CAAC4wB,IAAI,IAAI5wB,OAAO,CAAC+Z,MAAM,GAAG,CAAC,GAAG/Z,OAAO,CAAC+Z,MAAM,GAAG,CAAC,GAAG,CAAC;IACxE,IAAIvF,IAAI,EAAE;MACR,IAAI,CAACiG,UAAU,CAAE,GAAEjG,IAAK,KAAI,CAAC;;;;IAI/B,IAAIioB,gBAAgB,EAAE;MACpB,IAAI,CAAChiB,UAAU,CAAE,GAAE3V,QAAM,CAAC23B,gBAAgB,CAAE,KAAI,CAAC;;;;IAInD,IAAIF,iBAAiB,KAAK,GAAG,EAAE;MAC7B,IAAI,CAAC9hB,UAAU,CAAE,GAAE8hB,iBAAkB,KAAI,CAAC;;;;;;;IAO5C,IAAIG,WAAW,EAAE;MACfvpB,KAAK,GAAG6f,IAAI,CAACiP,IAAI,EAAE,CAACvQ,KAAK,CAAC,KAAK,CAAC;MAChCgL,WAAW,IAAI,IAAI,CAAC7I,aAAa,CAAC,GAAG,CAAC,GAAG4I,gBAAgB;MACzDC,WAAW,IAAI,IAAI,GAAG,IAAI,CAACd,SAAS;MAEpCvG,OAAO,GAAG,EAAE;MACZC,SAAS,GAAG,EAAE;MACd,KAAK,IAAIoI,IAAI,IAAIvqB,KAAK,EAAE;QACtB,MAAM,CAAC2vB,WAAW,EAAEC,aAAa,CAAC,GAAG,IAAI,CAAClH,KAAK,CAAC7jB,MAAM,CACpD0lB,IAAI,EACJ19B,OAAO,CAAC62B,QACV,CAAC;QACDxB,OAAO,GAAGA,OAAO,CAACnvB,MAAM,CAAC48B,WAAW,CAAC;QACrCxN,SAAS,GAAGA,SAAS,CAACpvB,MAAM,CAAC68B,aAAa,CAAC;;;;QAI3C,MAAM3lB,KAAK,GAAG,EAAE;QAChB,MAAM9Z,MAAM,GAAGgyB,SAAS,CAACA,SAAS,CAACv0B,MAAM,GAAG,CAAC,CAAC;QAC9C,KAAK,IAAIX,GAAG,IAAIkD,MAAM,EAAE;UACtB,MAAMjD,GAAG,GAAGiD,MAAM,CAAClD,GAAG,CAAC;UACvBgd,KAAK,CAAChd,GAAG,CAAC,GAAGC,GAAG;;QAElB+c,KAAK,CAACmY,QAAQ,IAAImH,WAAW;QAC7BpH,SAAS,CAACA,SAAS,CAACv0B,MAAM,GAAG,CAAC,CAAC,GAAGqc,KAAK;;KAE1C,MAAM;MACL,CAACiY,OAAO,EAAEC,SAAS,CAAC,GAAG,IAAI,CAACuG,KAAK,CAAC7jB,MAAM,CAACgb,IAAI,EAAEhzB,OAAO,CAAC62B,QAAQ,CAAC;;IAGlE,MAAMxF,KAAK,GAAG,IAAI,CAACuK,SAAS,GAAG,IAAI;IACnC,MAAM5S,QAAQ,GAAG,EAAE;IACnB,IAAI/nB,IAAI,GAAG,CAAC;IACZ,IAAI+hC,SAAS,GAAG,KAAK;;;IAGrB,MAAMC,UAAU,GAAGC,GAAG,IAAI;MACxB,IAAIjiC,IAAI,GAAGiiC,GAAG,EAAE;QACd,MAAMpmB,GAAG,GAAGuY,OAAO,CAACtyB,KAAK,CAAC9B,IAAI,EAAEiiC,GAAG,CAAC,CAAC3hC,IAAI,CAAC,EAAE,CAAC;QAC7C,MAAMq0B,OAAO,GACXN,SAAS,CAAC4N,GAAG,GAAG,CAAC,CAAC,CAAC3N,QAAQ,GAAGD,SAAS,CAAC4N,GAAG,GAAG,CAAC,CAAC,CAACvN,YAAY;QAC/D3M,QAAQ,CAAC9nB,IAAI,CAAE,IAAG4b,GAAI,KAAIhY,QAAM,CAAC,CAAC8wB,OAAO,CAAE,EAAC,CAAC;;MAG/C,OAAQ30B,IAAI,GAAGiiC,GAAG;KACnB;;;IAGD,MAAMC,KAAK,GAAG9/B,CAAC,IAAI;MACjB4/B,UAAU,CAAC5/B,CAAC,CAAC;MAEb,IAAI2lB,QAAQ,CAACjoB,MAAM,GAAG,CAAC,EAAE;QACvB,IAAI,CAAC0Z,UAAU,CAAE,IAAGuO,QAAQ,CAACznB,IAAI,CAAC,GAAG,CAAE,MAAK,CAAC;QAC7C,OAAQynB,QAAQ,CAACjoB,MAAM,GAAG,CAAC;;KAE9B;IAED,KAAKsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiyB,SAAS,CAACv0B,MAAM,EAAEsC,CAAC,EAAE,EAAE;;;MAGrC,MAAMyT,GAAG,GAAGwe,SAAS,CAACjyB,CAAC,CAAC;MACxB,IAAIyT,GAAG,CAAC2e,OAAO,IAAI3e,GAAG,CAAC4e,OAAO,EAAE;;QAE9ByN,KAAK,CAAC9/B,CAAC,CAAC;;;QAGR,IAAI,CAACoX,UAAU,CACZ,WAAU3V,QAAM,CAACmJ,CAAC,GAAG6I,GAAG,CAAC2e,OAAO,GAAGpE,KAAK,CAAE,IAAGvsB,QAAM,CAClD0kB,CAAC,GAAG1S,GAAG,CAAC4e,OAAO,GAAGrE,KACpB,CAAE,KACJ,CAAC;QACD8R,KAAK,CAAC9/B,CAAC,GAAG,CAAC,CAAC;QAEZ2/B,SAAS,GAAG,IAAI;OACjB,MAAM;;QAEL,IAAIA,SAAS,EAAE;UACb,IAAI,CAACvoB,UAAU,CAAE,WAAU3V,QAAM,CAACmJ,CAAC,CAAE,IAAGnJ,QAAM,CAAC0kB,CAAC,CAAE,KAAI,CAAC;UACvDwZ,SAAS,GAAG,KAAK;;;;QAInB,IAAIlsB,GAAG,CAACye,QAAQ,GAAGze,GAAG,CAAC6e,YAAY,KAAK,CAAC,EAAE;UACzCsN,UAAU,CAAC5/B,CAAC,GAAG,CAAC,CAAC;;;MAIrB4K,CAAC,IAAI6I,GAAG,CAACye,QAAQ,GAAGlE,KAAK;;;;IAI3B8R,KAAK,CAAC9/B,CAAC,CAAC;;;IAGR,IAAI,CAACoX,UAAU,CAAC,IAAI,CAAC;;;IAGrB,OAAO,IAAI,CAACoS,OAAO,EAAE;;AAEzB,CAAC;;ACpjBD,MAAMuW,OAAO,GAAG,CACd,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACP;AAED,MAAMC,eAAe,GAAG;EACtB,CAAC,EAAE,YAAY;EACf,CAAC,EAAE,WAAW;EACd,CAAC,EAAE;AACL,CAAC;AAED,MAAMC,IAAI,CAAC;EACTvjC,WAAWA,CAACqF,IAAI,EAAEg8B,KAAK,EAAE;IACvB,IAAImC,MAAM;IACV,IAAI,CAACn+B,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACg8B,KAAK,GAAGA,KAAK;IAClB,IAAI,IAAI,CAACh8B,IAAI,CAACo+B,YAAY,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;MACxC,MAAM,uBAAuB;;;;IAI/B,IAAI,CAACC,WAAW,GAAGC,IAAI,CAACC,UAAU,CAAC,IAAI,CAACv+B,IAAI,CAAC,CAACw+B,WAAW,IAAI,CAAC;IAE9D,IAAI9sB,GAAG,GAAG,CAAC;IACX,OAAOA,GAAG,GAAG,IAAI,CAAC1R,IAAI,CAACrE,MAAM,EAAE;MAC7BwiC,MAAM,GAAG,IAAI,CAACn+B,IAAI,CAACo+B,YAAY,CAAC1sB,GAAG,CAAC;MACpCA,GAAG,IAAI,CAAC;MACR,IAAIssB,OAAO,CAACra,QAAQ,CAACwa,MAAM,CAAC,EAAE;QAC5B;;MAEFzsB,GAAG,IAAI,IAAI,CAAC1R,IAAI,CAACo+B,YAAY,CAAC1sB,GAAG,CAAC;;IAGpC,IAAI,CAACssB,OAAO,CAACra,QAAQ,CAACwa,MAAM,CAAC,EAAE;MAC7B,MAAM,eAAe;;IAEvBzsB,GAAG,IAAI,CAAC;IAER,IAAI,CAAC+sB,IAAI,GAAG,IAAI,CAACz+B,IAAI,CAAC0R,GAAG,EAAE,CAAC;IAC5B,IAAI,CAAC3M,MAAM,GAAG,IAAI,CAAC/E,IAAI,CAACo+B,YAAY,CAAC1sB,GAAG,CAAC;IACzCA,GAAG,IAAI,CAAC;IAER,IAAI,CAAC5M,KAAK,GAAG,IAAI,CAAC9E,IAAI,CAACo+B,YAAY,CAAC1sB,GAAG,CAAC;IACxCA,GAAG,IAAI,CAAC;IAER,MAAMgtB,QAAQ,GAAG,IAAI,CAAC1+B,IAAI,CAAC0R,GAAG,EAAE,CAAC;IACjC,IAAI,CAACitB,UAAU,GAAGV,eAAe,CAACS,QAAQ,CAAC;IAE3C,IAAI,CAAC/vB,GAAG,GAAG,IAAI;;EAGjB2D,KAAKA,CAACvS,QAAQ,EAAE;IACd,IAAI,IAAI,CAAC4O,GAAG,EAAE;MACZ;;IAGF,IAAI,CAACA,GAAG,GAAG5O,QAAQ,CAACjD,GAAG,CAAC;MACtBsI,IAAI,EAAE,SAAS;MACfuO,OAAO,EAAE,OAAO;MAChBirB,gBAAgB,EAAE,IAAI,CAACH,IAAI;MAC3BI,KAAK,EAAE,IAAI,CAAC/5B,KAAK;MACjBg6B,MAAM,EAAE,IAAI,CAAC/5B,MAAM;MACnBsB,UAAU,EAAE,IAAI,CAACs4B,UAAU;MAC3Bx+B,MAAM,EAAE;KACT,CAAC;;;;;IAKF,IAAI,IAAI,CAACw+B,UAAU,KAAK,YAAY,EAAE;MACpC,IAAI,CAAChwB,GAAG,CAAC3O,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;;IAGpE,IAAI,CAAC2O,GAAG,CAACpR,GAAG,CAAC,IAAI,CAACyC,IAAI,CAAC;;;IAGvB,OAAQ,IAAI,CAACA,IAAI,GAAG,IAAI;;AAE5B;;AC1FA,MAAM++B,QAAQ,CAAC;EACbpkC,WAAWA,CAACqF,IAAI,EAAEg8B,KAAK,EAAE;IACvB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACgD,KAAK,GAAG,IAAIC,GAAG,CAACj/B,IAAI,CAAC;IAC1B,IAAI,CAAC8E,KAAK,GAAG,IAAI,CAACk6B,KAAK,CAACl6B,KAAK;IAC7B,IAAI,CAACC,MAAM,GAAG,IAAI,CAACi6B,KAAK,CAACj6B,MAAM;IAC/B,IAAI,CAACm6B,OAAO,GAAG,IAAI,CAACF,KAAK,CAACE,OAAO;IACjC,IAAI,CAACvwB,GAAG,GAAG,IAAI;;EAGjB2D,KAAKA,CAACvS,QAAQ,EAAE;IACd,IAAIo/B,WAAW,GAAG,KAAK;IAEvB,IAAI,CAACp/B,QAAQ,GAAGA,QAAQ;IACxB,IAAI,IAAI,CAAC4O,GAAG,EAAE;MACZ;;IAGF,MAAMywB,eAAe,GAAG,IAAI,CAACJ,KAAK,CAACI,eAAe;IAClD,MAAMC,YAAY,GAAG,IAAI,CAACL,KAAK,CAACM,eAAe,KAAK,CAAC;IAErD,IAAI,CAAC3wB,GAAG,GAAG,IAAI,CAAC5O,QAAQ,CAACjD,GAAG,CAAC;MAC3BsI,IAAI,EAAE,SAAS;MACfuO,OAAO,EAAE,OAAO;MAChBirB,gBAAgB,EAAEQ,eAAe,GAAG,CAAC,GAAG,IAAI,CAACJ,KAAK,CAACP,IAAI;MACvDI,KAAK,EAAE,IAAI,CAAC/5B,KAAK;MACjBg6B,MAAM,EAAE,IAAI,CAAC/5B,MAAM;MACnB5E,MAAM,EAAE;KACT,CAAC;IAEF,IAAI,CAACi/B,eAAe,EAAE;MACpB,MAAM1b,MAAM,GAAG,IAAI,CAAC3jB,QAAQ,CAACjD,GAAG,CAAC;QAC/ByiC,SAAS,EAAEF,YAAY,GAAG,CAAC,GAAG,EAAE;QAChCG,MAAM,EAAE,IAAI,CAACR,KAAK,CAACS,MAAM;QACzBb,gBAAgB,EAAE,IAAI,CAACI,KAAK,CAACP,IAAI;QACjCiB,OAAO,EAAE,IAAI,CAAC56B;OACf,CAAC;MAEF,IAAI,CAAC6J,GAAG,CAAC3O,IAAI,CAAC,aAAa,CAAC,GAAG0jB,MAAM;MACrCA,MAAM,CAACnmB,GAAG,EAAE;;IAGd,IAAI,IAAI,CAACyhC,KAAK,CAACW,OAAO,CAAChkC,MAAM,KAAK,CAAC,EAAE;MACnC,IAAI,CAACgT,GAAG,CAAC3O,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAACg/B,KAAK,CAACL,UAAU;KACpD,MAAM;;MAEL,MAAMgB,OAAO,GAAG,IAAI,CAAC5/B,QAAQ,CAACjD,GAAG,EAAE;MACnC6iC,OAAO,CAACpiC,GAAG,CAACkB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACsgC,KAAK,CAACW,OAAO,CAAC,CAAC;;;MAG5C,IAAI,CAAChxB,GAAG,CAAC3O,IAAI,CAAC,YAAY,CAAC,GAAG,CAC5B,SAAS,EACT,WAAW,EACX,IAAI,CAACg/B,KAAK,CAACW,OAAO,CAAChkC,MAAM,GAAG,CAAC,GAAG,CAAC,EACjCgkC,OAAO,CACR;;;;;IAKH,IAAI,IAAI,CAACX,KAAK,CAACY,YAAY,CAACC,SAAS,IAAI,IAAI,EAAE;;;MAG7C,MAAM5kC,GAAG,GAAG,IAAI,CAAC+jC,KAAK,CAACY,YAAY,CAACC,SAAS;MAC7C,IAAI,CAAClxB,GAAG,CAAC3O,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC/E,GAAG,EAAEA,GAAG,CAAC;KACnC,MAAM,IAAI,IAAI,CAAC+jC,KAAK,CAACY,YAAY,CAACE,GAAG,EAAE;;;MAGtC,MAAM;QAAEA;OAAK,GAAG,IAAI,CAACd,KAAK,CAACY,YAAY;MACvC,MAAMG,IAAI,GAAG,EAAE;MACf,KAAK,IAAIl3B,CAAC,IAAIi3B,GAAG,EAAE;QACjBC,IAAI,CAACjkC,IAAI,CAAC+M,CAAC,EAAEA,CAAC,CAAC;;MAGjB,IAAI,CAAC8F,GAAG,CAAC3O,IAAI,CAAC,MAAM,CAAC,GAAG+/B,IAAI;KAC7B,MAAM,IAAI,IAAI,CAACf,KAAK,CAACY,YAAY,CAACI,OAAO,EAAE;;;MAG1Cb,WAAW,GAAG,IAAI;MAClB,OAAO,IAAI,CAACc,uBAAuB,EAAE;KACtC,MAAM,IAAIb,eAAe,EAAE;;;;MAI1BD,WAAW,GAAG,IAAI;MAClB,OAAO,IAAI,CAACe,iBAAiB,EAAE;;IAGjC,IAAIb,YAAY,IAAI,CAACF,WAAW,EAAE;MAChC,OAAO,IAAI,CAACgB,UAAU,EAAE;;IAG1B,IAAI,CAAC1/B,QAAQ,EAAE;;EAGjBA,QAAQA,GAAG;IACT,IAAI,IAAI,CAAC2/B,YAAY,EAAE;MACrB,MAAMC,KAAK,GAAG,IAAI,CAACtgC,QAAQ,CAACjD,GAAG,CAAC;QAC9BsI,IAAI,EAAE,SAAS;QACfuO,OAAO,EAAE,OAAO;QAChBmrB,MAAM,EAAE,IAAI,CAAC/5B,MAAM;QACnB85B,KAAK,EAAE,IAAI,CAAC/5B,KAAK;QACjB85B,gBAAgB,EAAE,CAAC;QACnBz+B,MAAM,EAAE,aAAa;QACrBkG,UAAU,EAAE,YAAY;QACxBi6B,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;OACd,CAAC;MAEFD,KAAK,CAAC9iC,GAAG,CAAC,IAAI,CAAC6iC,YAAY,CAAC;MAC5B,IAAI,CAACzxB,GAAG,CAAC3O,IAAI,CAAC,OAAO,CAAC,GAAGqgC,KAAK;;;;IAIhC,IAAI,CAAC1xB,GAAG,CAACpR,GAAG,CAAC,IAAI,CAAC2hC,OAAO,CAAC;;;IAG1B,IAAI,CAACF,KAAK,GAAG,IAAI;IACjB,OAAQ,IAAI,CAACE,OAAO,GAAG,IAAI;;EAG7BgB,iBAAiBA,GAAG;IAClB,OAAO,IAAI,CAAClB,KAAK,CAACuB,YAAY,CAACC,MAAM,IAAI;MACvC,IAAIjlC,CAAC,EAAEklC,CAAC;MACR,MAAMC,UAAU,GAAG,IAAI,CAAC1B,KAAK,CAACS,MAAM;MACpC,MAAMkB,UAAU,GAAG,IAAI,CAAC77B,KAAK,GAAG,IAAI,CAACC,MAAM;MAC3C,MAAMm6B,OAAO,GAAGzgC,MAAM,CAACoS,KAAK,CAAC8vB,UAAU,GAAGD,UAAU,CAAC;MACrD,MAAMN,YAAY,GAAG3hC,MAAM,CAACoS,KAAK,CAAC8vB,UAAU,CAAC;MAE7C,IAAI1iC,CAAC,GAAIwiC,CAAC,GAAGllC,CAAC,GAAG,CAAE;MACnB,MAAMuyB,GAAG,GAAG0S,MAAM,CAAC7kC,MAAM;;MAEzB,MAAMilC,aAAa,GAAG,IAAI,CAAC5B,KAAK,CAACP,IAAI,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC;MACpD,OAAOxgC,CAAC,GAAG6vB,GAAG,EAAE;QACd,KAAK,IAAI+S,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGH,UAAU,EAAEG,UAAU,EAAE,EAAE;UAC9D3B,OAAO,CAACuB,CAAC,EAAE,CAAC,GAAGD,MAAM,CAACviC,CAAC,EAAE,CAAC;UAC1BA,CAAC,IAAI2iC,aAAa;;QAEpBR,YAAY,CAAC7kC,CAAC,EAAE,CAAC,GAAGilC,MAAM,CAACviC,CAAC,EAAE,CAAC;QAC/BA,CAAC,IAAI2iC,aAAa;;MAGpB,IAAI,CAAC1B,OAAO,GAAGn+B,IAAI,CAACC,WAAW,CAACk+B,OAAO,CAAC;MACxC,IAAI,CAACkB,YAAY,GAAGr/B,IAAI,CAACC,WAAW,CAACo/B,YAAY,CAAC;MAClD,OAAO,IAAI,CAAC3/B,QAAQ,EAAE;KACvB,CAAC;;EAGJw/B,uBAAuBA,GAAG;IACxB,MAAML,YAAY,GAAG,IAAI,CAACZ,KAAK,CAACY,YAAY,CAACI,OAAO;IACpD,OAAO,IAAI,CAAChB,KAAK,CAACuB,YAAY,CAACC,MAAM,IAAI;MACvC,MAAMJ,YAAY,GAAG3hC,MAAM,CAACoS,KAAK,CAAC,IAAI,CAAC/L,KAAK,GAAG,IAAI,CAACC,MAAM,CAAC;MAE3D,IAAI9G,CAAC,GAAG,CAAC;MACT,KAAK,IAAIoS,CAAC,GAAG,CAAC,EAAE9S,GAAG,GAAGijC,MAAM,CAAC7kC,MAAM,EAAE0U,CAAC,GAAG9S,GAAG,EAAE8S,CAAC,EAAE,EAAE;QACjD+vB,YAAY,CAACniC,CAAC,EAAE,CAAC,GAAG2hC,YAAY,CAACY,MAAM,CAACnwB,CAAC,CAAC,CAAC;;MAG7C,IAAI,CAAC+vB,YAAY,GAAGr/B,IAAI,CAACC,WAAW,CAACo/B,YAAY,CAAC;MAClD,OAAO,IAAI,CAAC3/B,QAAQ,EAAE;KACvB,CAAC;;EAGJ0/B,UAAUA,GAAG;IACX,IAAI,CAACnB,KAAK,CAACuB,YAAY,CAACC,MAAM,IAAI;MAChC,IAAI,CAACtB,OAAO,GAAGn+B,IAAI,CAACC,WAAW,CAACw/B,MAAM,CAAC;MACvC,IAAI,CAAC//B,QAAQ,EAAE;KAChB,CAAC;;AAEN;;AC3KA;AACA;AACA;AACA;AAMA,MAAMqgC,QAAQ,CAAC;EACb,OAAOtU,IAAIA,CAACiJ,GAAG,EAAEuG,KAAK,EAAE;IACtB,IAAIh8B,IAAI;IACR,IAAIvB,MAAM,CAACK,QAAQ,CAAC22B,GAAG,CAAC,EAAE;MACxBz1B,IAAI,GAAGy1B,GAAG;KACX,MAAM,IAAIA,GAAG,YAAYI,WAAW,EAAE;MACrC71B,IAAI,GAAGvB,MAAM,CAACC,IAAI,CAAC,IAAIk3B,UAAU,CAACH,GAAG,CAAC,CAAC;KACxC,MAAM;MACL,IAAI/H,KAAK;MACT,IAAKA,KAAK,GAAG,wBAAwB,CAACqT,IAAI,CAACtL,GAAG,CAAC,EAAG;QAChDz1B,IAAI,GAAGvB,MAAM,CAACC,IAAI,CAACgvB,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC;OACvC,MAAM;QACL1tB,IAAI,GAAG0sB,EAAE,CAACC,YAAY,CAAC8I,GAAG,CAAC;QAC3B,IAAI,CAACz1B,IAAI,EAAE;UACT;;;;IAKN,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;MACxC,OAAO,IAAIk+B,IAAI,CAACl+B,IAAI,EAAEg8B,KAAK,CAAC;KAC7B,MAAM,IAAIh8B,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIA,IAAI,CAACxF,QAAQ,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,EAAE;MACrE,OAAO,IAAIykC,QAAG,CAACj/B,IAAI,EAAEg8B,KAAK,CAAC;KAC5B,MAAM;MACL,MAAM,IAAIvhC,KAAK,CAAC,uBAAuB,CAAC;;;AAG9C;;AClCA,kBAAe;EACbumC,UAAUA,GAAG;IACX,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,OAAQ,IAAI,CAACC,WAAW,GAAG,CAAC;GAC7B;EAEDlC,KAAKA,CAACvJ,GAAG,EAAE5sB,CAAC,EAAEub,CAAC,EAAExpB,OAAO,GAAG,EAAE,EAAE;IAC7B,IAAIumC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAErC,KAAK,EAAEsC,EAAE,EAAEjgC,IAAI,EAAEkgC,KAAK,EAAEC,WAAW,EAAEC,OAAO,EAAEC,OAAO;IACrE,IAAI,OAAO74B,CAAC,KAAK,QAAQ,EAAE;MACzBjO,OAAO,GAAGiO,CAAC;MACXA,CAAC,GAAG,IAAI;;;;IAIV,MAAM84B,iBAAiB,GACrB/mC,OAAO,CAAC+mC,iBAAiB,IACxB/mC,OAAO,CAAC+mC,iBAAiB,KAAK,KAAK,IAAI,IAAI,CAAC/mC,OAAO,CAAC+mC,iBAAkB;IAEzE94B,CAAC,GAAG,CAACxH,IAAI,GAAGwH,CAAC,IAAI,IAAI,GAAGA,CAAC,GAAGjO,OAAO,CAACiO,CAAC,KAAK,IAAI,GAAGxH,IAAI,GAAG,IAAI,CAACwH,CAAC;IAC9Dub,CAAC,GAAG,CAACmd,KAAK,GAAGnd,CAAC,IAAI,IAAI,GAAGA,CAAC,GAAGxpB,OAAO,CAACwpB,CAAC,KAAK,IAAI,GAAGmd,KAAK,GAAG,IAAI,CAACnd,CAAC;IAEhE,IAAI,OAAOqR,GAAG,KAAK,QAAQ,EAAE;MAC3BuJ,KAAK,GAAG,IAAI,CAACiC,cAAc,CAACxL,GAAG,CAAC;;IAGlC,IAAI,CAACuJ,KAAK,EAAE;MACV,IAAIvJ,GAAG,CAAC3wB,KAAK,IAAI2wB,GAAG,CAAC1wB,MAAM,EAAE;QAC3Bi6B,KAAK,GAAGvJ,GAAG;OACZ,MAAM;QACLuJ,KAAK,GAAG,IAAI,CAAC4C,SAAS,CAACnM,GAAG,CAAC;;;IAI/B,IAAI,CAACuJ,KAAK,CAACrwB,GAAG,EAAE;MACdqwB,KAAK,CAAC1sB,KAAK,CAAC,IAAI,CAAC;;IAGnB,IAAI,IAAI,CAACmB,IAAI,CAAC3N,QAAQ,CAACk5B,KAAK,CAAChD,KAAK,CAAC,IAAI,IAAI,EAAE;MAC3C,IAAI,CAACvoB,IAAI,CAAC3N,QAAQ,CAACk5B,KAAK,CAAChD,KAAK,CAAC,GAAGgD,KAAK,CAACrwB,GAAG;;IAG7C,IAAI;MAAE7J,KAAK;MAAEC;KAAQ,GAAGi6B,KAAK;;;IAG7B,IAAI,CAAC2C,iBAAiB,IAAI3C,KAAK,CAACX,WAAW,GAAG,CAAC,EAAE;MAC/C,CAACv5B,KAAK,EAAEC,MAAM,CAAC,GAAG,CAACA,MAAM,EAAED,KAAK,CAAC;;IAGnC,IAAI8iB,CAAC,GAAGhtB,OAAO,CAACkK,KAAK,IAAIA,KAAK;IAC9B,IAAI4d,CAAC,GAAG9nB,OAAO,CAACmK,MAAM,IAAIA,MAAM;IAEhC,IAAInK,OAAO,CAACkK,KAAK,IAAI,CAAClK,OAAO,CAACmK,MAAM,EAAE;MACpC,MAAM88B,EAAE,GAAGja,CAAC,GAAG9iB,KAAK;MACpB8iB,CAAC,GAAG9iB,KAAK,GAAG+8B,EAAE;MACdnf,CAAC,GAAG3d,MAAM,GAAG88B,EAAE;KAChB,MAAM,IAAIjnC,OAAO,CAACmK,MAAM,IAAI,CAACnK,OAAO,CAACkK,KAAK,EAAE;MAC3C,MAAMg9B,EAAE,GAAGpf,CAAC,GAAG3d,MAAM;MACrB6iB,CAAC,GAAG9iB,KAAK,GAAGg9B,EAAE;MACdpf,CAAC,GAAG3d,MAAM,GAAG+8B,EAAE;KAChB,MAAM,IAAIlnC,OAAO,CAACqxB,KAAK,EAAE;MACxBrE,CAAC,GAAG9iB,KAAK,GAAGlK,OAAO,CAACqxB,KAAK;MACzBvJ,CAAC,GAAG3d,MAAM,GAAGnK,OAAO,CAACqxB,KAAK;KAC3B,MAAM,IAAIrxB,OAAO,CAACmnC,GAAG,EAAE;MACtB,CAACV,EAAE,EAAEF,EAAE,CAAC,GAAGvmC,OAAO,CAACmnC,GAAG;MACtBX,EAAE,GAAGC,EAAE,GAAGF,EAAE;MACZG,EAAE,GAAGx8B,KAAK,GAAGC,MAAM;MACnB,IAAIu8B,EAAE,GAAGF,EAAE,EAAE;QACXxZ,CAAC,GAAGyZ,EAAE;QACN3e,CAAC,GAAG2e,EAAE,GAAGC,EAAE;OACZ,MAAM;QACL5e,CAAC,GAAGye,EAAE;QACNvZ,CAAC,GAAGuZ,EAAE,GAAGG,EAAE;;KAEd,MAAM,IAAI1mC,OAAO,CAAConC,KAAK,EAAE;MACxB,CAACX,EAAE,EAAEF,EAAE,CAAC,GAAGvmC,OAAO,CAAConC,KAAK;MACxBZ,EAAE,GAAGC,EAAE,GAAGF,EAAE;MACZG,EAAE,GAAGx8B,KAAK,GAAGC,MAAM;MACnB,IAAIu8B,EAAE,GAAGF,EAAE,EAAE;QACX1e,CAAC,GAAGye,EAAE;QACNvZ,CAAC,GAAGuZ,EAAE,GAAGG,EAAE;OACZ,MAAM;QACL1Z,CAAC,GAAGyZ,EAAE;QACN3e,CAAC,GAAG2e,EAAE,GAAGC,EAAE;;;IAIf,IAAI1mC,OAAO,CAACmnC,GAAG,IAAInnC,OAAO,CAAConC,KAAK,EAAE;MAChC,IAAIpnC,OAAO,CAACs9B,KAAK,KAAK,QAAQ,EAAE;QAC9BrvB,CAAC,GAAGA,CAAC,GAAGw4B,EAAE,GAAG,CAAC,GAAGzZ,CAAC,GAAG,CAAC;OACvB,MAAM,IAAIhtB,OAAO,CAACs9B,KAAK,KAAK,OAAO,EAAE;QACpCrvB,CAAC,GAAGA,CAAC,GAAGw4B,EAAE,GAAGzZ,CAAC;;MAGhB,IAAIhtB,OAAO,CAACqnC,MAAM,KAAK,QAAQ,EAAE;QAC/B7d,CAAC,GAAGA,CAAC,GAAG+c,EAAE,GAAG,CAAC,GAAGze,CAAC,GAAG,CAAC;OACvB,MAAM,IAAI9nB,OAAO,CAACqnC,MAAM,KAAK,QAAQ,EAAE;QACtC7d,CAAC,GAAGA,CAAC,GAAG+c,EAAE,GAAGze,CAAC;;;IAIlB,IAAI,CAACif,iBAAiB,EAAE;MACtB,QAAQ3C,KAAK,CAACX,WAAW;;QAEvB;QACA,KAAK,CAAC;UACJ3b,CAAC,GAAG,CAACA,CAAC;UACN0B,CAAC,IAAI1B,CAAC;UAEN8e,WAAW,GAAG,CAAC;UACf;;QAEF,KAAK,CAAC;UACJ5Z,CAAC,GAAG,CAACA,CAAC;UACNlF,CAAC,GAAG,CAACA,CAAC;UACN7Z,CAAC,IAAI+e,CAAC;UACNxD,CAAC,IAAI1B,CAAC;UAEN8e,WAAW,GAAG,CAAC;UACf;;QAEF,KAAK,CAAC;UACJC,OAAO,GAAG54B,CAAC;UACX64B,OAAO,GAAGtd,CAAC;UAEX1B,CAAC,GAAG,CAACA,CAAC;UACN7Z,CAAC,IAAI+e,CAAC;UAEN4Z,WAAW,GAAG,GAAG;UACjB;;QAEF,KAAK,CAAC;;;UAGJ;;QAEF,KAAK,CAAC;UACJC,OAAO,GAAG54B,CAAC;UACX64B,OAAO,GAAGtd,CAAC;UAEX,CAACwD,CAAC,EAAElF,CAAC,CAAC,GAAG,CAACA,CAAC,EAAEkF,CAAC,CAAC;UACfxD,CAAC,IAAI1B,CAAC;UAEN8e,WAAW,GAAG,EAAE;UAChB;;QAEF,KAAK,CAAC;UACJC,OAAO,GAAG54B,CAAC;UACX64B,OAAO,GAAGtd,CAAC;UAEX,CAACwD,CAAC,EAAElF,CAAC,CAAC,GAAG,CAACA,CAAC,EAAEkF,CAAC,CAAC;UACflF,CAAC,GAAG,CAACA,CAAC;UAEN8e,WAAW,GAAG,EAAE;UAChB;;QAEF,KAAK,CAAC;UACJC,OAAO,GAAG54B,CAAC;UACX64B,OAAO,GAAGtd,CAAC;UAEX,CAACwD,CAAC,EAAElF,CAAC,CAAC,GAAG,CAACA,CAAC,EAAEkF,CAAC,CAAC;UACflF,CAAC,GAAG,CAACA,CAAC;UACNkF,CAAC,GAAG,CAACA,CAAC;UACN/e,CAAC,IAAI+e,CAAC;UAEN4Z,WAAW,GAAG,EAAE;UAChB;;QAEF,KAAK,CAAC;UACJC,OAAO,GAAG54B,CAAC;UACX64B,OAAO,GAAGtd,CAAC;UAEX,CAACwD,CAAC,EAAElF,CAAC,CAAC,GAAG,CAACA,CAAC,EAAEkF,CAAC,CAAC;UACflF,CAAC,GAAG,CAACA,CAAC;UACN7Z,CAAC,IAAI+e,CAAC;UACNxD,CAAC,IAAI1B,CAAC;UAEN8e,WAAW,GAAG,CAAC,EAAE;UACjB;;KAEL,MAAM;MACL9e,CAAC,GAAG,CAACA,CAAC;MACN0B,CAAC,IAAI1B,CAAC;MACN8e,WAAW,GAAG,CAAC;;;;IAIjB,IAAI5mC,OAAO,CAACqiC,IAAI,IAAI,IAAI,EAAE;MACxB,IAAI,CAACA,IAAI,CAACp0B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAACqiC,IAAI,CAAC;;IAErC,IAAIriC,OAAO,CAACsiC,IAAI,IAAI,IAAI,EAAE;MACxB,IAAI,CAACA,IAAI,CAACr0B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAACsiC,IAAI,CAAC;;IAErC,IAAItiC,OAAO,CAACuiC,WAAW,IAAI,IAAI,EAAE;MAC/B,IAAI,CAACC,mBAAmB,CAACxiC,OAAO,CAACuiC,WAAW,EAAE,KAAK,EAAEt0B,CAAC,EAAEub,CAAC,EAAE,IAAI,CAAC;;;;IAIlE,IAAI,IAAI,CAACA,CAAC,KAAKA,CAAC,EAAE;MAChB,IAAI,CAACA,CAAC,IAAI1B,CAAC;;IAGb,IAAI,CAAC8E,IAAI,EAAE;IAEX,IAAIga,WAAW,EAAE;MACf,IAAI,CAAC3V,MAAM,CAAC2V,WAAW,EAAE;QACvBxV,MAAM,EAAE,CAACyV,OAAO,EAAEC,OAAO;OAC1B,CAAC;;IAGJ,IAAI,CAAClwB,SAAS,CAACoW,CAAC,EAAE,CAAC,EAAE,CAAC,EAAElF,CAAC,EAAE7Z,CAAC,EAAEub,CAAC,CAAC;IAChC,IAAI,CAAC/O,UAAU,CAAE,IAAG2pB,KAAK,CAAChD,KAAM,KAAI,CAAC;IACrC,IAAI,CAACvU,OAAO,EAAE;IAEd,OAAO,IAAI;GACZ;EAEDma,SAASA,CAACnM,GAAG,EAAE;IACb,IAAIuJ,KAAK;IACT,IAAI,OAAOvJ,GAAG,KAAK,QAAQ,EAAE;MAC3BuJ,KAAK,GAAG,IAAI,CAACiC,cAAc,CAACxL,GAAG,CAAC;;IAGlC,IAAI,CAACuJ,KAAK,EAAE;MACVA,KAAK,GAAG8B,QAAQ,CAACtU,IAAI,CAACiJ,GAAG,EAAG,IAAG,EAAE,IAAI,CAACyL,WAAY,EAAC,CAAC;MACpD,IAAI,OAAOzL,GAAG,KAAK,QAAQ,EAAE;QAC3B,IAAI,CAACwL,cAAc,CAACxL,GAAG,CAAC,GAAGuJ,KAAK;;;IAIpC,OAAOA,KAAK;;AAEhB,CAAC;;ACzOD,uBAAe;EACbkD,QAAQA,CAACr5B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,EAAE;IAC5BA,OAAO,CAACwK,IAAI,GAAG,OAAO;IACtBxK,OAAO,CAACunC,IAAI,GAAG,IAAI,CAACC,YAAY,CAACv5B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,CAAC;IAC5C9nB,OAAO,CAACynC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1B,IAAIznC,OAAO,CAAC+Y,OAAO,KAAK,MAAM,IAAI,OAAO/Y,OAAO,CAAC0nC,CAAC,KAAK,WAAW,EAAE;MAClE1nC,OAAO,CAAC0nC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;IAGrB,IAAI1nC,OAAO,CAAC+Y,OAAO,KAAK,MAAM,EAAE;MAC9B,IAAI/Y,OAAO,CAAC2B,CAAC,IAAI,IAAI,EAAE;QACrB3B,OAAO,CAAC2B,CAAC,GAAG,IAAI,CAACqV,eAAe,CAAChX,OAAO,CAACmM,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;KAE/D;IACD,OAAOnM,OAAO,CAACmM,KAAK;IAEpB,IAAI,OAAOnM,OAAO,CAAC2nC,IAAI,KAAK,QAAQ,EAAE;MACpC3nC,OAAO,CAAC2nC,IAAI,GAAG,IAAInkC,MAAM,CAACxD,OAAO,CAAC2nC,IAAI,CAAC;;;;IAIzC,KAAK,IAAIvnC,GAAG,IAAIJ,OAAO,EAAE;MACvB,MAAMK,GAAG,GAAGL,OAAO,CAACI,GAAG,CAAC;MACxBJ,OAAO,CAACI,GAAG,CAAC,CAAC,CAAC,CAAC6J,WAAW,EAAE,GAAG7J,GAAG,CAAC2C,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG1C,GAAG;;IAGpD,MAAM6B,GAAG,GAAG,IAAI,CAACA,GAAG,CAAClC,OAAO,CAAC;IAC7B,IAAI,CAAC6Y,IAAI,CAACnN,WAAW,CAACxK,IAAI,CAACgB,GAAG,CAAC;IAC/BA,GAAG,CAACS,GAAG,EAAE;IACT,OAAO,IAAI;GACZ;EAEDilC,IAAIA,CAAC35B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAEkK,QAAQ,EAAEhyB,OAAO,GAAG,EAAE,EAAE;IACvCA,OAAO,CAAC+Y,OAAO,GAAG,MAAM;IACxB/Y,OAAO,CAAC6K,QAAQ,GAAG,IAAIrH,MAAM,CAACwuB,QAAQ,CAAC;IACvC,IAAIhyB,OAAO,CAAC6nC,IAAI,IAAI,IAAI,EAAE;MACxB7nC,OAAO,CAAC6nC,IAAI,GAAG,SAAS;;IAE1B,IAAI7nC,OAAO,CAACmM,KAAK,IAAI,IAAI,EAAE;MACzBnM,OAAO,CAACmM,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;;IAEhC,OAAO,IAAI,CAACm7B,QAAQ,CAACr5B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GAC1C;EAEDsiC,IAAIA,CAACr0B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAEpmB,IAAI,EAAE1B,OAAO,GAAG,EAAE,EAAE;IACnCA,OAAO,CAAC+Y,OAAO,GAAG,MAAM;IACxB/Y,OAAO,CAAC4nB,CAAC,GAAG,IAAI,CAAC1lB,GAAG,CAAC;MACnBiX,CAAC,EAAE,MAAM;MACT2uB,CAAC,EAAE,IAAItkC,MAAM,CAAC9B,IAAI;KACnB,CAAC;IACF1B,OAAO,CAAC4nB,CAAC,CAACjlB,GAAG,EAAE;IACf,OAAO,IAAI,CAAC2kC,QAAQ,CAACr5B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GAC1C;EAEDqiC,IAAIA,CAACp0B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAEigB,GAAG,EAAE/nC,OAAO,GAAG,EAAE,EAAE;IAClCA,OAAO,CAAC+Y,OAAO,GAAG,MAAM;IAExB,IAAI,OAAOgvB,GAAG,KAAK,QAAQ,EAAE;;MAE3B,MAAMC,KAAK,GAAG,IAAI,CAACt9B,KAAK,CAACtF,IAAI,CAACuF,KAAK,CAACvF,IAAI;MACxC,IAAI2iC,GAAG,IAAI,CAAC,IAAIA,GAAG,GAAGC,KAAK,CAACC,IAAI,CAAClnC,MAAM,EAAE;QACvCf,OAAO,CAAC4nB,CAAC,GAAG,IAAI,CAAC1lB,GAAG,CAAC;UACnBiX,CAAC,EAAE,MAAM;UACT2uB,CAAC,EAAE,CAACE,KAAK,CAACC,IAAI,CAACF,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;SAC7C,CAAC;QACF/nC,OAAO,CAAC4nB,CAAC,CAACjlB,GAAG,EAAE;OAChB,MAAM;QACL,MAAM,IAAI9C,KAAK,CAAE,4BAA2BkoC,GAAI,EAAC,CAAC;;KAErD,MAAM;;MAEL/nC,OAAO,CAAC4nB,CAAC,GAAG,IAAI,CAAC1lB,GAAG,CAAC;QACnBiX,CAAC,EAAE,KAAK;QACR+uB,GAAG,EAAE,IAAI1kC,MAAM,CAACukC,GAAG;OACpB,CAAC;MACF/nC,OAAO,CAAC4nB,CAAC,CAACjlB,GAAG,EAAE;;IAGjB,OAAO,IAAI,CAAC2kC,QAAQ,CAACr5B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GAC1C;EAEDmoC,OAAOA,CAACl6B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,GAAG,EAAE,EAAE;IAChC,MAAM,CAAC2a,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAI,CAAC0sB,YAAY,CAACv5B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,CAAC;IACtD9nB,OAAO,CAACooC,UAAU,GAAG,CAACztB,EAAE,EAAEG,EAAE,EAAED,EAAE,EAAEC,EAAE,EAAEH,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAED,EAAE,CAAC;IACrD5a,OAAO,CAAC6K,QAAQ,GAAG,IAAIrH,MAAM,EAAE;IAC/B,OAAO,IAAI,CAAC8jC,QAAQ,CAACr5B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GAC1C;EAEDqoC,SAASA,CAACp6B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,GAAG,EAAE,EAAE;IAClCA,OAAO,CAAC+Y,OAAO,GAAG,WAAW;IAC7B,IAAI/Y,OAAO,CAACmM,KAAK,IAAI,IAAI,EAAE;MACzBnM,OAAO,CAACmM,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;;IAEjC,OAAO,IAAI,CAACg8B,OAAO,CAACl6B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GACzC;EAEDyiC,SAASA,CAACx0B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,GAAG,EAAE,EAAE;IAClCA,OAAO,CAAC+Y,OAAO,GAAG,WAAW;IAC7B,OAAO,IAAI,CAACovB,OAAO,CAACl6B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GACzC;EAED2iC,MAAMA,CAAC10B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,GAAG,EAAE,EAAE;IAC/BA,OAAO,CAAC+Y,OAAO,GAAG,WAAW;IAC7B,OAAO,IAAI,CAACovB,OAAO,CAACl6B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GACzC;EAEDsoC,cAAcA,CAAC3tB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE9a,OAAO,GAAG,EAAE,EAAE;IAC3CA,OAAO,CAAC+Y,OAAO,GAAG,MAAM;IACxB/Y,OAAO,CAAC6K,QAAQ,GAAG,IAAIrH,MAAM,EAAE;IAC/BxD,OAAO,CAAC+nB,CAAC,GAAG,CAACpN,EAAE,EAAE,IAAI,CAAC9B,IAAI,CAAC1O,MAAM,GAAGyQ,EAAE,EAAEC,EAAE,EAAE,IAAI,CAAChC,IAAI,CAAC1O,MAAM,GAAG2Q,EAAE,CAAC;IAClE,OAAO,IAAI,CAACwsB,QAAQ,CAAC3sB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE9a,OAAO,CAAC;GAC9C;EAEDuoC,cAAcA,CAACt6B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,GAAG,EAAE,EAAE;IACvCA,OAAO,CAAC+Y,OAAO,GAAG,QAAQ;IAC1B/Y,OAAO,CAAC6K,QAAQ,GAAG,IAAIrH,MAAM,EAAE;IAC/B,OAAO,IAAI,CAAC8jC,QAAQ,CAACr5B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GAC1C;EAEDwoC,iBAAiBA,CAACv6B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,GAAG,EAAE,EAAE;IAC1CA,OAAO,CAAC+Y,OAAO,GAAG,QAAQ;IAC1B/Y,OAAO,CAAC6K,QAAQ,GAAG,IAAIrH,MAAM,EAAE;IAC/B,OAAO,IAAI,CAAC8jC,QAAQ,CAACr5B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GAC1C;EAEDyoC,cAAcA,CAACx6B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAEkL,IAAI,EAAEhzB,OAAO,GAAG,EAAE,EAAE;IAC7CA,OAAO,CAAC+Y,OAAO,GAAG,UAAU;IAC5B/Y,OAAO,CAAC6K,QAAQ,GAAG,IAAIrH,MAAM,CAACwvB,IAAI,CAAC;IACnChzB,OAAO,CAAC0oC,EAAE,GAAG,IAAIllC,MAAM,EAAE;IACzB,OAAO,IAAI,CAAC8jC,QAAQ,CAACr5B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GAC1C;EAED2oC,cAAcA,CAAC16B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE8gB,IAAI,GAAG,EAAE,EAAE5oC,OAAO,GAAG,EAAE,EAAE;;IAElD,MAAM6oC,QAAQ,GAAG,IAAI,CAACD,IAAI,CACxBA,IAAI,CAAC/N,GAAG,EACRr6B,MAAM,CAACuhC,MAAM,CAAC;MAAE+G,MAAM,EAAE;KAAM,EAAEF,IAAI,CACtC,CAAC;IAED5oC,OAAO,CAAC+Y,OAAO,GAAG,gBAAgB;IAClC/Y,OAAO,CAAC+oC,EAAE,GAAGF,QAAQ;;;IAGrB,IAAI7oC,OAAO,CAAC6K,QAAQ,EAAE;MACpB7K,OAAO,CAAC6K,QAAQ,GAAG,IAAIrH,MAAM,CAACxD,OAAO,CAAC6K,QAAQ,CAAC;KAChD,MAAM,IAAIg+B,QAAQ,CAACzjC,IAAI,CAAC4jC,IAAI,EAAE;MAC7BhpC,OAAO,CAAC6K,QAAQ,GAAGg+B,QAAQ,CAACzjC,IAAI,CAAC4jC,IAAI;;IAGvC,OAAO,IAAI,CAAC1B,QAAQ,CAACr5B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GAC1C;EAEDwnC,YAAYA,CAAC7sB,EAAE,EAAEC,EAAE,EAAEoS,CAAC,EAAElF,CAAC,EAAE;;IAEzB,IAAIhN,EAAE,GAAGF,EAAE;IACXA,EAAE,IAAIkN,CAAC;;;IAGP,IAAIjN,EAAE,GAAGF,EAAE,GAAGqS,CAAC;;;IAGf,MAAM,CAAChT,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAI,CAACC,IAAI;IAC1CK,EAAE,GAAGX,EAAE,GAAGW,EAAE,GAAGT,EAAE,GAAGU,EAAE,GAAGR,EAAE;IAC3BQ,EAAE,GAAGX,EAAE,GAAGU,EAAE,GAAGR,EAAE,GAAGS,EAAE,GAAGP,EAAE;IAC3BQ,EAAE,GAAGb,EAAE,GAAGa,EAAE,GAAGX,EAAE,GAAGY,EAAE,GAAGV,EAAE;IAC3BU,EAAE,GAAGb,EAAE,GAAGY,EAAE,GAAGV,EAAE,GAAGW,EAAE,GAAGT,EAAE;IAE3B,OAAO,CAACM,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;;AAE3B,CAAC;;AC1KD,MAAMmuB,UAAU,CAAC;EACflpC,WAAWA,CAACoF,QAAQ,EAAE+jC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEppC,OAAO,GAAG;IAAEqpC,QAAQ,EAAE;GAAO,EAAE;IACxE,IAAI,CAAClkC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACnF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACspC,WAAW,GAAG,EAAE;IAErB,IAAIF,IAAI,KAAK,IAAI,EAAE;MACjB,IAAI,CAACE,WAAW,CAAC,MAAM,CAAC,GAAG,CAACF,IAAI,CAAC7+B,UAAU,EAAE,KAAK,CAAC;;IAGrD,IAAI2+B,MAAM,KAAK,IAAI,EAAE;MACnB,IAAI,CAACI,WAAW,CAAC,QAAQ,CAAC,GAAGJ,MAAM;;IAGrC,IAAIC,KAAK,KAAK,IAAI,EAAE;MAClB,IAAI,CAACG,WAAW,CAAC,OAAO,CAAC,GAAG,IAAI9lC,MAAM,CAAC2lC,KAAK,CAAC;;IAG/C,IAAI,CAAC5+B,UAAU,GAAG,IAAI,CAACpF,QAAQ,CAACjD,GAAG,CAAC,IAAI,CAAConC,WAAW,CAAC;IACrD,IAAI,CAACC,QAAQ,GAAG,EAAE;;EAGpBC,OAAOA,CAACL,KAAK,EAAEnpC,OAAO,GAAG;IAAEqpC,QAAQ,EAAE;GAAO,EAAE;IAC5C,MAAMnd,MAAM,GAAG,IAAI+c,UAAU,CAC3B,IAAI,CAAC9jC,QAAQ,EACb,IAAI,CAACoF,UAAU,EACf4+B,KAAK,EACL,IAAI,CAAChkC,QAAQ,CAAC0T,IAAI,EAClB7Y,OACF,CAAC;IACD,IAAI,CAACupC,QAAQ,CAACroC,IAAI,CAACgrB,MAAM,CAAC;IAE1B,OAAOA,MAAM;;EAGfud,UAAUA,GAAG;IACX,IAAI,IAAI,CAACF,QAAQ,CAACxoC,MAAM,GAAG,CAAC,EAAE;MAC5B,IAAI,IAAI,CAACf,OAAO,CAACqpC,QAAQ,EAAE;QACzB,IAAI,CAACC,WAAW,CAACI,KAAK,GAAG,IAAI,CAACH,QAAQ,CAACxoC,MAAM;;MAG/C,MAAMC,KAAK,GAAG,IAAI,CAACuoC,QAAQ,CAAC,CAAC,CAAC;QAC5BtoC,IAAI,GAAG,IAAI,CAACsoC,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACxoC,MAAM,GAAG,CAAC,CAAC;MAChD,IAAI,CAACuoC,WAAW,CAACK,KAAK,GAAG3oC,KAAK,CAACuJ,UAAU;MACzC,IAAI,CAAC++B,WAAW,CAACM,IAAI,GAAG3oC,IAAI,CAACsJ,UAAU;MAEvC,KAAK,IAAIlH,CAAC,GAAG,CAAC,EAAE6vB,GAAG,GAAG,IAAI,CAACqW,QAAQ,CAACxoC,MAAM,EAAEsC,CAAC,GAAG6vB,GAAG,EAAE7vB,CAAC,EAAE,EAAE;QACxD,MAAMwmC,KAAK,GAAG,IAAI,CAACN,QAAQ,CAAClmC,CAAC,CAAC;QAC9B,IAAIA,CAAC,GAAG,CAAC,EAAE;UACTwmC,KAAK,CAACP,WAAW,CAACQ,IAAI,GAAG,IAAI,CAACP,QAAQ,CAAClmC,CAAC,GAAG,CAAC,CAAC,CAACkH,UAAU;;QAE1D,IAAIlH,CAAC,GAAG,IAAI,CAACkmC,QAAQ,CAACxoC,MAAM,GAAG,CAAC,EAAE;UAChC8oC,KAAK,CAACP,WAAW,CAACS,IAAI,GAAG,IAAI,CAACR,QAAQ,CAAClmC,CAAC,GAAG,CAAC,CAAC,CAACkH,UAAU;;QAE1Ds/B,KAAK,CAACJ,UAAU,EAAE;;;IAItB,OAAO,IAAI,CAACl/B,UAAU,CAAC5H,GAAG,EAAE;;AAEhC;;AC1DA,mBAAe;EACbqnC,WAAWA,GAAG;IACZ,OAAQ,IAAI,CAACC,OAAO,GAAG,IAAIhB,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;GAC9D;EAEDQ,UAAUA,GAAG;IACX,IAAI,CAACQ,OAAO,CAACR,UAAU,EAAE;IACzB,IAAI,IAAI,CAACQ,OAAO,CAACV,QAAQ,CAACxoC,MAAM,GAAG,CAAC,EAAE;MACpC,IAAI,CAAC2J,KAAK,CAACtF,IAAI,CAAC8kC,QAAQ,GAAG,IAAI,CAACD,OAAO,CAAC1/B,UAAU;MAClD,OAAQ,IAAI,CAACG,KAAK,CAACtF,IAAI,CAAC+kC,QAAQ,GAAG,aAAa;;;AAGtD,CAAC;;ACdD;AACA;AACA;AACA;;AAEA,MAAMC,mBAAmB,CAAC;EACxBrqC,WAAWA,CAACsqC,OAAO,EAAEC,IAAI,EAAE;IACzB,IAAI,CAACC,IAAI,GAAG,CAAC;MAAEF,OAAO;MAAEC;KAAM,CAAC;;EAGjCppC,IAAIA,CAACspC,aAAa,EAAE;IAClBA,aAAa,CAACD,IAAI,CAACxuB,OAAO,CAAE7Z,GAAG,IAAK,IAAI,CAACqoC,IAAI,CAACrpC,IAAI,CAACgB,GAAG,CAAC,CAAC;;AAE5D;;ACbA;AACA;AACA;AACA;AAIA,MAAMuoC,mBAAmB,CAAC;EACxB1qC,WAAWA,CAACoF,QAAQ,EAAEulC,IAAI,EAAE1qC,OAAO,GAAG,EAAE,EAAEupC,QAAQ,GAAG,IAAI,EAAE;IACzD,IAAI,CAACpkC,QAAQ,GAAGA,QAAQ;IAExB,IAAI,CAACwlC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACtgC,UAAU,GAAGpF,QAAQ,CAACjD,GAAG,CAAC;;MAE7BiX,CAAC,EAAEuxB;KACJ,CAAC;IAEF,MAAMtlC,IAAI,GAAG,IAAI,CAACmF,UAAU,CAACnF,IAAI;IAEjC,IAAItC,KAAK,CAAC4B,OAAO,CAAC1E,OAAO,CAAC,IAAI,IAAI,CAAC8qC,aAAa,CAAC9qC,OAAO,CAAC,EAAE;MACzDupC,QAAQ,GAAGvpC,OAAO;MAClBA,OAAO,GAAG,EAAE;;IAGd,IAAI,OAAOA,OAAO,CAACmpC,KAAK,KAAK,WAAW,EAAE;MACxC/jC,IAAI,CAAC+iB,CAAC,GAAG,IAAI3kB,MAAM,CAACxD,OAAO,CAACmpC,KAAK,CAAC;;IAEpC,IAAI,OAAOnpC,OAAO,CAAC+qC,IAAI,KAAK,WAAW,EAAE;MACvC3lC,IAAI,CAAC4lC,IAAI,GAAG,IAAIxnC,MAAM,CAACxD,OAAO,CAAC+qC,IAAI,CAAC;;IAEtC,IAAI,OAAO/qC,OAAO,CAACirC,GAAG,KAAK,WAAW,EAAE;MACtC7lC,IAAI,CAAC8lC,GAAG,GAAG,IAAI1nC,MAAM,CAACxD,OAAO,CAACirC,GAAG,CAAC;;IAEpC,IAAI,OAAOjrC,OAAO,CAACqpC,QAAQ,KAAK,WAAW,EAAE;MAC3CjkC,IAAI,CAAC+lC,CAAC,GAAG,IAAI3nC,MAAM,CAACxD,OAAO,CAACqpC,QAAQ,CAAC;;IAEvC,IAAI,OAAOrpC,OAAO,CAACorC,MAAM,KAAK,WAAW,EAAE;MACzChmC,IAAI,CAACimC,UAAU,GAAG,IAAI7nC,MAAM,CAACxD,OAAO,CAACorC,MAAM,CAAC;;IAG9C,IAAI,CAACE,SAAS,GAAG,EAAE;IAEnB,IAAI/B,QAAQ,EAAE;MACZ,IAAI,CAACzmC,KAAK,CAAC4B,OAAO,CAAC6kC,QAAQ,CAAC,EAAE;QAC5BA,QAAQ,GAAG,CAACA,QAAQ,CAAC;;MAEvBA,QAAQ,CAACxtB,OAAO,CAAE8tB,KAAK,IAAK,IAAI,CAAC1pC,GAAG,CAAC0pC,KAAK,CAAC,CAAC;MAC5C,IAAI,CAAClnC,GAAG,EAAE;;;EAIdxC,GAAGA,CAAC0pC,KAAK,EAAE;IACT,IAAI,IAAI,CAACe,MAAM,EAAE;MACf,MAAM,IAAI/qC,KAAK,CAAE,qDAAoD,CAAC;;IAGxE,IAAI,CAAC,IAAI,CAACirC,aAAa,CAACjB,KAAK,CAAC,EAAE;MAC9B,MAAM,IAAIhqC,KAAK,CAAE,iCAAgC,CAAC;;IAGpD,IAAIgqC,KAAK,YAAYY,mBAAmB,EAAE;MACxCZ,KAAK,CAAC0B,SAAS,CAAC,IAAI,CAAChhC,UAAU,CAAC;MAChC,IAAI,IAAI,CAACogC,SAAS,EAAE;QAClBd,KAAK,CAAC2B,WAAW,EAAE;;;IAIvB,IAAI3B,KAAK,YAAYO,mBAAmB,EAAE;MACxC,IAAI,CAACqB,uBAAuB,CAAC5B,KAAK,CAAC;;IAGrC,IAAI,OAAOA,KAAK,KAAK,UAAU,IAAI,IAAI,CAACc,SAAS,EAAE;;MAEjDd,KAAK,GAAG,IAAI,CAAC6B,kBAAkB,CAAC7B,KAAK,CAAC;;IAGxC,IAAI,CAACyB,SAAS,CAACpqC,IAAI,CAAC2oC,KAAK,CAAC;IAE1B,OAAO,IAAI;;EAGb4B,uBAAuBA,CAACrhC,OAAO,EAAE;IAC/BA,OAAO,CAACmgC,IAAI,CAACxuB,OAAO,CAAC,CAAC;MAAEsuB,OAAO;MAAEC;KAAM,KAAK;MAC1C,MAAMqB,iBAAiB,GAAG,IAAI,CAACxmC,QAAQ,CAACymC,mBAAmB,EAAE,CAC1DtrC,GAAG,CAAC+pC,OAAO,CAACjlC,IAAI,CAACyG,aAAa,CAAC;MAClC8/B,iBAAiB,CAACrB,IAAI,CAAC,GAAG,IAAI,CAAC//B,UAAU;KAC1C,CAAC;;EAGJghC,SAASA,CAACM,SAAS,EAAE;IACnB,IAAI,IAAI,CAACthC,UAAU,CAACnF,IAAI,CAACwN,CAAC,EAAE;MAC1B,MAAM,IAAI/S,KAAK,CAAE,iDAAgD,CAAC;;IAGpE,IAAI,CAAC0K,UAAU,CAACnF,IAAI,CAACwN,CAAC,GAAGi5B,SAAS;IAElC,IAAI,CAACC,MAAM,EAAE;;EAGfN,WAAWA,GAAG;IACZ,IAAI,IAAI,CAACb,SAAS,EAAE;MAClB;;IAGF,IAAI,CAACW,SAAS,CAACvvB,OAAO,CAAC,CAAC8tB,KAAK,EAAE3zB,KAAK,KAAK;MACvC,IAAI2zB,KAAK,YAAYY,mBAAmB,EAAE;QACxCZ,KAAK,CAAC2B,WAAW,EAAE;;MAErB,IAAI,OAAO3B,KAAK,KAAK,UAAU,EAAE;QAC/B,IAAI,CAACyB,SAAS,CAACp1B,KAAK,CAAC,GAAG,IAAI,CAACw1B,kBAAkB,CAAC7B,KAAK,CAAC;;KAEzD,CAAC;IAEF,IAAI,CAACc,SAAS,GAAG,IAAI;IAErB,IAAI,CAACmB,MAAM,EAAE;;EAGfnpC,GAAGA,GAAG;IACJ,IAAI,IAAI,CAACioC,MAAM,EAAE;MACf;;IAGF,IAAI,CAACU,SAAS,CACX38B,MAAM,CAAEk7B,KAAK,IAAKA,KAAK,YAAYY,mBAAmB,CAAC,CACvD1uB,OAAO,CAAE8tB,KAAK,IAAKA,KAAK,CAAClnC,GAAG,EAAE,CAAC;IAElC,IAAI,CAACioC,MAAM,GAAG,IAAI;IAElB,IAAI,CAACkB,MAAM,EAAE;;EAGfhB,aAAaA,CAACjB,KAAK,EAAE;IACnB,OAAOA,KAAK,YAAYY,mBAAmB,IACvCZ,KAAK,YAAYO,mBAAmB,IACpC,OAAOP,KAAK,KAAK,UAAU;;EAGjC6B,kBAAkBA,CAACK,OAAO,EAAE;IAC1B,MAAM3hC,OAAO,GAAG,IAAI,CAACjF,QAAQ,CAAC86B,oBAAoB,CAAC,IAAI,CAAC11B,UAAU,CAACnF,IAAI,CAAC+T,CAAC,CAAC;IAC1E4yB,OAAO,EAAE;IACT,IAAI,CAAC5mC,QAAQ,CAAC6mC,gBAAgB,EAAE;IAEhC,IAAI,CAACP,uBAAuB,CAACrhC,OAAO,CAAC;IAErC,OAAOA,OAAO;;EAGhB6hC,YAAYA,GAAG;IACb,IAAI,CAAC,IAAI,CAAC1hC,UAAU,CAACnF,IAAI,CAACwN,CAAC,IAAI,CAAC,IAAI,CAACg4B,MAAM,EAAE;MAC3C,OAAO,KAAK;;IAGd,OAAO,IAAI,CAACU,SAAS,CAACxd,KAAK,CAAE+b,KAAK,IAAK;MACrC,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;QAC/B,OAAO,KAAK;;MAEd,IAAIA,KAAK,YAAYY,mBAAmB,EAAE;QACxC,OAAOZ,KAAK,CAACoC,YAAY,EAAE;;MAE7B,OAAO,IAAI;KACZ,CAAC;;EAGJH,MAAMA,GAAG;IACP,IAAI,IAAI,CAACjB,QAAQ,IAAI,CAAC,IAAI,CAACoB,YAAY,EAAE,EAAE;MACzC;;IAGF,IAAI,CAAC1hC,UAAU,CAACnF,IAAI,CAACtD,CAAC,GAAG,EAAE;IAE3B,IAAI,CAACwpC,SAAS,CAACvvB,OAAO,CAAE8tB,KAAK,IAAK,IAAI,CAACqC,WAAW,CAACrC,KAAK,CAAC,CAAC;IAE1D,IAAI,CAACt/B,UAAU,CAAC5H,GAAG,EAAE;;;;;IAKrB,IAAI,CAAC2oC,SAAS,GAAG,EAAE;IACnB,IAAI,CAAC/gC,UAAU,CAACnF,IAAI,CAACtD,CAAC,GAAG,IAAI;IAE7B,IAAI,CAAC+oC,QAAQ,GAAG,IAAI;;EAGtBqB,WAAWA,CAACrC,KAAK,EAAE;IACjB,IAAIA,KAAK,YAAYY,mBAAmB,EAAE;MACxC,IAAI,CAAClgC,UAAU,CAACnF,IAAI,CAACtD,CAAC,CAACZ,IAAI,CAAC2oC,KAAK,CAACt/B,UAAU,CAAC;;IAG/C,IAAIs/B,KAAK,YAAYO,mBAAmB,EAAE;MACxCP,KAAK,CAACU,IAAI,CAACxuB,OAAO,CAAC,CAAC;QAAEsuB,OAAO;QAAEC;OAAM,KAAK;QACxC,IAAI,CAAC,IAAI,CAAC//B,UAAU,CAACnF,IAAI,CAAC+mC,EAAE,EAAE;UAC5B,IAAI,CAAC5hC,UAAU,CAACnF,IAAI,CAAC+mC,EAAE,GAAG9B,OAAO;;QAGnC,IAAI,IAAI,CAAC9/B,UAAU,CAACnF,IAAI,CAAC+mC,EAAE,KAAK9B,OAAO,EAAE;UACvC,IAAI,CAAC9/B,UAAU,CAACnF,IAAI,CAACtD,CAAC,CAACZ,IAAI,CAACopC,IAAI,CAAC;SAClC,MAAM;UACL,IAAI,CAAC//B,UAAU,CAACnF,IAAI,CAACtD,CAAC,CAACZ,IAAI,CAAC;YAC1BsJ,IAAI,EAAE,KAAK;YACX2hC,EAAE,EAAE9B,OAAO;YACX+B,IAAI,EAAE9B;WACP,CAAC;;OAEL,CAAC;;;AAGR;;ACjNA;AACA;AACA;AAIA,MAAM+B,aAAa,SAASvsC,OAAO,CAAC;EAClCe,YAAYA,CAACF,CAAC,EAAEC,CAAC,EAAE;IACjB,OAAOmc,QAAQ,CAACpc,CAAC,CAAC,GAAGoc,QAAQ,CAACnc,CAAC,CAAC;;EAGlCU,SAASA,GAAG;IACV,OAAO,MAAM;;EAGfD,WAAWA,CAACiL,CAAC,EAAE;IACb,OAAOyQ,QAAQ,CAACzQ,CAAC,CAAC;;AAEtB;;AClBA;AACA;AACA;AACA;AAOA,oBAAe;EAEbggC,YAAYA,CAACtsC,OAAO,EAAE;IACpB,IAAI,CAACusC,cAAc,GAAG,EAAE;IAExB,IAAIvsC,OAAO,CAACwsC,MAAM,EAAE;MAClB,IAAI,CAACC,qBAAqB,EAAE,CAACrnC,IAAI,CAACsnC,MAAM,GAAG,IAAI;MAC/C,IAAI,CAACC,iBAAiB,EAAE;;GAE3B;EAEDC,WAAWA,CAACvU,GAAG,EAAEr4B,OAAO,GAAG,IAAI,EAAE;IAC/B,IAAIq4B,GAAG,KAAK,UAAU,IAAKr4B,OAAO,IAAIA,OAAO,CAACsqC,IAAK,EAAE;MACnD,IAAIuC,OAAO,GAAG,CAAC;MACf,IAAI,CAACh0B,IAAI,CAAC9N,QAAQ,CAACgR,OAAO,CAAE+wB,OAAO,IAAK;QACtC,IAAID,OAAO,IAAIC,OAAO,CAACtC,aAAa,IAAIsC,OAAO,CAACzU,GAAG,KAAK,UAAU,EAAE;UAClEwU,OAAO,EAAE;;OAEZ,CAAC;MACF,OAAOA,OAAO,EAAE,EAAE;QAChB,IAAI,CAACb,gBAAgB,EAAE;;;IAI3B,IAAI,CAAChsC,OAAO,EAAE;MACZ,IAAI,CAAC6Y,IAAI,CAAC9N,QAAQ,CAAC7J,IAAI,CAAC;QAAEm3B;OAAK,CAAC;MAChC,IAAI,CAAC5d,UAAU,CAAE,IAAG4d,GAAI,MAAK,CAAC;MAC9B,OAAO,IAAI;;IAGb,IAAI,CAACxf,IAAI,CAAC9N,QAAQ,CAAC7J,IAAI,CAAC;MAAEm3B,GAAG;MAAEr4B;KAAS,CAAC;IAEzC,MAAMuK,UAAU,GAAG,EAAE;IAErB,IAAI,OAAOvK,OAAO,CAACsqC,IAAI,KAAK,WAAW,EAAE;MACvC//B,UAAU,CAAC6hC,IAAI,GAAGpsC,OAAO,CAACsqC,IAAI;;IAEhC,IAAIjS,GAAG,KAAK,UAAU,EAAE;MACtB,IAAI,OAAOr4B,OAAO,CAAC0qC,IAAI,KAAK,QAAQ,EAAE;QACpCngC,UAAU,CAACC,IAAI,GAAGxK,OAAO,CAAC0qC,IAAI;;MAEhC,IAAI5nC,KAAK,CAAC4B,OAAO,CAAC1E,OAAO,CAACge,IAAI,CAAC,EAAE;QAC/BzT,UAAU,CAAC0O,IAAI,GAAG,CAACjZ,OAAO,CAACge,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAACnF,IAAI,CAAC1O,MAAM,GAAGnK,OAAO,CAACge,IAAI,CAAC,CAAC,CAAC,EACpEhe,OAAO,CAACge,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAACnF,IAAI,CAAC1O,MAAM,GAAGnK,OAAO,CAACge,IAAI,CAAC,CAAC,CAAC,CAAC;;MAExD,IAAIlb,KAAK,CAAC4B,OAAO,CAAC1E,OAAO,CAAC+sC,QAAQ,CAAC,IACjC/sC,OAAO,CAAC+sC,QAAQ,CAACjf,KAAK,CAACztB,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,CAAC,EAAE;QACxDkK,UAAU,CAACyiC,QAAQ,GAAGhtC,OAAO,CAAC+sC,QAAQ;;;IAG1C,IAAI1U,GAAG,KAAK,MAAM,EAAE;MAClB,IAAIr4B,OAAO,CAAC+qC,IAAI,EAAE;QAChBxgC,UAAU,CAACygC,IAAI,GAAG,IAAIxnC,MAAM,CAACxD,OAAO,CAAC+qC,IAAI,CAAC;;MAE5C,IAAI/qC,OAAO,CAACirC,GAAG,EAAE;QACf1gC,UAAU,CAAC2gC,GAAG,GAAG,IAAI1nC,MAAM,CAACxD,OAAO,CAACirC,GAAG,CAAC;;MAE1C,IAAIjrC,OAAO,CAACqpC,QAAQ,EAAE;QACpB9+B,UAAU,CAAC4gC,CAAC,GAAG,IAAI3nC,MAAM,CAACxD,OAAO,CAACqpC,QAAQ,CAAC;;MAE7C,IAAIrpC,OAAO,CAACorC,MAAM,EAAE;QAClB7gC,UAAU,CAAC8gC,UAAU,GAAG,IAAI7nC,MAAM,CAACxD,OAAO,CAACorC,MAAM,CAAC;;;IAItD,IAAI,CAAC3wB,UAAU,CAAE,IAAG4d,GAAI,IAAGl3B,SAAS,CAACC,OAAO,CAACmJ,UAAU,CAAE,MAAK,CAAC;IAC/D,OAAO,IAAI;GACZ;EAED01B,oBAAoBA,CAAC5H,GAAG,EAAEr4B,OAAO,GAAG,EAAE,EAAE;IACtC,MAAM2rC,iBAAiB,GAAG,IAAI,CAACC,mBAAmB,EAAE,CAACtrC,GAAG,CAAC,IAAI,CAACuY,IAAI,CAACjN,mBAAmB,CAAC;IACvF,MAAM0+B,IAAI,GAAGqB,iBAAiB,CAAC5qC,MAAM;IACrC4qC,iBAAiB,CAACzqC,IAAI,CAAC,IAAI,CAAC;IAE5B,IAAI,CAAC0rC,WAAW,CAACvU,GAAG,EAAE;MAAE,GAAGr4B,OAAO;MAAEsqC;KAAM,CAAC;IAE3C,MAAME,aAAa,GAAG,IAAIJ,mBAAmB,CAAC,IAAI,CAACvxB,IAAI,CAACtO,UAAU,EAAE+/B,IAAI,CAAC;IACzE,IAAI,CAACzxB,IAAI,CAAC9N,QAAQ,CAAChI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACynC,aAAa,GAAGA,aAAa;IAC7D,OAAOA,aAAa;GACrB;EAEDwB,gBAAgBA,GAAG;IACjB,IAAI,CAACnzB,IAAI,CAAC9N,QAAQ,CAAC+hB,GAAG,EAAE;IACxB,IAAI,CAACrS,UAAU,CAAC,KAAK,CAAC;IACtB,OAAO,IAAI;GACZ;EAEDslB,MAAMA,CAAC2K,IAAI,EAAE1qC,OAAO,GAAG,EAAE,EAAEupC,QAAQ,GAAG,IAAI,EAAE;IAC1C,OAAO,IAAIkB,mBAAmB,CAAC,IAAI,EAAEC,IAAI,EAAE1qC,OAAO,EAAEupC,QAAQ,CAAC;GAC9D;EAED1J,YAAYA,CAACoN,UAAU,EAAE;IACvB,MAAMC,cAAc,GAAG,IAAI,CAACP,iBAAiB,EAAE;IAC/CM,UAAU,CAAC1B,SAAS,CAAC2B,cAAc,CAAC;IACpCD,UAAU,CAACzB,WAAW,EAAE;IACxB,IAAI,CAACe,cAAc,CAACrrC,IAAI,CAAC+rC,UAAU,CAAC;IACpC,IAAI,CAACC,cAAc,CAAC9nC,IAAI,CAACtD,CAAC,EAAE;MAC1BorC,cAAc,CAAC9nC,IAAI,CAACtD,CAAC,GAAG,EAAE;;IAE5BorC,cAAc,CAAC9nC,IAAI,CAACtD,CAAC,CAACZ,IAAI,CAAC+rC,UAAU,CAAC1iC,UAAU,CAAC;IACjD,OAAO,IAAI;GACZ;EAED4iC,gBAAgBA,CAACC,YAAY,EAAE;IAC7BA,YAAY,CAACrxB,OAAO,CAAE+wB,OAAO,IAAK;MAChC,IAAIA,OAAO,CAACtC,aAAa,EAAE;QACzB,MAAMA,aAAa,GAAGsC,OAAO,CAACtC,aAAa;QAC3C,MAAM6C,gBAAgB,GAAG,IAAI,CAACpN,oBAAoB,CAAC6M,OAAO,CAACzU,GAAG,EAAEyU,OAAO,CAAC9sC,OAAO,CAAC;QAChFwqC,aAAa,CAACtpC,IAAI,CAACmsC,gBAAgB,CAAC;QACpC,IAAI,CAACx0B,IAAI,CAAC9N,QAAQ,CAAChI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACynC,aAAa,GAAGA,aAAa;OAC9D,MAAM;QACL,IAAI,CAACoC,WAAW,CAACE,OAAO,CAACzU,GAAG,EAAEyU,OAAO,CAAC9sC,OAAO,CAAC;;KAEjD,CAAC;GACH;EAEDstC,eAAeA,CAACz0B,IAAI,EAAE;IACpB,MAAMu0B,YAAY,GAAGv0B,IAAI,CAAC9N,QAAQ;IAClCqiC,YAAY,CAACrxB,OAAO,CAAC,MAAMlD,IAAI,CAACnT,KAAK,CAAC,KAAK,CAAC,CAAC;IAC7CmT,IAAI,CAAC9N,QAAQ,GAAG,EAAE;IAClB,OAAOqiC,YAAY;GACpB;EAEDX,qBAAqBA,GAAG;IACtB,IAAI,CAAC,IAAI,CAAC/hC,KAAK,CAACtF,IAAI,CAACmoC,QAAQ,EAAE;MAC7B,IAAI,CAAC7iC,KAAK,CAACtF,IAAI,CAACmoC,QAAQ,GAAG,IAAI,CAACrrC,GAAG,CAAC,EAAE,CAAC;;IAEzC,OAAO,IAAI,CAACwI,KAAK,CAACtF,IAAI,CAACmoC,QAAQ;GAChC;EAEDrhC,qBAAqBA,GAAG;IACtB,OAAO,CAAC,CAAC,IAAI,CAACxB,KAAK,CAACtF,IAAI,CAACmoC,QAAQ;GAClC;EAEDZ,iBAAiBA,GAAG;IAClB,IAAI,CAAC,IAAI,CAACjiC,KAAK,CAACtF,IAAI,CAACooC,cAAc,EAAE;MACnC,IAAI,CAAC9iC,KAAK,CAACtF,IAAI,CAACooC,cAAc,GAAG,IAAI,CAACtrC,GAAG,CAAC;QACxCsI,IAAI,EAAE,gBAAgB;QACtBijC,UAAU,EAAE,IAAIpB,aAAa,EAAE;QAC/BqB,iBAAiB,EAAE;OACpB,CAAC;;IAEJ,OAAO,IAAI,CAAChjC,KAAK,CAACtF,IAAI,CAACooC,cAAc;GACtC;EAED5B,mBAAmBA,GAAG;IACpB,OAAO,IAAI,CAACe,iBAAiB,EAAE,CAACvnC,IAAI,CAACqoC,UAAU;GAChD;EAED3hC,6BAA6BA,GAAG;;IAE9B,IAAI,CAAC2gC,qBAAqB,EAAE;IAE5B,MAAMS,cAAc,GAAG,IAAI,CAACP,iBAAiB,EAAE;IAC/C,MAAMvsC,GAAG,GAAG8sC,cAAc,CAAC9nC,IAAI,CAACsoC,iBAAiB,EAAE;IACnDR,cAAc,CAAC9nC,IAAI,CAACqoC,UAAU,CAACttC,GAAG,CAACC,GAAG,EAAE,EAAE,CAAC;IAC3C,OAAOA,GAAG;GACX;EAEDutC,WAAWA,GAAG;IACZ,MAAMT,cAAc,GAAG,IAAI,CAACxiC,KAAK,CAACtF,IAAI,CAACooC,cAAc;IACrD,IAAIN,cAAc,EAAE;MAClBA,cAAc,CAACvqC,GAAG,EAAE;MACpB,IAAI,CAAC4pC,cAAc,CAACxwB,OAAO,CAAEkxB,UAAU,IAAKA,UAAU,CAACtqC,GAAG,EAAE,CAAC;;IAE/D,IAAI,IAAI,CAAC+H,KAAK,CAACtF,IAAI,CAACmoC,QAAQ,EAAE;MAC5B,IAAI,CAAC7iC,KAAK,CAACtF,IAAI,CAACmoC,QAAQ,CAAC5qC,GAAG,EAAE;;;AAIpC,CAAC;;ACpLD,MAAMirC,WAAW,GAAG;EAClBC,QAAQ,EAAE,CAAC;EACXtP,QAAQ,EAAE,CAAC;EACXuP,QAAQ,EAAE,CAAC;EACXC,SAAS,EAAE,MAAM;EACjB/3B,QAAQ,EAAE,MAAM;EAChBg4B,iBAAiB,EAAE,MAAM;EACzBC,WAAW,EAAE,MAAM;EACnBC,UAAU,EAAE,OAAO;EACnBC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,OAAO;EACb1tC,IAAI,EAAE,OAAO;EACb2tC,WAAW,EAAE,QAAQ;EACrBC,OAAO,EAAE;AACX,CAAC;AACD,MAAMC,aAAa,GAAG;EACpB9nC,IAAI,EAAE,CAAC;EACP+nC,MAAM,EAAE,CAAC;EACT7nC,KAAK,EAAE;AACT,CAAC;AACD,MAAM8nC,SAAS,GAAG;EAAElsC,KAAK,EAAE,GAAG;EAAEmsC,YAAY,EAAE;AAAK,CAAC;AACpD,MAAMC,cAAc,GAAG;EACrBC,GAAG,EAAE,GAAG;EACRC,QAAQ,EAAE,GAAG;EACbC,IAAI,EAAE,GAAG;EACTC,KAAK,EAAE,GAAG;EACVC,GAAG,EAAE;AACP,CAAC;AACD,MAAMC,cAAc,GAAG;EACrBnqC,MAAM,EAAE;IACNoqC,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,YAAY;IACtBC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;GAClB;EACDC,OAAO,EAAE;IACPL,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;;AAEd,CAAC;AAED,oBAAe;;AAEf;AACA;AACA;EACEK,QAAQA,GAAG;IACT,IAAI,CAAC,IAAI,CAAC3T,KAAK,EAAE;MACf,MAAM,IAAIh8B,KAAK,CAAC,gDAAgD,CAAC;;IAEnE,IAAI,CAAC4vC,SAAS,GAAG;MACfzkC,KAAK,EAAE,EAAE;MACTywB,WAAW,EAAE,IAAI,CAACI,KAAK,CAACn6B;KACzB;IACD,IAAI,CAAC+tC,SAAS,CAACzkC,KAAK,CAAC,IAAI,CAAC6wB,KAAK,CAAC95B,EAAE,CAAC,GAAG,IAAI,CAAC85B,KAAK,CAAC35B,GAAG,EAAE;IAEtD,IAAIkD,IAAI,GAAG;MACTsqC,MAAM,EAAE,EAAE;MACVC,eAAe,EAAE,IAAI;MACrBjH,EAAE,EAAE,IAAIllC,MAAM,CAAE,IAAG,IAAI,CAACq4B,KAAK,CAAC95B,EAAG,WAAU,CAAC;MAC5C6tC,EAAE,EAAE;QACF3kC,IAAI,EAAE;;KAET;IACD7F,IAAI,CAACwqC,EAAE,CAAC3kC,IAAI,CAAC,IAAI,CAAC4wB,KAAK,CAAC95B,EAAE,CAAC,GAAG,IAAI,CAAC85B,KAAK,CAAC35B,GAAG,EAAE;IAC9C,MAAM2tC,QAAQ,GAAG,IAAI,CAAC3tC,GAAG,CAACkD,IAAI,CAAC;IAC/B,IAAI,CAACsF,KAAK,CAACtF,IAAI,CAACyqC,QAAQ,GAAGA,QAAQ;IACnC,OAAO,IAAI;GACZ;;AAGH;AACA;EACEC,WAAWA,GAAG;IACZ,IAAI,IAAI,CAACplC,KAAK,CAACtF,IAAI,CAACyqC,QAAQ,EAAE;MAC5B,IACE,CAACrvC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACgvC,SAAS,CAACzkC,KAAK,CAAC,CAACjK,MAAM,IACzC,CAAC,IAAI,CAAC0uC,SAAS,CAAChU,WAAW,EAC3B;QACA,MAAM,IAAI57B,KAAK,CAAC,iCAAiC,CAAC;;MAEpD,IAAIkwC,QAAQ,GAAG,IAAI,CAACrlC,KAAK,CAACtF,IAAI,CAACyqC,QAAQ,CAACzqC,IAAI,CAACwqC,EAAE,CAAC3kC,IAAI;MACpDzK,MAAM,CAACC,IAAI,CAAC,IAAI,CAACgvC,SAAS,CAACzkC,KAAK,CAAC,CAAC+Q,OAAO,CAACra,IAAI,IAAI;QAChDquC,QAAQ,CAACruC,IAAI,CAAC,GAAG,IAAI,CAAC+tC,SAAS,CAACzkC,KAAK,CAACtJ,IAAI,CAAC;OAC5C,CAAC;MACF,IAAI,CAACgJ,KAAK,CAACtF,IAAI,CAACyqC,QAAQ,CAACzqC,IAAI,CAACsqC,MAAM,CAAC3zB,OAAO,CAACi0B,QAAQ,IAAI;QACvD,IAAI,CAACC,SAAS,CAACD,QAAQ,CAAC;OACzB,CAAC;MACF,IAAI,CAACtlC,KAAK,CAACtF,IAAI,CAACyqC,QAAQ,CAACltC,GAAG,EAAE;;IAEhC,OAAO,IAAI;GACZ;EAEDstC,SAASA,CAAC/tC,GAAG,EAAE;IACb,IAAIY,KAAK,CAAC4B,OAAO,CAACxC,GAAG,CAACkD,IAAI,CAAC6iC,IAAI,CAAC,EAAE;MAChC/lC,GAAG,CAACkD,IAAI,CAAC6iC,IAAI,CAAClsB,OAAO,CAACm0B,QAAQ,IAAI;QAChC,IAAI,CAACD,SAAS,CAACC,QAAQ,CAAC;OACzB,CAAC;MACFhuC,GAAG,CAACS,GAAG,EAAE;;IAEX,OAAO,IAAI;GACZ;;AAGH;AACA;AACA;AACA;AACA;AACA;EACEwtC,SAASA,CAACzuC,IAAI,EAAE1B,OAAO,GAAG,EAAE,EAAE;IAC5B,IAAIowC,SAAS,GAAG,IAAI,CAACC,UAAU,CAAC3uC,IAAI,EAAE,IAAI,EAAE1B,OAAO,CAAC;IACpD,IAAIgwC,QAAQ,GAAG,IAAI,CAAC9tC,GAAG,CAACkuC,SAAS,CAAC;IAClC,IAAI,CAACE,YAAY,CAACN,QAAQ,CAAC;IAC3B,OAAOA,QAAQ;GAChB;;AAGH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEO,cAAcA,CAAC7uC,IAAI,EAAEgpC,IAAI,EAAEz8B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,GAAG,EAAE,EAAE;IACnD,IAAIowC,SAAS,GAAG,IAAI,CAACC,UAAU,CAAC3uC,IAAI,EAAEgpC,IAAI,EAAE1qC,OAAO,CAAC;IACpDowC,SAAS,CAACr3B,OAAO,GAAG,QAAQ;IAC5B,IAAIq3B,SAAS,CAAC1I,CAAC,KAAK5P,SAAS,EAAE;MAC7BsY,SAAS,CAAC1I,CAAC,GAAG,CAAC,CAAC;;;;IAIlB,IAAI,CAACJ,QAAQ,CAACr5B,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAEsoB,SAAS,CAAC;IACpC,IAAII,QAAQ,GAAG,IAAI,CAAC33B,IAAI,CAACnN,WAAW,CAAC,IAAI,CAACmN,IAAI,CAACnN,WAAW,CAAC3K,MAAM,GAAG,CAAC,CAAC;IAEtE,OAAO,IAAI,CAACuvC,YAAY,CAACE,QAAQ,CAAC;GACnC;EAEDC,QAAQA,CAAC/uC,IAAI,EAAEuM,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,GAAG,EAAE,EAAE;IACvC,OAAO,IAAI,CAACuwC,cAAc,CAAC7uC,IAAI,EAAE,MAAM,EAAEuM,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GAC9D;EAED0wC,cAAcA,CAAChvC,IAAI,EAAEuM,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,GAAG,EAAE,EAAE;IAC7C,OAAO,IAAI,CAACuwC,cAAc,CAAC7uC,IAAI,EAAE,YAAY,EAAEuM,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GACpE;EAED2wC,SAASA,CAACjvC,IAAI,EAAEuM,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,GAAG,EAAE,EAAE;IACxC,OAAO,IAAI,CAACuwC,cAAc,CAAC7uC,IAAI,EAAE,OAAO,EAAEuM,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GAC/D;EAED4wC,QAAQA,CAAClvC,IAAI,EAAEuM,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,GAAG,EAAE,EAAE;IACvC,OAAO,IAAI,CAACuwC,cAAc,CAAC7uC,IAAI,EAAE,MAAM,EAAEuM,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GAC9D;EAED6wC,eAAeA,CAACnvC,IAAI,EAAEuM,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,GAAG,EAAE,EAAE;IAC9C,OAAO,IAAI,CAACuwC,cAAc,CAAC7uC,IAAI,EAAE,aAAa,EAAEuM,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GACrE;EAED8wC,YAAYA,CAACpvC,IAAI,EAAEuM,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,GAAG,EAAE,EAAE;IAC3C,OAAO,IAAI,CAACuwC,cAAc,CAAC7uC,IAAI,EAAE,UAAU,EAAEuM,CAAC,EAAEub,CAAC,EAAEwD,CAAC,EAAElF,CAAC,EAAE9nB,OAAO,CAAC;GAClE;EAEDswC,YAAYA,CAACN,QAAQ,EAAE;IACrB,IAAI9G,MAAM,GAAG8G,QAAQ,CAAC5qC,IAAI,CAACqF,MAAM;IACjC,IAAIy+B,MAAM,EAAE;MACV,IAAI,CAACA,MAAM,CAAC9jC,IAAI,CAAC6iC,IAAI,EAAE;QACrBiB,MAAM,CAAC9jC,IAAI,CAAC6iC,IAAI,GAAG,EAAE;;MAEvBiB,MAAM,CAAC9jC,IAAI,CAAC6iC,IAAI,CAAC/mC,IAAI,CAAC8uC,QAAQ,CAAC;KAChC,MAAM;MACL,IAAI,CAACtlC,KAAK,CAACtF,IAAI,CAACyqC,QAAQ,CAACzqC,IAAI,CAACsqC,MAAM,CAACxuC,IAAI,CAAC8uC,QAAQ,CAAC;;IAErD,OAAO,IAAI;GACZ;EAEDK,UAAUA,CAAC3uC,IAAI,EAAEgpC,IAAI,EAAE1qC,OAAO,GAAG,EAAE,EAAE;IACnC,IAAI,CAAC,IAAI,CAACyvC,SAAS,EAAE;MACnB,MAAM,IAAI5vC,KAAK,CACb,yEACF,CAAC;;IAEH,IAAI2O,IAAI,GAAGhO,MAAM,CAACuhC,MAAM,CAAC,EAAE,EAAE/hC,OAAO,CAAC;IACrC,IAAI0qC,IAAI,KAAK,IAAI,EAAE;MACjBl8B,IAAI,GAAG,IAAI,CAACuiC,YAAY,CAACrG,IAAI,EAAE1qC,OAAO,CAAC;;IAEzCwO,IAAI,GAAG,IAAI,CAACwiC,aAAa,CAACxiC,IAAI,CAAC;IAC/BA,IAAI,GAAG,IAAI,CAACyiC,eAAe,CAACziC,IAAI,CAAC;IACjCA,IAAI,GAAG,IAAI,CAAC0iC,YAAY,CAAC1iC,IAAI,CAAC;IAC9BA,IAAI,GAAG,IAAI,CAAC2iC,eAAe,CAAC3iC,IAAI,CAAC;IACjCA,IAAI,GAAG,IAAI,CAAC4iC,cAAc,CAAC5iC,IAAI,CAAC;IAChCA,IAAI,GAAG,IAAI,CAAC6iC,cAAc,CAAC7iC,IAAI,CAAC;IAChCA,IAAI,CAAC2Z,CAAC,GAAG,IAAI3kB,MAAM,CAAC9B,IAAI,CAAC;IACzB,IAAI8M,IAAI,CAAC06B,MAAM,EAAE;MACf16B,IAAI,CAAC/D,MAAM,GAAG+D,IAAI,CAAC06B,MAAM;MACzB,OAAO16B,IAAI,CAAC06B,MAAM;;IAEpB,OAAO16B,IAAI;GACZ;EAEDuiC,YAAYA,CAACrG,IAAI,EAAEl8B,IAAI,EAAE;IACvB,IAAIk8B,IAAI,KAAK,MAAM,EAAE;MACnBl8B,IAAI,CAAC8iC,EAAE,GAAG,IAAI;KACf,MAAM,IAAI5G,IAAI,KAAK,YAAY,EAAE;MAChCl8B,IAAI,CAAC8iC,EAAE,GAAG,KAAK;MACf9iC,IAAI,CAAC0/B,UAAU,GAAG,IAAI;KACvB,MAAM,IAAIxD,IAAI,KAAK,aAAa,EAAE;MACjCl8B,IAAI,CAAC8iC,EAAE,GAAG,KAAK;MACf9iC,IAAI,CAACy/B,WAAW,GAAG,IAAI;KACxB,MAAM,IAAIvD,IAAI,KAAK,UAAU,EAAE;MAC9Bl8B,IAAI,CAAC8iC,EAAE,GAAG,KAAK;KAChB,MAAM,IAAI5G,IAAI,KAAK,OAAO,EAAE;MAC3Bl8B,IAAI,CAAC8iC,EAAE,GAAG,IAAI;MACd9iC,IAAI,CAAC2/B,KAAK,GAAG,IAAI;KAClB,MAAM,IAAIzD,IAAI,KAAK,MAAM,EAAE;MAC1Bl8B,IAAI,CAAC8iC,EAAE,GAAG,IAAI;KACf,MAAM;MACL,MAAM,IAAIzxC,KAAK,CAAE,iCAAgC6qC,IAAK,GAAE,CAAC;;IAE3D,OAAOl8B,IAAI;GACZ;EAED6iC,cAAcA,CAAC7iC,IAAI,EAAE;IACnB,MAAM+iC,CAAC,GAAG/iC,IAAI,CAACgjC,MAAM;IACrB,IAAID,CAAC,IAAIA,CAAC,CAAC7G,IAAI,EAAE;MACf,IAAI+G,WAAW;MACf,IAAIC,QAAQ;MACZ,IAAI5oB,MAAM,GAAG,EAAE;MACf,IAAI6lB,cAAc,CAAC4C,CAAC,CAAC7G,IAAI,CAAC,KAAK5S,SAAS,EAAE;QACxC2Z,WAAW,GAAI,qBAAoB;QACnCC,QAAQ,GAAI,kBAAiB;QAC7B5oB,MAAM,GAAG6lB,cAAc,CAAC4C,CAAC,CAAC7G,IAAI,CAAC;OAChC,MAAM;QACL,IAAI8G,MAAM,GAAGD,CAAC,CAAC7G,IAAI,CAAC7tB,MAAM,CAAC,CAAC,CAAC,CAAC5S,WAAW,EAAE,GAAGsnC,CAAC,CAAC7G,IAAI,CAAC3nC,KAAK,CAAC,CAAC,CAAC;QAC7D0uC,WAAW,GAAI,KAAID,MAAO,YAAW;QACrCE,QAAQ,GAAI,KAAIF,MAAO,SAAQ;QAE/B,IAAID,CAAC,CAAC7G,IAAI,KAAK,MAAM,EAAE;UACrB+G,WAAW,IAAI,IAAI;UACnB3oB,MAAM,GAAGtlB,MAAM,CAAC+tC,CAAC,CAACI,KAAK,CAAC;SACzB,MAAM,IAAIJ,CAAC,CAAC7G,IAAI,KAAK,MAAM,EAAE;UAC5B5hB,MAAM,GAAGtlB,MAAM,CAAC+tC,CAAC,CAACI,KAAK,CAAC;SACzB,MAAM,IAAIJ,CAAC,CAAC7G,IAAI,KAAK,QAAQ,EAAE;UAC9B,IAAI7E,CAAC,GAAGrlC,MAAM,CAACuhC,MAAM,CAAC,EAAE,EAAEkN,cAAc,CAACnqC,MAAM,EAAEysC,CAAC,CAAC;UACnDzoB,MAAM,GAAGtlB,MAAM,CACb,CACEA,MAAM,CAACqiC,CAAC,CAACqJ,IAAI,CAAC,EACdrJ,CAAC,CAACsJ,QAAQ,GAAG,GAAG,GAAG,GAAG,EACtB,GAAG,GAAGtJ,CAAC,CAACuJ,QAAQ,GAAG,GAAG,EACtB,MAAM,EACN,GAAG,GAAGvJ,CAAC,CAACwJ,QAAQ,GAAG,GAAG,EACtB7rC,MAAM,CAACqiC,CAAC,CAACyJ,eAAe,CAAC,CAC1B,CAAC/tC,IAAI,CAAC,GAAG,CACZ,CAAC;SACF,MAAM,IAAIgwC,CAAC,CAAC7G,IAAI,KAAK,SAAS,EAAE;UAC/B,IAAI7E,CAAC,GAAGrlC,MAAM,CAACuhC,MAAM,CAAC,EAAE,EAAEkN,cAAc,CAACM,OAAO,EAAEgC,CAAC,CAAC;UACpDzoB,MAAM,GAAGtlB,MAAM,CAAC,CAACA,MAAM,CAACqiC,CAAC,CAACqJ,IAAI,CAAC,EAAErJ,CAAC,CAACsJ,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC5tC,IAAI,CAAC,GAAG,CAAC,CAAC;;;MAGvEiN,IAAI,CAACojC,EAAE,GAAGpjC,IAAI,CAACojC,EAAE,GAAGpjC,IAAI,CAACojC,EAAE,GAAG,EAAE;MAChCpjC,IAAI,CAACojC,EAAE,CAAC9vC,CAAC,GAAG;QACVqX,CAAC,EAAE,YAAY;QACf04B,EAAE,EAAE,IAAIruC,MAAM,CAAE,GAAEiuC,WAAY,IAAG3oB,MAAO,IAAG;OAC5C;MACDta,IAAI,CAACojC,EAAE,CAAClK,CAAC,GAAG;QACVvuB,CAAC,EAAE,YAAY;QACf04B,EAAE,EAAE,IAAIruC,MAAM,CAAE,GAAEkuC,QAAS,IAAG5oB,MAAO,IAAG;OACzC;;IAEH,OAAOta,IAAI,CAACgjC,MAAM;IAClB,OAAOhjC,IAAI;GACZ;EAED4iC,cAAcA,CAAC5iC,IAAI,EAAE;IACnB,IAAIrC,KAAK,GAAG,IAAI,CAAC6K,eAAe,CAACxI,IAAI,CAACsjC,eAAe,CAAC;IACtD,IAAI3lC,KAAK,EAAE;MACT,IAAI,CAACqC,IAAI,CAACujC,EAAE,EAAE;QACZvjC,IAAI,CAACujC,EAAE,GAAG,EAAE;;MAEdvjC,IAAI,CAACujC,EAAE,CAACC,EAAE,GAAG7lC,KAAK;;IAEpBA,KAAK,GAAG,IAAI,CAAC6K,eAAe,CAACxI,IAAI,CAACyjC,WAAW,CAAC;IAC9C,IAAI9lC,KAAK,EAAE;MACT,IAAI,CAACqC,IAAI,CAACujC,EAAE,EAAE;QACZvjC,IAAI,CAACujC,EAAE,GAAG,EAAE;;MAEdvjC,IAAI,CAACujC,EAAE,CAACG,EAAE,GAAG/lC,KAAK;;IAEpB,OAAOqC,IAAI,CAACsjC,eAAe;IAC3B,OAAOtjC,IAAI,CAACyjC,WAAW;IACvB,OAAOzjC,IAAI;GACZ;EAEDwiC,aAAaA,CAAChxC,OAAO,EAAE;IACrB,IAAIksB,MAAM,GAAG,CAAC;IACd1rB,MAAM,CAACC,IAAI,CAACT,OAAO,CAAC,CAAC+b,OAAO,CAAC3b,GAAG,IAAI;MAClC,IAAIwtC,WAAW,CAACxtC,GAAG,CAAC,EAAE;QACpB,IAAIJ,OAAO,CAACI,GAAG,CAAC,EAAE;UAChB8rB,MAAM,IAAI0hB,WAAW,CAACxtC,GAAG,CAAC;;QAE5B,OAAOJ,OAAO,CAACI,GAAG,CAAC;;KAEtB,CAAC;IACF,IAAI8rB,MAAM,KAAK,CAAC,EAAE;MAChBlsB,OAAO,CAACmyC,EAAE,GAAGnyC,OAAO,CAACmyC,EAAE,GAAGnyC,OAAO,CAACmyC,EAAE,GAAG,CAAC;MACxCnyC,OAAO,CAACmyC,EAAE,IAAIjmB,MAAM;;IAEtB,OAAOlsB,OAAO;GACf;EAEDixC,eAAeA,CAACjxC,OAAO,EAAE;IACvB,IAAIksB,MAAM,GAAG,CAAC;IACd,IAAIlsB,OAAO,CAACs9B,KAAK,KAAKxF,SAAS,EAAE;MAC/B,IAAI,OAAOyW,aAAa,CAACvuC,OAAO,CAACs9B,KAAK,CAAC,KAAK,QAAQ,EAAE;QACpDpR,MAAM,GAAGqiB,aAAa,CAACvuC,OAAO,CAACs9B,KAAK,CAAC;;MAEvC,OAAOt9B,OAAO,CAACs9B,KAAK;;IAEtB,IAAIpR,MAAM,KAAK,CAAC,EAAE;MAChBlsB,OAAO,CAACgoB,CAAC,GAAGkE,MAAM,CAAC;;IAErB,OAAOlsB,OAAO;GACf;EAEDkxC,YAAYA,CAAClxC,OAAO,EAAE;;IAEpB,IAAI,IAAI,CAACyvC,SAAS,CAACzkC,KAAK,CAAC,IAAI,CAAC6wB,KAAK,CAAC95B,EAAE,CAAC,IAAI,IAAI,EAAE;MAC/C,IAAI,CAAC0tC,SAAS,CAACzkC,KAAK,CAAC,IAAI,CAAC6wB,KAAK,CAAC95B,EAAE,CAAC,GAAG,IAAI,CAAC85B,KAAK,CAAC35B,GAAG,EAAE;;;;IAIxD,IAAI,IAAI,CAACutC,SAAS,CAAChU,WAAW,KAAK,IAAI,CAACI,KAAK,CAACn6B,IAAI,EAAE;MAClD1B,OAAO,CAAC4vC,EAAE,GAAG;QAAE3kC,IAAI,EAAE;OAAI;;;MAGzB,MAAM+wB,QAAQ,GAAGh8B,OAAO,CAACg8B,QAAQ,IAAI,CAAC;MAEtCh8B,OAAO,CAAC4vC,EAAE,CAAC3kC,IAAI,CAAC,IAAI,CAAC4wB,KAAK,CAAC95B,EAAE,CAAC,GAAG,IAAI,CAAC85B,KAAK,CAAC35B,GAAG,EAAE;MACjDlC,OAAO,CAAC0oC,EAAE,GAAG,IAAIllC,MAAM,CAAE,IAAG,IAAI,CAACq4B,KAAK,CAAC95B,EAAG,IAAGi6B,QAAS,SAAQ,CAAC;;IAEjE,OAAOh8B,OAAO;GACf;EAEDmxC,eAAeA,CAACnxC,OAAO,EAAE;IACvB,IAAIoyC,MAAM,GAAG,EAAE;IACf,SAASC,aAAaA,CAAC1xC,CAAC,EAAE;MACxB,IAAImC,KAAK,CAAC4B,OAAO,CAAC/D,CAAC,CAAC,EAAE;QACpB,KAAK,IAAI2xC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG3xC,CAAC,CAACI,MAAM,EAAEuxC,GAAG,EAAE,EAAE;UACvC,IAAI,OAAO3xC,CAAC,CAAC2xC,GAAG,CAAC,KAAK,QAAQ,EAAE;YAC9BF,MAAM,CAAClxC,IAAI,CAAC,IAAIsC,MAAM,CAAC7C,CAAC,CAAC2xC,GAAG,CAAC,CAAC,CAAC;WAChC,MAAM;YACLF,MAAM,CAAClxC,IAAI,CAACP,CAAC,CAAC2xC,GAAG,CAAC,CAAC;;;;;IAK3BD,aAAa,CAACryC,OAAO,CAACuyC,GAAG,CAAC;IAC1B,IAAIvyC,OAAO,CAACoyC,MAAM,EAAE;MAClBC,aAAa,CAACryC,OAAO,CAACoyC,MAAM,CAAC;MAC7B,OAAOpyC,OAAO,CAACoyC,MAAM;;IAEvB,IAAIA,MAAM,CAACrxC,MAAM,EAAE;MACjBf,OAAO,CAACuyC,GAAG,GAAGH,MAAM;;IAGtB5xC,MAAM,CAACC,IAAI,CAACguC,SAAS,CAAC,CAAC1yB,OAAO,CAAC3b,GAAG,IAAI;MACpC,IAAIJ,OAAO,CAACI,GAAG,CAAC,KAAK03B,SAAS,EAAE;QAC9B93B,OAAO,CAACyuC,SAAS,CAACruC,GAAG,CAAC,CAAC,GAAGJ,OAAO,CAACI,GAAG,CAAC;QACtC,OAAOJ,OAAO,CAACI,GAAG,CAAC;;KAEtB,CAAC;IACF,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC2b,OAAO,CAAC3b,GAAG,IAAI;MACzB,IAAI,OAAOJ,OAAO,CAACI,GAAG,CAAC,KAAK,QAAQ,EAAE;QACpCJ,OAAO,CAACI,GAAG,CAAC,GAAG,IAAIoD,MAAM,CAACxD,OAAO,CAACI,GAAG,CAAC,CAAC;;KAE1C,CAAC;IAEF,IAAIJ,OAAO,CAAC+xC,EAAE,IAAI/xC,OAAO,CAAC+xC,EAAE,CAACl0B,EAAE,EAAE;MAC/B7d,OAAO,CAAC+xC,EAAE,CAACl0B,EAAE,GAAG,IAAIra,MAAM,CAACxD,OAAO,CAAC+xC,EAAE,CAACl0B,EAAE,CAAC;;IAE3C,IAAI7d,OAAO,CAACohC,KAAK,EAAE;MACjBphC,OAAO,CAAC+xC,EAAE,GAAG/xC,OAAO,CAAC+xC,EAAE,GAAG/xC,OAAO,CAAC+xC,EAAE,GAAG,EAAE;MACzC/xC,OAAO,CAAC+xC,EAAE,CAACl0B,EAAE,GAAG,IAAIra,MAAM,CAACxD,OAAO,CAACohC,KAAK,CAAC;MACzC,OAAOphC,OAAO,CAACohC,KAAK;;IAEtB,OAAOphC,OAAO;;AAElB,CAAC;;ACpYD,uBAAe;;AAEf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE4oC,IAAIA,CAAC/N,GAAG,EAAE76B,OAAO,GAAG,EAAE,EAAE;IACtBA,OAAO,CAAC0B,IAAI,GAAG1B,OAAO,CAAC0B,IAAI,IAAIm5B,GAAG;IAClC76B,OAAO,CAACwyC,YAAY,GAAGxyC,OAAO,CAACwyC,YAAY,IAAI,aAAa;IAE5D,MAAMC,OAAO,GAAG;MACdjoC,IAAI,EAAE,cAAc;MACpBkoC,MAAM,EAAE;KACT;IACD,IAAIttC,IAAI;IAER,IAAI,CAACy1B,GAAG,EAAE;MACR,MAAM,IAAIh7B,KAAK,CAAC,kBAAkB,CAAC;;IAErC,IAAIgE,MAAM,CAACK,QAAQ,CAAC22B,GAAG,CAAC,EAAE;MACxBz1B,IAAI,GAAGy1B,GAAG;KACX,MAAM,IAAIA,GAAG,YAAYI,WAAW,EAAE;MACrC71B,IAAI,GAAGvB,MAAM,CAACC,IAAI,CAAC,IAAIk3B,UAAU,CAACH,GAAG,CAAC,CAAC;KACxC,MAAM;MACL,IAAI/H,KAAK;MACT,IAAKA,KAAK,GAAG,0BAA0B,CAACqT,IAAI,CAACtL,GAAG,CAAC,EAAG;QAClD,IAAI/H,KAAK,CAAC,CAAC,CAAC,EAAE;UACZ2f,OAAO,CAAC15B,OAAO,GAAG+Z,KAAK,CAAC,CAAC,CAAC,CAAC9uB,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC;;QAEhDoB,IAAI,GAAGvB,MAAM,CAACC,IAAI,CAACgvB,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC;OACvC,MAAM;QACL1tB,IAAI,GAAG0sB,EAAE,CAACC,YAAY,CAAC8I,GAAG,CAAC;QAC3B,IAAI,CAACz1B,IAAI,EAAE;UACT,MAAM,IAAIvF,KAAK,CAAE,+CAA8Cg7B,GAAI,EAAC,CAAC;;;;QAIvE,MAAM;UAAE8X,SAAS;UAAEC;SAAO,GAAG9gB,EAAE,CAAC+gB,QAAQ,CAAChY,GAAG,CAAC;QAC7C4X,OAAO,CAACC,MAAM,CAAC7iC,YAAY,GAAG8iC,SAAS;QACvCF,OAAO,CAACC,MAAM,CAACI,OAAO,GAAGF,KAAK;;;;;IAKlC,IAAI5yC,OAAO,CAAC+yC,YAAY,YAAY5uC,IAAI,EAAE;MACxCsuC,OAAO,CAACC,MAAM,CAAC7iC,YAAY,GAAG7P,OAAO,CAAC+yC,YAAY;;IAEpD,IAAI/yC,OAAO,CAACgzC,YAAY,YAAY7uC,IAAI,EAAE;MACxCsuC,OAAO,CAACC,MAAM,CAACI,OAAO,GAAG9yC,OAAO,CAACgzC,YAAY;;;IAG/C,IAAIhzC,OAAO,CAAC0qC,IAAI,EAAE;MAChB+H,OAAO,CAAC15B,OAAO,GAAG/Y,OAAO,CAAC0qC,IAAI,CAAC1mC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC;;;;IAIpD,MAAMivC,QAAQ,GAAGhjC,QAAQ,CAACC,GAAG,CAC3BD,QAAQ,CAACI,GAAG,CAACC,SAAS,CAACE,MAAM,CAAC,IAAIwqB,UAAU,CAAC51B,IAAI,CAAC,CACpD,CAAC;IACDqtC,OAAO,CAACC,MAAM,CAACQ,QAAQ,GAAG,IAAI1vC,MAAM,CAACyvC,QAAQ,CAAC;IAC9CR,OAAO,CAACC,MAAM,CAACS,IAAI,GAAG/tC,IAAI,CAACguC,UAAU;;;;IAIrC,IAAIlxC,GAAG;IACP,IAAI,CAAC,IAAI,CAACmxC,aAAa,EAAE,IAAI,CAACA,aAAa,GAAG,EAAE;IAChD,IAAIzK,IAAI,GAAG,IAAI,CAACyK,aAAa,CAACrzC,OAAO,CAAC0B,IAAI,CAAC;IAC3C,IAAIknC,IAAI,IAAI0K,OAAO,CAACb,OAAO,EAAE7J,IAAI,CAAC,EAAE;MAClC1mC,GAAG,GAAG0mC,IAAI,CAAC1mC,GAAG;KACf,MAAM;MACLA,GAAG,GAAG,IAAI,CAACA,GAAG,CAACuwC,OAAO,CAAC;MACvBvwC,GAAG,CAACS,GAAG,CAACyC,IAAI,CAAC;MAEb,IAAI,CAACiuC,aAAa,CAACrzC,OAAO,CAAC0B,IAAI,CAAC,GAAG;QAAE,GAAG+wC,OAAO;QAAEvwC;OAAK;;;IAGxD,MAAMqxC,YAAY,GAAG;MACnB/oC,IAAI,EAAE,UAAU;MAChBgpC,cAAc,EAAExzC,OAAO,CAACwyC,YAAY;MACpC9K,CAAC,EAAE,IAAIlkC,MAAM,CAACxD,OAAO,CAAC0B,IAAI,CAAC;MAC3B+xC,EAAE,EAAE;QAAE/L,CAAC,EAAExlC;OAAK;MACdwxC,EAAE,EAAE,IAAIlwC,MAAM,CAACxD,OAAO,CAAC0B,IAAI;KAC5B;IACD,IAAI1B,OAAO,CAAC2zC,WAAW,EAAE;MACvBJ,YAAY,CAACvK,IAAI,GAAG,IAAIxlC,MAAM,CAACxD,OAAO,CAAC2zC,WAAW,CAAC;;IAErD,MAAM9K,QAAQ,GAAG,IAAI,CAAC3mC,GAAG,CAACqxC,YAAY,CAAC;IACvC1K,QAAQ,CAAClmC,GAAG,EAAE;IAEd,IAAI,CAAC3C,OAAO,CAAC8oC,MAAM,EAAE;MACnB,IAAI,CAAC8K,oBAAoB,CAAC5zC,OAAO,CAAC0B,IAAI,EAAEmnC,QAAQ,CAAC;;;;IAInD,IAAI,IAAI,CAACn+B,KAAK,CAACtF,IAAI,CAACyuC,EAAE,EAAE;MACtB,IAAI,CAACnpC,KAAK,CAACtF,IAAI,CAACyuC,EAAE,CAAC3yC,IAAI,CAAC2nC,QAAQ,CAAC;KAClC,MAAM;MACL,IAAI,CAACn+B,KAAK,CAACtF,IAAI,CAACyuC,EAAE,GAAG,CAAChL,QAAQ,CAAC;;IAGjC,OAAOA,QAAQ;;AAEnB,CAAC;;AAED;AACA,SAASyK,OAAOA,CAAC3yC,CAAC,EAAEC,CAAC,EAAE;EACrB,OACED,CAAC,CAACoY,OAAO,KAAKnY,CAAC,CAACmY,OAAO,IACvBpY,CAAC,CAAC+xC,MAAM,CAACQ,QAAQ,CAACtzC,QAAQ,EAAE,KAAKgB,CAAC,CAAC8xC,MAAM,CAACQ,QAAQ,CAACtzC,QAAQ,EAAE,IAC7De,CAAC,CAAC+xC,MAAM,CAACS,IAAI,KAAKvyC,CAAC,CAAC8xC,MAAM,CAACS,IAAI,IAC/BxyC,CAAC,CAAC+xC,MAAM,CAAC7iC,YAAY,CAACC,OAAO,EAAE,KAAKlP,CAAC,CAAC8xC,MAAM,CAAC7iC,YAAY,CAACC,OAAO,EAAE,KACjEnP,CAAC,CAAC+xC,MAAM,CAACI,OAAO,KAAKhb,SAAS,IAAIl3B,CAAC,CAAC8xC,MAAM,CAACI,OAAO,KAAKhb,SAAS,IAChEn3B,CAAC,CAAC+xC,MAAM,CAACI,OAAO,CAAChjC,OAAO,EAAE,KAAKlP,CAAC,CAAC8xC,MAAM,CAACI,OAAO,CAAChjC,OAAO,EAAE,CAAC;AAEhE;;AC3HA,WAAe;EACbgkC,QAAQA,CAACC,OAAO,EAAE;IAChB,IAAIA,OAAO,CAACl3B,MAAM,CAACk3B,OAAO,CAAChzC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;MAC9C,IAAI,CAACizC,kBAAkB,GAAGD,OAAO,CAC9Bl3B,MAAM,CAACk3B,OAAO,CAAChzC,MAAM,GAAG,CAAC,CAAC,CAC1BkJ,WAAW,EAAE;MAChB,IAAI,CAACgsB,MAAM,GAAGlZ,QAAQ,CAACg3B,OAAO,CAACl3B,MAAM,CAACk3B,OAAO,CAAChzC,MAAM,GAAG,CAAC,CAAC,CAAC;KAC3D,MAAM;;MAEL,IAAI,CAACizC,kBAAkB,GAAG,GAAG;MAC7B,IAAI,CAAC/d,MAAM,GAAGlZ,QAAQ,CAACg3B,OAAO,CAACl3B,MAAM,CAACk3B,OAAO,CAAChzC,MAAM,GAAG,CAAC,CAAC,CAAC;;GAE7D;EAEDkzC,SAASA,GAAG;IACV,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,qBAAqB,EAAE;GAC7B;EAEDA,qBAAqBA,GAAG;IACtB,MAAMC,UAAU,GAAGtiB,EAAE,CAACC,YAAY,CAAE,GAAEoC,SAAU,6BAA4B,CAAC;IAE7E,MAAMkgB,eAAe,GAAG,IAAI,CAACnyC,GAAG,CAAC;MAC/B0D,MAAM,EAAEwuC,UAAU,CAACrzC,MAAM;MACzB2B,CAAC,EAAE;KACJ,CAAC;IACF2xC,eAAe,CAAC3uC,KAAK,CAAC0uC,UAAU,CAAC;IACjCC,eAAe,CAAC1xC,GAAG,EAAE;IAErB,MAAM2xC,SAAS,GAAG,IAAI,CAACpyC,GAAG,CAAC;MACzBsI,IAAI,EAAE,cAAc;MACpB2O,CAAC,EAAE,WAAW;MACdo7B,IAAI,EAAE,IAAI/wC,MAAM,CAAC,mBAAmB,CAAC;MACrCgxC,yBAAyB,EAAE,IAAIhxC,MAAM,CAAC,mBAAmB,CAAC;MAC1DixC,iBAAiB,EAAEJ;KACpB,CAAC;IACFC,SAAS,CAAC3xC,GAAG,EAAE;IAEf,IAAI,CAAC+H,KAAK,CAACtF,IAAI,CAACsvC,aAAa,GAAG,CAACJ,SAAS,CAAC;GAC5C;EAEDK,UAAUA,GAAG;IACX,OAAQ;AACZ;AACA,2BAA2B,IAAI,CAAC1e,MAAO;AACvC,kCAAkC,IAAI,CAAC+d,kBAAmB;AAC1D;AACA,SAAS;GACN;EAEDE,gBAAgBA,GAAG;IACjB,IAAI,CAACU,SAAS,CAAC,IAAI,CAACD,UAAU,EAAE,CAAC;;AAErC,CAAC;;ACtDD,YAAe;EAEXE,SAASA,GAAG;IACR,IAAI,CAAC5e,MAAM,GAAG,CAAC;GAClB;EAEDge,SAASA,GAAG;IACR,IAAI,CAACa,iBAAiB,EAAE;GAC3B;EAEDA,iBAAiBA,GAAG;IAChB,IAAI,CAACF,SAAS,CAAC,IAAI,CAACG,WAAW,EAAE,CAAC;GACrC;EAEDA,WAAWA,GAAG;IACV,OAAQ;AAChB;AACA,4BAA4B,IAAI,CAAC9e,MAAO;AACxC;AACA,SAAS;;AAGT,CAAC;;ACpBD,kBAAe;EACX+e,aAAaA,CAAC/e,MAAM,EAAE;IAClBz1B,MAAM,CAACuhC,MAAM,CAAC,IAAI,EAAE9L,MAAM,CAAC;GAC9B;EAEDgf,UAAUA,CAACj1C,OAAO,EAAE;IAEhB,QAAQA,OAAO,CAACi2B,MAAM;MAClB,KAAK,SAAS;MACd,KAAK,UAAU;MACf,KAAK,UAAU;MACf,KAAK,SAAS;MACd,KAAK,UAAU;MACf,KAAK,UAAU;MACf,KAAK,SAAS;MACd,KAAK,UAAU;MACf,KAAK,UAAU;QACX,IAAI,CAAC+e,aAAa,CAACE,IAAI,CAAC;QACxB,IAAI,CAACpB,QAAQ,CAAC9zC,OAAO,CAACi2B,MAAM,CAAC;QAC7B;MACJ,KAAK,QAAQ;QACT,IAAI,CAAC+e,aAAa,CAACG,KAAK,CAAC;QACzB,IAAI,CAACN,SAAS,EAAE;QAChB;;;AAGhB,CAAC;;AC5BD,MAAMO,WAAW,CAAC;EACdr1C,WAAWA,GAAG;IACV,IAAI,CAACs1C,SAAS,GAAI;AAC1B;AACA;AACA;AACA,SAAS;;EAGLC,UAAUA,GAAG;IACT,IAAI,CAACD,SAAS,GAAG,IAAI,CAACA,SAAS,CAACnvC,MAAM,CAAE;AAChD;AACA;AACA;AACA,SAAS,CAAC;;EAGNqvC,MAAMA,CAACC,GAAG,EAAEC,OAAO,GAAC,IAAI,EAAE;IACtB,IAAI,CAACJ,SAAS,GAAG,IAAI,CAACA,SAAS,CAACnvC,MAAM,CAACsvC,GAAG,CAAC;IAC3C,IAAIC,OAAO,EACP,IAAI,CAACJ,SAAS,GAAG,IAAI,CAACA,SAAS,CAACnvC,MAAM,CAAC,IAAI,CAAC;;EAGpDwvC,MAAMA,GAAG;IAAE,OAAO,IAAI,CAACL,SAAS;;EAEhCM,SAASA,GAAG;IAAE,OAAO,IAAI,CAACN,SAAS,CAACt0C,MAAM;;EAE1C4B,GAAGA,GAAG;IACF,IAAI,CAAC2yC,UAAU,EAAE;IACjB,IAAI,CAACD,SAAS,GAAG,IAAI,CAACA,SAAS,CAACpT,IAAI,EAAE;;AAE9C;;AC9BA,oBAAe;EACX2T,YAAYA,GAAG;IACX,IAAI,CAACC,QAAQ,GAAG,IAAIT,WAAW,EAAE;GACpC;EAEDR,SAASA,CAACY,GAAG,EAAEC,OAAO,GAAC,IAAI,EAAE;IAAE,IAAI,CAACI,QAAQ,CAACN,MAAM,CAACC,GAAG,EAACC,OAAO,CAAC;GAAG;EAEnEK,QAAQA,GAAG;IACP,IAAI,CAAClB,SAAS,CAAE;AACxB;AACA,8BAA8B,IAAI,CAACjlC,IAAI,CAACE,YAAY,CAACkmC,WAAW,EAAE,CAACrkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAC,GAAI;AACrF,+BAA+B,IAAI,CAAC/hB,IAAI,CAACqmC,OAAQ;AACjD;AACA,SACQ,CAAC;IAED,IAAI,IAAI,CAACrmC,IAAI,CAACsmC,KAAK,IAAI,IAAI,CAACtmC,IAAI,CAACumC,MAAM,IAAI,IAAI,CAACvmC,IAAI,CAACwmC,OAAO,EAAE;MAC1D,IAAI,CAACvB,SAAS,CAAE;AAC5B;AACA,aAAa,CAAC;MAEF,IAAI,IAAI,CAACjlC,IAAI,CAACsmC,KAAK,EAAE;QACjB,IAAI,CAACrB,SAAS,CAAE;AAChC;AACA;AACA,uDAAuD,IAAI,CAACjlC,IAAI,CAACsmC,KAAM;AACvE;AACA;AACA,iBAAiB,CAAC;;MAGN,IAAI,IAAI,CAACtmC,IAAI,CAACumC,MAAM,EAAE;QAClB,IAAI,CAACtB,SAAS,CAAE;AAChC;AACA;AACA,kCAAkC,IAAI,CAACjlC,IAAI,CAACumC,MAAO;AACnD;AACA;AACA,iBAAiB,CAAC;;MAGN,IAAI,IAAI,CAACvmC,IAAI,CAACwmC,OAAO,EAAE;QACnB,IAAI,CAACvB,SAAS,CAAE;AAChC;AACA;AACA,uDAAuD,IAAI,CAACjlC,IAAI,CAACwmC,OAAQ;AACzE;AACA;AACA,iBAAiB,CAAC;;MAGN,IAAI,CAACvB,SAAS,CAAE;AAC5B;AACA,aAAa,CAAC;;IAGN,IAAI,CAACA,SAAS,CAAE;AACxB;AACA,4BAA4B,IAAI,CAACjlC,IAAI,CAACqmC,OAAQ,iBAAgB,EAAE,KAAK,CAAC;IAE9D,IAAI,IAAI,CAACrmC,IAAI,CAACymC,QAAQ,EAAE;MACpB,IAAI,CAACxB,SAAS,CAAE;AAC5B,4BAA4B,IAAI,CAACjlC,IAAI,CAACymC,QAAS,iBAAgB,EAAE,KAAK,CAAC;;IAG/D,IAAI,CAACxB,SAAS,CAAE;AACxB;AACA,SAAS,CAAC;GACL;EAEDyB,WAAWA,GAAG;IACV,IAAI,CAACP,QAAQ,EAAE;IAEf,IAAI,CAACD,QAAQ,CAAClzC,GAAG,EAAE;;;AAG3B;AACA;AACA;IACQ,IAAI,IAAI,CAACkO,OAAO,IAAI,GAAG,EAAE;MACrB,IAAI,CAACylC,WAAW,GAAG,IAAI,CAACp0C,GAAG,CAAC;QACxBnB,MAAM,EAAE,IAAI,CAAC80C,QAAQ,CAACF,SAAS,EAAE;QACjCnrC,IAAI,EAAE,UAAU;QAChBuO,OAAO,EAAE;OACZ,CAAC;MACF,IAAI,CAACu9B,WAAW,CAAChxC,QAAQ,GAAG,KAAK;MACjC,IAAI,CAACgxC,WAAW,CAAC5wC,KAAK,CAAC7B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC+xC,QAAQ,CAACH,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;MACpE,IAAI,CAACY,WAAW,CAAC3zC,GAAG,EAAE;MACtB,IAAI,CAAC+H,KAAK,CAACtF,IAAI,CAACmxC,QAAQ,GAAG,IAAI,CAACD,WAAW;;;AAGvD,CAAC;;AC7FD;AACA;AACA;AACA;AAsBA,MAAME,WAAW,SAAS76B,MAAM,CAAC86B,QAAQ,CAAC;EACxC12C,WAAWA,CAACC,OAAO,GAAG,EAAE,EAAE;IACxB,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACA,OAAO,GAAGA,OAAO;;;IAGtB,QAAQA,OAAO,CAAC4Q,UAAU;MACxB,KAAK,KAAK;QACR,IAAI,CAACC,OAAO,GAAG,GAAG;QAClB;MACF,KAAK,KAAK;QACR,IAAI,CAACA,OAAO,GAAG,GAAG;QAClB;MACF,KAAK,KAAK;QACR,IAAI,CAACA,OAAO,GAAG,GAAG;QAClB;MACF,KAAK,KAAK;MACV,KAAK,SAAS;QACZ,IAAI,CAACA,OAAO,GAAG,GAAG;QAClB;MACF;QACE,IAAI,CAACA,OAAO,GAAG,GAAG;QAClB;;;;IAIJ,IAAI,CAACvL,QAAQ,GACX,IAAI,CAACtF,OAAO,CAACsF,QAAQ,IAAI,IAAI,GAAG,IAAI,CAACtF,OAAO,CAACsF,QAAQ,GAAG,IAAI;IAE9D,IAAI,CAACoxC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,gBAAgB,GAAG,CAAC;;;IAGzB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACjM,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC7kC,OAAO,GAAG,CAAC;IAChB,MAAM4E,KAAK,GAAG,IAAI,CAACzI,GAAG,CAAC;MACrBsI,IAAI,EAAE,OAAO;MACbk/B,KAAK,EAAE,CAAC;MACRzB,IAAI,EAAE;KACP,CAAC;IAEF,MAAM6O,KAAK,GAAG,IAAI,CAAC50C,GAAG,CAAC;MACrB60C,KAAK,EAAE,IAAI3qC,WAAW;KACvB,CAAC;IAEF,IAAI,CAAC1B,KAAK,GAAG,IAAI,CAACxI,GAAG,CAAC;MACpBsI,IAAI,EAAE,SAAS;MACfG,KAAK;MACLmsC;KACD,CAAC;IAEF,IAAI,IAAI,CAAC92C,OAAO,CAAC+qC,IAAI,EAAE;MACrB,IAAI,CAACrgC,KAAK,CAACtF,IAAI,CAAC4lC,IAAI,GAAG,IAAIxnC,MAAM,CAAC,IAAI,CAACxD,OAAO,CAAC+qC,IAAI,CAAC;;;;IAItD,IAAI,CAAClyB,IAAI,GAAG,IAAI;;;IAGhB,IAAI,CAAC+8B,YAAY,EAAE;IACnB,IAAI,CAACl5B,SAAS,EAAE;IAChB,IAAI,CAACgQ,UAAU,EAAE;IACjB,IAAI,CAAC8O,SAAS,CAACx7B,OAAO,CAACk1B,IAAI,CAAC;IAC5B,IAAI,CAACiK,QAAQ,EAAE;IACf,IAAI,CAACiH,UAAU,EAAE;IACjB,IAAI,CAAC4D,WAAW,EAAE;IAClB,IAAI,CAACsC,YAAY,CAACtsC,OAAO,CAAC;IAC1B,IAAI,CAACi1C,UAAU,CAACj1C,OAAO,CAAC;;;IAGxB,IAAI,CAAC2P,IAAI,GAAG;MACVqnC,QAAQ,EAAE,QAAQ;MAClBhB,OAAO,EAAE,QAAQ;MACjBnmC,YAAY,EAAE,IAAI1L,IAAI;KACvB;IAED,IAAI,IAAI,CAACnE,OAAO,CAAC2P,IAAI,EAAE;MACrB,KAAK,IAAIvP,GAAG,IAAI,IAAI,CAACJ,OAAO,CAAC2P,IAAI,EAAE;QACjC,MAAMtP,GAAG,GAAG,IAAI,CAACL,OAAO,CAAC2P,IAAI,CAACvP,GAAG,CAAC;QAClC,IAAI,CAACuP,IAAI,CAACvP,GAAG,CAAC,GAAGC,GAAG;;;IAIxB,IAAI,IAAI,CAACL,OAAO,CAACi3C,YAAY,EAAE;MAC7B,IAAI,CAACvsC,KAAK,CAACtF,IAAI,CAAC8xC,iBAAiB,GAAG,IAAI,CAACh1C,GAAG,CAAC;QAC3Ci1C,eAAe,EAAE;OAClB,CAAC;;;;IAIJ,IAAI,CAACrlC,GAAG,GAAGrC,WAAW,CAACC,cAAc,CAAC,IAAI,CAACC,IAAI,CAAC;;;IAGhD,IAAI,CAAC3J,SAAS,GAAGyJ,WAAW,CAACe,MAAM,CAAC,IAAI,EAAExQ,OAAO,CAAC;;;;IAIlD,IAAI,CAACqG,MAAM,CAAE,QAAO,IAAI,CAACwK,OAAQ,EAAC,CAAC;;;IAGnC,IAAI,CAACxK,MAAM,CAAC,mBAAmB,CAAC;;;IAGhC,IAAI,IAAI,CAACrG,OAAO,CAACo3C,aAAa,KAAK,KAAK,EAAE;MACxC,IAAI,CAACC,OAAO,EAAE;;;EAIlBA,OAAOA,CAACr3C,OAAO,EAAE;IACf,IAAIA,OAAO,IAAI,IAAI,EAAE;MACnB,CAAC;QAAEA;OAAS,GAAG,IAAI;;;;IAIrB,IAAI,CAAC,IAAI,CAACA,OAAO,CAACs3C,WAAW,EAAE;MAC7B,IAAI,CAACC,UAAU,EAAE;;;;IAInB,IAAI,CAAC1+B,IAAI,GAAG,IAAIlP,OAAO,CAAC,IAAI,EAAE3J,OAAO,CAAC;IACtC,IAAI,CAAC02C,WAAW,CAACx1C,IAAI,CAAC,IAAI,CAAC2X,IAAI,CAAC;;;IAGhC,MAAMmvB,KAAK,GAAG,IAAI,CAACt9B,KAAK,CAACtF,IAAI,CAACuF,KAAK,CAACvF,IAAI;IACxC4iC,KAAK,CAACC,IAAI,CAAC/mC,IAAI,CAAC,IAAI,CAAC2X,IAAI,CAACtO,UAAU,CAAC;IACrCy9B,KAAK,CAAC0B,KAAK,EAAE;;;IAGb,IAAI,CAACz7B,CAAC,GAAG,IAAI,CAAC4K,IAAI,CAAC9O,OAAO,CAACtD,IAAI;IAC/B,IAAI,CAAC+iB,CAAC,GAAG,IAAI,CAAC3Q,IAAI,CAAC9O,OAAO,CAACvD,GAAG;;;;IAI9B,IAAI,CAAC8T,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC9B,IAAI,CAAC1D,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACiC,IAAI,CAAC1O,MAAM,CAAC;IAEhD,IAAI,CAAC60B,IAAI,CAAC,WAAW,CAAC;IAEtB,OAAO,IAAI;;EAGbE,iBAAiBA,CAACl/B,OAAO,EAAE;IACzB,MAAMotC,YAAY,GAAG,IAAI,CAACE,eAAe,CAAC,IAAI,CAACz0B,IAAI,CAAC;IAEpD,IAAI,CAACw+B,OAAO,CAACr3C,OAAO,CAAC;IAErB,IAAI,CAACmtC,gBAAgB,CAACC,YAAY,CAAC;IAEnC,OAAO,IAAI;;EAGboK,iBAAiBA,GAAG;IAClB,OAAO;MAAE7c,KAAK,EAAE,IAAI,CAACgc,gBAAgB;MAAEc,KAAK,EAAE,IAAI,CAACf,WAAW,CAAC31C;KAAQ;;EAGzE22C,YAAYA,CAAC3yC,CAAC,EAAE;IACd,IAAI8T,IAAI;IACR,IAAI,EAAEA,IAAI,GAAG,IAAI,CAAC69B,WAAW,CAAC3xC,CAAC,GAAG,IAAI,CAAC4xC,gBAAgB,CAAC,CAAC,EAAE;MACzD,MAAM,IAAI92C,KAAK,CACZ,gBAAekF,CAAE,gDAChB,IAAI,CAAC4xC,gBACN,OAAM,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACD,WAAW,CAAC31C,MAAM,GAAG,CAAE,EAC7D,CAAC;;IAGH,OAAQ,IAAI,CAAC8X,IAAI,GAAGA,IAAI;;EAG1B0+B,UAAUA,GAAG;;;IAGX,MAAMvP,KAAK,GAAG,IAAI,CAAC0O,WAAW;IAC9B,IAAI,CAACA,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,gBAAgB,IAAI3O,KAAK,CAACjnC,MAAM;IACrC,KAAK,IAAI8X,IAAI,IAAImvB,KAAK,EAAE;MACtB,IAAI,CAACsF,eAAe,CAACz0B,IAAI,CAAC;MAC1BA,IAAI,CAAClW,GAAG,EAAE;;;EAId6/B,mBAAmBA,CAAC9gC,IAAI,EAAE,GAAGinB,IAAI,EAAE;IACjC,IAAIA,IAAI,CAAC5nB,MAAM,KAAK,CAAC,EAAE;MACrB4nB,IAAI,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;;IAElC,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;MACzCA,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC9P,IAAI,CAAC1O,MAAM,GAAGwe,IAAI,CAAC,CAAC,CAAC;;IAEtCA,IAAI,CAACgvB,OAAO,CAAC,IAAI,CAAC9+B,IAAI,CAACtO,UAAU,CAAC;IAClC,IAAI,CAACG,KAAK,CAACtF,IAAI,CAAC0xC,KAAK,CAAC1xC,IAAI,CAAC2xC,KAAK,CAAC52C,GAAG,CAACuB,IAAI,EAAEinB,IAAI,CAAC;;EAGlDirB,oBAAoBA,CAAClyC,IAAI,EAAEQ,GAAG,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACwI,KAAK,CAACtF,IAAI,CAAC0xC,KAAK,CAAC1xC,IAAI,CAACwyC,aAAa,EAAE;;MAE7C,IAAI,CAACltC,KAAK,CAACtF,IAAI,CAAC0xC,KAAK,CAAC1xC,IAAI,CAACwyC,aAAa,GAAG,IAAIxrC,WAAW,CAAC;QACzDlM,MAAM,EAAE;OACT,CAAC;;;;IAIJ,IAAI,CAACwK,KAAK,CAACtF,IAAI,CAAC0xC,KAAK,CAAC1xC,IAAI,CAACwyC,aAAa,CAACz3C,GAAG,CAACuB,IAAI,EAAEQ,GAAG,CAAC;;EAGzD21C,kBAAkBA,CAACn2C,IAAI,EAAEo2C,EAAE,EAAE;IAC3B,IAAI,CAAC,IAAI,CAACptC,KAAK,CAACtF,IAAI,CAAC0xC,KAAK,CAAC1xC,IAAI,CAAC2yC,UAAU,EAAE;MAC1C,IAAI,CAACrtC,KAAK,CAACtF,IAAI,CAAC0xC,KAAK,CAAC1xC,IAAI,CAAC2yC,UAAU,GAAG,IAAI3rC,WAAW,EAAE;;IAE3D,IAAIhH,IAAI,GAAG;MACTysC,EAAE,EAAE,IAAIruC,MAAM,CAACs0C,EAAE,CAAC;MAClB3+B,CAAC,EAAE;KACJ;IACD,IAAI,CAACzO,KAAK,CAACtF,IAAI,CAAC0xC,KAAK,CAAC1xC,IAAI,CAAC2yC,UAAU,CAAC53C,GAAG,CAACuB,IAAI,EAAE0D,IAAI,CAAC;;EAGvDlD,GAAGA,CAACkD,IAAI,EAAE;IACR,MAAMlD,GAAG,GAAG,IAAIgD,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC0xC,QAAQ,CAAC71C,MAAM,GAAG,CAAC,EAAEqE,IAAI,CAAC;IAClE,IAAI,CAACwxC,QAAQ,CAAC11C,IAAI,CAAC,IAAI,CAAC,CAAC;IACzB,IAAI,CAAC21C,QAAQ,EAAE;IACf,OAAO30C,GAAG;;EAGZ81C,KAAKA,GAAG;;;EAGR3xC,MAAMA,CAACjB,IAAI,EAAE;IACX,IAAI,CAACvB,MAAM,CAACK,QAAQ,CAACkB,IAAI,CAAC,EAAE;MAC1BA,IAAI,GAAGvB,MAAM,CAACC,IAAI,CAACsB,IAAI,GAAG,IAAI,EAAE,QAAQ,CAAC;;IAG3C,IAAI,CAAClE,IAAI,CAACkE,IAAI,CAAC;IACf,OAAQ,IAAI,CAACW,OAAO,IAAIX,IAAI,CAACrE,MAAM;;EAGrC0Z,UAAUA,CAACrV,IAAI,EAAE;IACf,IAAI,CAACyT,IAAI,CAACnT,KAAK,CAACN,IAAI,CAAC;IACrB,OAAO,IAAI;;EAGbkB,OAAOA,CAACpE,GAAG,EAAE;IACX,IAAI,CAAC00C,QAAQ,CAAC10C,GAAG,CAACH,EAAE,GAAG,CAAC,CAAC,GAAGG,GAAG,CAAC4D,MAAM;IACtC,IAAI,EAAE,IAAI,CAAC+wC,QAAQ,KAAK,CAAC,IAAI,IAAI,CAACjM,MAAM,EAAE;MACxC,IAAI,CAACqN,SAAS,EAAE;MAChB,OAAQ,IAAI,CAACrN,MAAM,GAAG,KAAK;;;EAI/BjoC,GAAGA,GAAG;IACJ,IAAI,CAAC40C,UAAU,EAAE;IAEjB,IAAI,CAACW,KAAK,GAAG,IAAI,CAACh2C,GAAG,EAAE;IACvB,KAAK,IAAI9B,GAAG,IAAI,IAAI,CAACuP,IAAI,EAAE;MACzB,IAAItP,GAAG,GAAG,IAAI,CAACsP,IAAI,CAACvP,GAAG,CAAC;MACxB,IAAI,OAAOC,GAAG,KAAK,QAAQ,EAAE;QAC3BA,GAAG,GAAG,IAAImD,MAAM,CAACnD,GAAG,CAAC;;MAGvB,IAAI83C,KAAK,GAAG,IAAI,CAACj2C,GAAG,CAAC7B,GAAG,CAAC;MACzB83C,KAAK,CAACx1C,GAAG,EAAE;MAEX,IAAI,CAACu1C,KAAK,CAAC9yC,IAAI,CAAChF,GAAG,CAAC,GAAG+3C,KAAK;;IAG9B,IAAI,CAACD,KAAK,CAACv1C,GAAG,EAAE;IAEhB,KAAK,IAAIjB,IAAI,IAAI,IAAI,CAACg6B,aAAa,EAAE;MACnC,MAAMxG,IAAI,GAAG,IAAI,CAACwG,aAAa,CAACh6B,IAAI,CAAC;MACrCwzB,IAAI,CAACrvB,QAAQ,EAAE;;IAGjB,IAAI,CAAC4jC,UAAU,EAAE;IACjB,IAAI,CAACkE,WAAW,EAAE;IAElB,IAAI,IAAI,CAAC1X,MAAM,EAAE;MACf,IAAI,CAACge,SAAS,EAAE;;IAGlB,IAAI,CAACoC,WAAW,EAAE;IAElB,IAAI,CAAC3rC,KAAK,CAAC/H,GAAG,EAAE;IAChB,IAAI,CAAC+H,KAAK,CAACtF,IAAI,CAACuF,KAAK,CAAChI,GAAG,EAAE;IAC3B,IAAI,CAAC+H,KAAK,CAACtF,IAAI,CAAC0xC,KAAK,CAACn0C,GAAG,EAAE;IAC3B,IAAI,CAACmtC,WAAW,EAAE;IAElB,IAAI,IAAI,CAACplC,KAAK,CAACtF,IAAI,CAAC8xC,iBAAiB,EAAE;MACrC,IAAI,CAACxsC,KAAK,CAACtF,IAAI,CAAC8xC,iBAAiB,CAACv0C,GAAG,EAAE;;IAGzC,IAAI,IAAI,CAACqD,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACrD,GAAG,EAAE;;IAGtB,IAAI,IAAI,CAACk0C,QAAQ,KAAK,CAAC,EAAE;MACvB,OAAO,IAAI,CAACoB,SAAS,EAAE;KACxB,MAAM;MACL,OAAQ,IAAI,CAACrN,MAAM,GAAG,IAAI;;;EAI9BqN,SAASA,GAAG;;IAEV,MAAMG,UAAU,GAAG,IAAI,CAACryC,OAAO;IAC/B,IAAI,CAACM,MAAM,CAAC,MAAM,CAAC;IACnB,IAAI,CAACA,MAAM,CAAE,KAAI,IAAI,CAACuwC,QAAQ,CAAC71C,MAAM,GAAG,CAAE,EAAC,CAAC;IAC5C,IAAI,CAACsF,MAAM,CAAC,qBAAqB,CAAC;IAElC,KAAK,IAAIP,MAAM,IAAI,IAAI,CAAC8wC,QAAQ,EAAE;MAChC9wC,MAAM,GAAI,aAAYA,MAAO,EAAC,CAAC/C,KAAK,CAAC,CAAC,EAAE,CAAC;MACzC,IAAI,CAACsD,MAAM,CAACP,MAAM,GAAG,WAAW,CAAC;;;;IAInC,MAAMuyC,OAAO,GAAG;MACdlF,IAAI,EAAE,IAAI,CAACyD,QAAQ,CAAC71C,MAAM,GAAG,CAAC;MAC9Bu3C,IAAI,EAAE,IAAI,CAAC5tC,KAAK;MAChB6pC,IAAI,EAAE,IAAI,CAAC2D,KAAK;MAChBK,EAAE,EAAE,CAAC,IAAI,CAACzmC,GAAG,EAAE,IAAI,CAACA,GAAG;KACxB;IACD,IAAI,IAAI,CAAC9L,SAAS,EAAE;MAClBqyC,OAAO,CAACG,OAAO,GAAG,IAAI,CAACxyC,SAAS,CAACuE,UAAU;;IAG7C,IAAI,CAAClE,MAAM,CAAC,SAAS,CAAC;IACtB,IAAI,CAACA,MAAM,CAAClF,SAAS,CAACC,OAAO,CAACi3C,OAAO,CAAC,CAAC;IAEvC,IAAI,CAAChyC,MAAM,CAAC,WAAW,CAAC;IACxB,IAAI,CAACA,MAAM,CAAE,GAAE+xC,UAAW,EAAC,CAAC;IAC5B,IAAI,CAAC/xC,MAAM,CAAC,OAAO,CAAC;;;IAGpB,OAAO,IAAI,CAACnF,IAAI,CAAC,IAAI,CAAC;;EAGxBtB,QAAQA,GAAG;IACT,OAAO,sBAAsB;;AAEjC;AAEA,MAAM64C,KAAK,GAAGC,OAAO,IAAI;EACvBl4C,MAAM,CAACuhC,MAAM,CAACyU,WAAW,CAACmC,SAAS,EAAED,OAAO,CAAC;AAC/C,CAAC;AAEDD,KAAK,CAACG,aAAa,CAAC;AACpBH,KAAK,CAACI,UAAU,CAAC;AACjBJ,KAAK,CAACK,WAAW,CAAC;AAClBL,KAAK,CAACM,UAAU,CAAC;AACjBN,KAAK,CAACO,SAAS,CAAC;AAChBP,KAAK,CAACQ,WAAW,CAAC;AAClBR,KAAK,CAACS,gBAAgB,CAAC;AACvBT,KAAK,CAACU,YAAY,CAAC;AACnBV,KAAK,CAACW,aAAa,CAAC;AACpBX,KAAK,CAACY,aAAa,CAAC;AACpBZ,KAAK,CAACa,gBAAgB,CAAC;AACvBb,KAAK,CAACc,WAAW,CAAC;AAElB/C,WAAW,CAACna,WAAW,GAAGA,WAAW;;;;"}