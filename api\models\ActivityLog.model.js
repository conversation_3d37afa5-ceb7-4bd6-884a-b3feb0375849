const mongoose = require('mongoose');

const activityLogSchema = new mongoose.Schema({
  // Basic identification
  storeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Store', required: true },
  deviceId: { type: String, required: true },
  sessionId: { type: String, required: true },
  
  // Activity details
  activityType: {
    type: String,
    enum: [
      'music_player_start',
      'music_player_stop',
      'music_player_pause',
      'music_player_resume',
      'radio_start',
      'radio_stop',
      'radio_pause',
      'radio_resume',
      'source_switch',
      'volume_change',
      'playlist_change',
      'station_change'
    ],
    required: true
  },
  
  // Source information
  sourceFrom: {
    type: String,
    enum: ['music_player', 'radio_stream', 'silence', 'unknown'],
    default: 'unknown'
  },
  sourceTo: {
    type: String,
    enum: ['music_player', 'radio_stream', 'silence', 'unknown'],
    default: 'unknown'
  },
  
  // Music player specific data
  musicPlayerData: {
    trackId: { type: mongoose.Schema.Types.ObjectId, ref: 'Track' },
    playlistId: { type: mongoose.Schema.Types.ObjectId, ref: 'Playlist' },
    playlistName: String,
    trackTitle: String,
    trackArtist: String,
    trackPosition: Number, // Position in playlist
    isShuffled: { type: Boolean, default: false },
    isRepeating: { type: Boolean, default: false },
    volume: Number
  },
  
  // Radio specific data
  radioData: {
    stationId: { type: mongoose.Schema.Types.ObjectId, ref: 'RadioStation' },
    stationName: String,
    stationUrl: String,
    stationGenre: String,
    stationCountry: String,
    volume: Number,
    streamQuality: String
  },
  
  // Timing information
  timestamp: { type: Date, default: Date.now },
  duration: Number, // Duration of the activity in seconds
  
  // Context information
  context: {
    userAgent: String,
    ipAddress: String,
    timezone: { type: String, default: 'Africa/Johannesburg' },
    storeHours: {
      isOpen: Boolean,
      openTime: String,
      closeTime: String
    },
    scheduledActivity: Boolean, // Was this a scheduled activity
    manualTrigger: Boolean, // Was this manually triggered by user
    automaticTrigger: Boolean // Was this an automatic system trigger
  },
  
  // Compliance tracking
  compliance: {
    requiresReporting: { type: Boolean, default: true },
    reportedToSAMRO: { type: Boolean, default: false },
    reportedToSAMPRA: { type: Boolean, default: false },
    reportedToRISA: { type: Boolean, default: false },
    reportingDate: Date,
    complianceNotes: String
  },
  
  // Technical details
  technical: {
    audioFormat: String,
    bitrate: String,
    sampleRate: String,
    bufferHealth: Number, // For streaming quality monitoring
    connectionType: String, // wifi, cellular, ethernet
    errorOccurred: Boolean,
    errorMessage: String,
    performanceMetrics: {
      loadTime: Number,
      bufferTime: Number,
      latency: Number
    }
  },
  
  // Audit information
  auditInfo: {
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    verifiedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    verificationStatus: {
      type: String,
      enum: ['pending', 'verified', 'flagged', 'requires_review'],
      default: 'pending'
    },
    notes: String,
    tags: [String]
  }
}, {
  timestamps: true,
  // Add indexes for efficient querying
  indexes: [
    { storeId: 1, timestamp: -1 },
    { activityType: 1, timestamp: -1 },
    { sessionId: 1 },
    { 'compliance.requiresReporting': 1, 'compliance.reportedToSAMRO': 1 },
    { 'compliance.requiresReporting': 1, 'compliance.reportedToSAMPRA': 1 },
    { timestamp: -1 }
  ]
});

// Add methods for compliance reporting
activityLogSchema.methods.markAsReported = function(organization) {
  if (organization === 'SAMRO') {
    this.compliance.reportedToSAMRO = true;
  } else if (organization === 'SAMPRA') {
    this.compliance.reportedToSAMPRA = true;
  } else if (organization === 'RISA') {
    this.compliance.reportedToRISA = true;
  }
  this.compliance.reportingDate = new Date();
  return this.save();
};

// Static method to get switching statistics
activityLogSchema.statics.getSwitchingStats = function(storeId, dateRange) {
  return this.aggregate([
    {
      $match: {
        storeId: mongoose.Types.ObjectId(storeId),
        activityType: 'source_switch',
        timestamp: {
          $gte: new Date(dateRange.start),
          $lte: new Date(dateRange.end)
        }
      }
    },
    {
      $group: {
        _id: {
          from: '$sourceFrom',
          to: '$sourceTo'
        },
        count: { $sum: 1 },
        totalDuration: { $sum: '$duration' },
        avgDuration: { $avg: '$duration' }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);
};

module.exports = mongoose.model('ActivityLog', activityLogSchema);
