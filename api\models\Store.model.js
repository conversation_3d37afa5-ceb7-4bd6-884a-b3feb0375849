const mongoose = require('mongoose');

const storeSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true, lowercase: true, trim: true },
  address: String,
  city: String,
  province: String,
  country: { type: String, default: 'South Africa' },
  postalCode: String,
  phone: String,
  timezone: { type: String, default: 'Africa/Johannesburg' },
  // Store verification status
  verificationStatus: {
    type: String,
    enum: ['pending', 'verified', 'rejected'],
    default: 'pending'
  },
  verifiedAt: Date,
  verifiedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  // Business details
  businessType: {
    type: String,
    enum: ['retail', 'restaurant', 'hotel', 'gym', 'office', 'public_venue', 'other'],
    required: true
  },
  businessRegistrationNumber: String,
  vatNumber: String,
  // License information
  licenses: {
    samro: {
      licenseNumber: String,
      tariffCode: String,
      expiryDate: Date,
      isActive: { type: Boolean, default: false }
    },
    sampra: {
      licenseNumber: String,
      tariffCode: String,
      expiryDate: Date,
      isActive: { type: Boolean, default: false }
    }
  },
  // Operating hours
  openHours: {
    monday: { open: String, close: String, closed: { type: Boolean, default: false } },
    tuesday: { open: String, close: String, closed: { type: Boolean, default: false } },
    wednesday: { open: String, close: String, closed: { type: Boolean, default: false } },
    thursday: { open: String, close: String, closed: { type: Boolean, default: false } },
    friday: { open: String, close: String, closed: { type: Boolean, default: false } },
    saturday: { open: String, close: String, closed: { type: Boolean, default: false } },
    sunday: { open: String, close: String, closed: { type: Boolean, default: false } }
  },
  devices: [String],
  users: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true
});

module.exports = mongoose.model('Store', storeSchema);