// controllers/playlist.controller.js
const Playlist = require('../models/Playlist.model');
const Track = require('../models/Track.model');

// Get all playlists (for admin use)
exports.getAllPlaylists = async (req, res) => {
  try {
    const playlists = await Playlist.find()
      .populate('tracks', 'title artist duration')
      .populate('storeId', 'name')
      .populate('createdBy', 'username');
    res.json(playlists);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to fetch playlists' });
  }
};

// Create new playlist
exports.createPlaylist = async (req, res) => {
  try {
    const { name, storeId, schedule, trackIds } = req.body;

    // If schedule is provided, check for conflicts
    if (schedule && Array.isArray(schedule)) {
      for (const newSchedule of schedule) {
        const conflicts = await checkScheduleConflicts(storeId, newSchedule);
        if (conflicts.length > 0) {
          const conflictMessages = conflicts.map(c =>
            `"${c.playlistName}" on ${c.day} at ${c.startTime}-${c.endTime}`
          ).join(', ');
          return res.status(400).json({
            error: `Schedule conflicts with: ${conflictMessages}`,
            conflicts
          });
        }
      }
    }

    const playlist = await Playlist.create({
      name,
      storeId,
      schedule,
      tracks: trackIds || [],
      createdBy: req.user.id
    });

    res.status(201).json(playlist);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to create playlist' });
  }
};

// Get all playlists for a store
exports.getPlaylistsByStore = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Validate storeId
    if (!storeId) {
      return res.status(400).json({
        success: false,
        error: 'Store ID is required',
        timestamp: new Date().toISOString()
      });
    }

    const playlists = await Playlist.find({ storeId })
      .populate('tracks', 'title artist album genre duration filePath isValid validationErrors')
      .populate('storeId', 'name')
      .populate('createdBy', 'username');

    // Validate playlist data and calculate metadata
    const validatedPlaylists = playlists.map(playlist => {
      const playlistObj = playlist.toObject();
      const validTracks = playlistObj.tracks?.filter(track => track.isValid !== false) || [];
      const invalidTracks = playlistObj.tracks?.filter(track => track.isValid === false) || [];

      return {
        ...playlistObj,
        trackCount: playlistObj.tracks?.length || 0,
        validTrackCount: validTracks.length,
        invalidTrackCount: invalidTracks.length,
        totalDuration: validTracks.reduce((sum, track) => sum + (track.duration || 0), 0),
        isValid: invalidTracks.length === 0 && validTracks.length > 0,
        lastUpdated: playlistObj.updatedAt || playlistObj.createdAt
      };
    });

    res.json({
      success: true,
      data: validatedPlaylists,
      metadata: {
        total: validatedPlaylists.length,
        storeId,
        timestamp: new Date().toISOString()
      }
    });
  } catch (err) {
    console.error('Error fetching playlists for store:', err);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch playlists',
      details: err.message,
      timestamp: new Date().toISOString()
    });
  }
};

// Get single playlist
exports.getPlaylistById = async (req, res) => {
  try {
    const { id } = req.params;
    const playlist = await Playlist.findById(id).populate('tracks', 'title artist album genre duration filePath');
    if (!playlist) return res.status(404).json({ error: 'Playlist not found' });
    res.json(playlist);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to fetch playlist' });
  }
};

// Helper function to check schedule conflicts
const checkScheduleConflicts = async (storeId, newSchedule, excludePlaylistId = null) => {
  const playlists = await Playlist.find({
    storeId,
    _id: { $ne: excludePlaylistId } // Exclude current playlist if editing
  });

  const conflicts = [];

  for (const playlist of playlists) {
    if (playlist.schedule) {
      for (const existingSchedule of playlist.schedule) {
        // Check if same day
        if (existingSchedule.day === newSchedule.day) {
          // Check for time overlap
          const newStart = newSchedule.startTime;
          const newEnd = newSchedule.endTime;
          const existingStart = existingSchedule.startTime;
          const existingEnd = existingSchedule.endTime;

          // Check if times overlap
          if (
            (newStart >= existingStart && newStart < existingEnd) ||
            (newEnd > existingStart && newEnd <= existingEnd) ||
            (newStart <= existingStart && newEnd >= existingEnd)
          ) {
            conflicts.push({
              playlistId: playlist._id,
              playlistName: playlist.name,
              day: existingSchedule.day,
              startTime: existingStart,
              endTime: existingEnd
            });
          }
        }
      }
    }
  }

  return conflicts;
};

// Update playlist (name, schedule, or tracks)
exports.updatePlaylist = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, schedule, trackIds } = req.body;

    // Get the current playlist to check store ID
    const currentPlaylist = await Playlist.findById(id);
    if (!currentPlaylist) {
      return res.status(404).json({ error: 'Playlist not found' });
    }

    // If schedule is being updated, check for conflicts
    if (schedule && Array.isArray(schedule)) {
      for (const newSchedule of schedule) {
        const conflicts = await checkScheduleConflicts(currentPlaylist.storeId, newSchedule, id);
        if (conflicts.length > 0) {
          const conflictMessages = conflicts.map(c =>
            `"${c.playlistName}" on ${c.day} at ${c.startTime}-${c.endTime}`
          ).join(', ');
          return res.status(400).json({
            error: `Schedule conflicts with: ${conflictMessages}`,
            conflicts
          });
        }
      }
    }

    const updated = await Playlist.findByIdAndUpdate(
      id,
      { name, schedule, tracks: trackIds },
      { new: true }
    ).populate('tracks', 'title artist album genre duration filePath');

    res.json(updated);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to update playlist' });
  }
};

// Delete playlist
exports.deletePlaylist = async (req, res) => {
  try {
    const { id } = req.params;
    await Playlist.findByIdAndDelete(id);
    res.json({ message: 'Playlist deleted successfully' });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to delete playlist' });
  }
};

// Reorder tracks in playlist (for drag-and-drop)
exports.reorderTracks = async (req, res) => {
  try {
    const { id } = req.params;
    const { orderedTrackIds } = req.body;

    const playlist = await Playlist.findById(id);
    if (!playlist) return res.status(404).json({ error: 'Playlist not found' });

    playlist.tracks = orderedTrackIds;
    await playlist.save();

    res.json(playlist);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to reorder tracks' });
  }
};

// Get active playlist based on current time/day
exports.getActivePlaylist = async (req, res) => {
  try {
    const { storeId } = req.params;
    const now = new Date();
    const day = now.toLocaleString('en-US', { weekday: 'long' });
    const time = now.toTimeString().slice(0, 5); // HH:mm

    const playlist = await Playlist.findOne({
      storeId,
      'schedule.day': day,
      'schedule.startTime': { $lte: time },
      'schedule.endTime': { $gte: time }
    });

    if (!playlist) return res.status(404).json({ error: 'No active playlist found' });

    const populated = await playlist.populate('tracks', 'title artist album genre duration filePath');
    res.json(populated);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Failed to get active playlist' });
  }
};