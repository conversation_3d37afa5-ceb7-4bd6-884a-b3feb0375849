const mongoose = require('mongoose');

const trackSchema = new mongoose.Schema({
  trackId: { type: String, unique: true },
  title: { type: String, required: true },
  artist: { type: String, required: true },
  album: String,
  genre: String,
  duration: { type: Number, required: true }, // Duration in seconds
  filePath: String,

  // Essential compliance metadata
 isrcCode: {
  type: String,
  unique: true,
  sparse: true,
  validate: {
    validator: function(v) {
      return !v || /^[A-Z]{2}-[A-Z0-9]{3}-\d{2}-\d{5}$/.test(v);
    },
    message: 'ISRC code must follow format: CC-XXX-YY-NNNNN'
  }
},

  iswcCode: {
    type: String,
    sparse: true,
    validate: {
      validator: function(v) {
        return !v || /^T-[0-9]{9}-[0-9]$/.test(v);
      },
      message: 'ISWC code must follow format: T-NNNNNNNNN-C'
    }
  },

  // Composer and author information
  composers: [{
    name: { type: String, required: true },
    role: { type: String, enum: ['composer', 'lyricist', 'arranger'], default: 'composer' },
    share: { type: Number, min: 0, max: 100 }, // Percentage share
    ipiNumber: String // IPI (Interested Party Information) number for SAMRO
  }],

  // Publisher information
  publishers: [{
    name: { type: String, required: true },
    publisherCode: String,
    copyrightOwner: String,
    share: { type: Number, min: 0, max: 100 }, // Percentage share
    territory: { type: String, default: 'ZA' }, // Territory code
    ipiNumber: String // Publisher IPI number for SAMRO
  }],

  // Record label information
  recordLabel: {
    name: String,
    catalogNumber: String,
    releaseDate: Date,
    territory: { type: String, default: 'ZA' }
  },

  // Additional metadata for compliance
  metadata: {
    bpm: Number,
    key: String,
    language: { type: String, default: 'en' },
    explicit: { type: Boolean, default: false },
    instrumental: { type: Boolean, default: false }
  },

  // Rights and licensing information
  rights: {
    mechanicalRights: { type: Boolean, default: true },
    performanceRights: { type: Boolean, default: true },
    synchronizationRights: { type: Boolean, default: false },
    licenseType: { type: String, enum: ['standard', 'premium', 'custom'], default: 'standard' }
  },

  // SAMRO-specific fields
  samroWorkNumber: String,
  composerIPINumbers: [String],
  performanceRightsSplits: [{
    rightsHolderName: String,
    ipiNumber: String,
    percentage: { type: Number, min: 0, max: 100 },
    role: { type: String, enum: ['composer', 'lyricist', 'arranger', 'publisher'] }
  }],

  // SAMPRA-specific fields
  sampraArtistNumbers: [String],
  recordingRightsSplits: [{
    artistName: String,
    sampraNumber: String,
    percentage: { type: Number, min: 0, max: 100 },
    role: { type: String, enum: ['performer', 'producer', 'label'] }
  }],
  producerInfo: [{
    name: String,
    sampraNumber: String,
    role: { type: String, enum: ['producer', 'engineer', 'mixer'], default: 'producer' },
    percentage: { type: Number, min: 0, max: 100 }
  }],
  masterRecordingInfo: {
    recordingDate: Date,
    studioName: String,
    engineerName: String,
    producerName: String
  },

  // Enhanced compliance tracking
  complianceChecks: {
    samroDataComplete: { type: Boolean, default: false },
    sampraDataComplete: { type: Boolean, default: false },
    lastValidated: Date,
    validationErrors: [String],
    complianceScore: { type: Number, min: 0, max: 100, default: 0 }
  },

  // Compliance status
  compliance: {
    samroRegistered: { type: Boolean, default: false },
    sampraRegistered: { type: Boolean, default: false },
    risaCompliant: { type: Boolean, default: true },
    verificationStatus: {
      type: String,
      enum: ['pending', 'verified', 'rejected', 'requires_review'],
      default: 'pending'
    },
    lastVerified: Date,
    verifiedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
  },

  addedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Indexes for efficient querying
trackSchema.index({ isrcCode: 1 });
trackSchema.index({ iswcCode: 1 });
trackSchema.index({ 'publishers.name': 1 });
trackSchema.index({ 'composers.name': 1 });
trackSchema.index({ 'compliance.verificationStatus': 1 });
trackSchema.index({ artist: 1, title: 1 });

// Update timestamp on save
trackSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Update timestamp on findOneAndUpdate
trackSchema.pre('findOneAndUpdate', function(next) {
  this.set({ updatedAt: Date.now() });
  next();
});

module.exports = mongoose.model('Track', trackSchema);