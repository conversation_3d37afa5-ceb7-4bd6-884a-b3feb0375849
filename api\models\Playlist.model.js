const mongoose = require('mongoose');

const scheduleSchema = new mongoose.Schema({
  day: String,
  startTime: String,
  endTime: String
});

const playlistSchema = new mongoose.Schema({
  name: { type: String, required: true },
  storeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Store' },
  schedule: [scheduleSchema],
  tracks: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Track' }],
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
});

module.exports = mongoose.model('Playlist', playlistSchema);