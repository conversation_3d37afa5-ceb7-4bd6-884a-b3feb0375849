const mongoose = require('mongoose');

const emailVerificationSchema = new mongoose.Schema({
  email: { 
    type: String, 
    required: true, 
    lowercase: true, 
    trim: true 
  },
  verificationCode: { 
    type: String, 
    required: true 
  },
  type: {
    type: String,
    enum: ['store_registration', 'email_change', 'password_reset'],
    required: true
  },
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User' 
  },
  storeId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Store' 
  },
  // Additional data for store registration
  registrationData: {
    storeName: String,
    storeAddress: String,
    storeCity: String,
    storeProvince: String,
    storePostalCode: String,
    storePhone: String,
    businessType: String,
    businessRegistrationNumber: String,
    vatNumber: String,
    // License information
    samroLicenseNumber: String,
    samroTariffCode: String,
    samroExpiryDate: Date,
    sampraLicenseNumber: String,
    sampraTariffCode: String,
    sampraExpiryDate: Date,
    risaRegistrationNumber: String,
    ownerName: String,
    ownerUsername: String,
    ownerPassword: String // This will be hashed before saving
  },
  expiresAt: { 
    type: Date, 
    default: () => new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    index: { expireAfterSeconds: 0 }
  },
  isUsed: { 
    type: Boolean, 
    default: false 
  },
  usedAt: Date,
  attempts: { 
    type: Number, 
    default: 0 
  },
  maxAttempts: { 
    type: Number, 
    default: 5 
  },
  ipAddress: String,
  userAgent: String
}, {
  timestamps: true
});

// Index for efficient queries
emailVerificationSchema.index({ email: 1, type: 1 });
emailVerificationSchema.index({ verificationCode: 1 });
emailVerificationSchema.index({ expiresAt: 1 });

// Static method to generate verification code
emailVerificationSchema.statics.generateCode = function() {
  return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit code
};

// Instance method to check if verification is valid
emailVerificationSchema.methods.isValid = function() {
  return !this.isUsed && 
         this.expiresAt > new Date() && 
         this.attempts < this.maxAttempts;
};

// Instance method to increment attempts
emailVerificationSchema.methods.incrementAttempts = function() {
  this.attempts += 1;
  return this.save();
};

// Instance method to mark as used
emailVerificationSchema.methods.markAsUsed = function() {
  this.isUsed = true;
  this.usedAt = new Date();
  return this.save();
};

module.exports = mongoose.model('EmailVerification', emailVerificationSchema);
