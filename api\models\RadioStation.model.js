const mongoose = require('mongoose');

const radioStationSchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: true,
    trim: true
  },
  url: { 
    type: String, 
    required: true,
    trim: true
  },
  genre: { 
    type: String, 
    required: true,
    trim: true
  },
  country: { 
    type: String, 
    required: true,
    trim: true
  },
  description: { 
    type: String,
    trim: true
  },
  isActive: { 
    type: Boolean, 
    default: true 
  },
  createdBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User',
    required: true
  },
  // Stores that have enabled this station
  enabledStores: [{ 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Store' 
  }]
}, {
  timestamps: true
});

// Index for better query performance
radioStationSchema.index({ isActive: 1 });
radioStationSchema.index({ genre: 1 });
radioStationSchema.index({ createdBy: 1 });

module.exports = mongoose.model('RadioStation', radioStationSchema);
