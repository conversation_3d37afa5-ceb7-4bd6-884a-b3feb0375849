const cron = require('node-cron');
const Playlist = require('../models/Playlist.model');
const PlayHistory = require('../models/PlayHistory.model');
const Store = require('../models/Store.model');
const { format } = require('date-fns-tz');

// Run every minute
cron.schedule('* * * * *', async () => {
  try {
    // Get all stores with their timezones
    const stores = await Store.find({});

    for (const store of stores) {
      const timezone = store.timezone || 'UTC';
      const now = new Date();
      const day = format(now, 'EEEE', { timeZone: timezone }); // Full day name
      const time = format(now, 'HH:mm', { timeZone: timezone });

      const playlists = await Playlist.find({
        storeId: store._id,
        'schedule.day': day,
        'schedule.startTime': { $lte: time },
        'schedule.endTime': { $gte: time }
      }).populate('tracks');

      // If multiple playlists are scheduled at the same time, prioritize by creation date (oldest first)
      // This prevents conflicts and ensures only one playlist plays at a time
      const sortedPlaylists = playlists.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));

      // Only play the first (highest priority) playlist
      if (sortedPlaylists.length > 0) {
        const playlist = sortedPlaylists[0];

        // Log warning if multiple playlists are scheduled at the same time
        if (sortedPlaylists.length > 1) {
          console.warn(`[${timezone}] Multiple playlists scheduled at ${time} on ${day}. Playing "${playlist.name}" (priority). Conflicting playlists: ${sortedPlaylists.slice(1).map(p => p.name).join(', ')}`);
        }

        console.log(`[${timezone}] Playing playlist: ${playlist.name} at ${time} on ${day}`);

        // Check if playlist is already active to avoid duplicate plays
        const recentPlay = await PlayHistory.findOne({
          storeId: store._id,
          playlistId: playlist._id,
          startTime: { $gte: new Date(Date.now() - 60000) } // Within last minute
        });

        if (!recentPlay) {
          // Log playlist start
          await PlayHistory.create({
            storeId: store._id,
            deviceId: `scheduler-${store._id}`,
            playlistId: playlist._id,
            trackId: playlist.tracks[0]?._id,
            startTime: new Date(),
            endTime: new Date(),
            durationPlayed: 0,
            playedDate: format(new Date(), 'yyyy-MM-dd', { timeZone: timezone }),
            metadata: {
              timezone,
              scheduledTime: time,
              scheduledDay: day,
              playlistName: playlist.name,
              conflictingPlaylists: sortedPlaylists.length > 1 ? sortedPlaylists.slice(1).map(p => p.name) : []
            }
          });
        }
      }
    }
  } catch (error) {
    console.error('Scheduler error:', error);
  }
});