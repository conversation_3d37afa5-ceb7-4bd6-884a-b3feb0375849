const SAMPRARates = require('../models/SAMPRARates.model');
const Track = require('../models/Track.model');
const ComplianceReport = require('../models/ComplianceReport.model');
const ComplianceAlert = require('../models/ComplianceAlert.model');
const { subDays, subMonths, format } = require('date-fns');

// Get SAMPRA dashboard overview
exports.getSAMPRADashboard = async (req, res) => {
  try {
    const { organization } = req.user;
    const today = new Date();
    const lastMonth = subMonths(today, 1);
    const lastWeek = subDays(today, 7);

    // Get SAMPRA-specific statistics
    const [
      totalTracks,
      sampraRegisteredTracks,
      pendingVerification,
      recentReports,
      activeAlerts
    ] = await Promise.all([
      Track.countDocuments(),
      Track.countDocuments({ 'compliance.sampraRegistered': true }),
      Track.countDocuments({
        'complianceChecks.sampraDataComplete': false,
        'compliance.verificationStatus': 'pending'
      }),
      ComplianceReport.find({
        organization: { $in: ['SAMPRA', 'All'] },
        createdAt: { $gte: lastMonth }
      }).sort({ createdAt: -1 }).limit(5),
      ComplianceAlert.find({
        category: 'SAMPRA',
        isActive: true,
        status: { $in: ['active', 'acknowledged'] }
      }).limit(10)
    ]);

    // Calculate compliance metrics
    const complianceRate = totalTracks > 0 ? (sampraRegisteredTracks / totalTracks) * 100 : 0;
    
    // Get recent activity
    const recentActivity = await Track.find({
      'compliance.sampraRegistered': true,
      updatedAt: { $gte: lastWeek }
    })
    .sort({ updatedAt: -1 })
    .limit(10)
    .select('title artist updatedAt compliance');

    res.json({
      overview: {
        totalTracks,
        sampraRegisteredTracks,
        complianceRate: Math.round(complianceRate * 100) / 100,
        pendingVerification,
        activeAlerts: activeAlerts.length
      },
      recentReports,
      activeAlerts,
      recentActivity
    });
  } catch (error) {
    console.error('Error fetching SAMPRA dashboard:', error);
    res.status(500).json({ error: 'Failed to fetch SAMPRA dashboard data' });
  }
};

// Get current SAMPRA rates
exports.getSAMPRARates = async (req, res) => {
  try {
    const { category, subcategory, territory = 'ZA' } = req.query;
    
    let query = {
      isActive: true,
      effectiveDate: { $lte: new Date() },
      $or: [
        { expiryDate: { $exists: false } },
        { expiryDate: null },
        { expiryDate: { $gte: new Date() } }
      ]
    };

    if (category) query.category = category;
    if (subcategory) query.subcategory = subcategory;
    if (territory) query.territory = territory;

    const rates = await SAMPRARates.find(query)
      .sort({ category: 1, subcategory: 1, effectiveDate: -1 })
      .populate('createdBy', 'username')
      .populate('approvedBy', 'username');

    res.json({
      rates,
      total: rates.length
    });
  } catch (error) {
    console.error('Error fetching SAMPRA rates:', error);
    res.status(500).json({ error: 'Failed to fetch SAMPRA rates' });
  }
};

// Create new SAMPRA rate
exports.createSAMPRARate = async (req, res) => {
  try {
    const rateData = {
      ...req.body,
      createdBy: req.user.id,
      rateId: `SAMPRA-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`.toUpperCase()
    };

    const rate = new SAMPRARates(rateData);
    await rate.save();

    res.status(201).json({
      message: 'SAMPRA rate created successfully',
      rate
    });
  } catch (error) {
    console.error('Error creating SAMPRA rate:', error);
    res.status(500).json({ error: 'Failed to create SAMPRA rate' });
  }
};

// Update SAMPRA rate
exports.updateSAMPRARate = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      updatedBy: req.user.id
    };

    const rate = await SAMPRARates.findByIdAndUpdate(id, updateData, { new: true });
    if (!rate) {
      return res.status(404).json({ error: 'SAMPRA rate not found' });
    }

    res.json({
      message: 'SAMPRA rate updated successfully',
      rate
    });
  } catch (error) {
    console.error('Error updating SAMPRA rate:', error);
    res.status(500).json({ error: 'Failed to update SAMPRA rate' });
  }
};

// Verify SAMPRA artist registration
exports.verifyArtistRegistration = async (req, res) => {
  try {
    const { artistName, sampraNumber, recordingTitle } = req.body;

    // Simulate SAMPRA database lookup
    // In a real implementation, this would call SAMPRA's API
    const mockVerification = {
      artistName,
      sampraNumber,
      isValid: true,
      registrationStatus: 'active',
      memberSince: new Date('2020-01-01'),
      recordings: [
        {
          title: recordingTitle || 'Sample Recording',
          isrc: 'ZA-ABC-20-12345',
          role: 'performer',
          percentage: 50
        }
      ],
      contactInfo: {
        email: '<EMAIL>',
        phone: '+27123456789'
      }
    };

    // Create compliance alert if verification fails
    if (!mockVerification.isValid) {
      const alert = new ComplianceAlert({
        type: 'verification_required',
        category: 'SAMPRA',
        severity: 'medium',
        title: 'SAMPRA Artist Verification Failed',
        message: `Artist ${artistName} with SAMPRA number ${sampraNumber} could not be verified`,
        contextData: {
          metadata: { artistName, sampraNumber, recordingTitle }
        },
        createdBy: req.user.id
      });
      await alert.save();
    }

    res.json({
      verification: mockVerification,
      message: mockVerification.isValid ? 'Artist verified successfully' : 'Artist verification failed'
    });
  } catch (error) {
    console.error('Error verifying SAMPRA artist:', error);
    res.status(500).json({ error: 'Failed to verify SAMPRA artist registration' });
  }
};

// Generate SAMPRA-specific report
exports.generateSAMPRAReport = async (req, res) => {
  try {
    const { type, startDate, endDate, storeIds } = req.body;
    
    // Build query for SAMPRA-specific data
    const query = {
      'compliance.sampraRegistered': true,
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };

    if (storeIds && storeIds.length > 0) {
      // This would need to be adjusted based on how tracks are linked to stores
    }

    const tracks = await Track.find(query)
      .populate('addedBy', 'username')
      .sort({ createdAt: -1 });

    // Calculate SAMPRA-specific metrics
    const sampraData = {
      totalTracks: tracks.length,
      totalArtists: [...new Set(tracks.map(t => t.artist))].length,
      totalRecordingRightsHolders: [...new Set(tracks.flatMap(t => 
        t.recordingRightsSplits?.map(r => r.artistName) || []
      ))].length,
      complianceRate: tracks.filter(t => t.complianceChecks?.sampraDataComplete).length / tracks.length * 100,
      
      // Recording rights breakdown
      recordingRightsHolders: tracks.reduce((acc, track) => {
        track.recordingRightsSplits?.forEach(split => {
          if (!acc[split.artistName]) {
            acc[split.artistName] = {
              name: split.artistName,
              sampraNumber: split.sampraNumber,
              trackCount: 0,
              totalPercentage: 0,
              roles: new Set()
            };
          }
          acc[split.artistName].trackCount++;
          acc[split.artistName].totalPercentage += split.percentage;
          acc[split.artistName].roles.add(split.role);
        });
        return acc;
      }, {}),
      
      // Track details for export
      trackDetails: tracks.map(track => ({
        title: track.title,
        artist: track.artist,
        isrcCode: track.isrcCode,
        sampraArtistNumbers: track.sampraArtistNumbers,
        recordingRightsSplits: track.recordingRightsSplits,
        complianceScore: track.complianceChecks?.complianceScore || 0
      }))
    };

    // Convert roles Set to Array for JSON serialization
    Object.values(sampraData.recordingRightsHolders).forEach(holder => {
      holder.roles = Array.from(holder.roles);
    });

    res.json({
      reportType: type,
      period: { startDate, endDate },
      generatedAt: new Date(),
      data: sampraData,
      summary: {
        totalTracks: sampraData.totalTracks,
        complianceRate: Math.round(sampraData.complianceRate * 100) / 100,
        recordingRightsHoldersCount: Object.keys(sampraData.recordingRightsHolders).length
      }
    });
  } catch (error) {
    console.error('Error generating SAMPRA report:', error);
    res.status(500).json({ error: 'Failed to generate SAMPRA report' });
  }
};

// Export SAMPRA data in CSV format
exports.exportSAMPRAData = async (req, res) => {
  try {
    const { reportId, format = 'csv' } = req.body;
    
    // Get report data
    const report = await ComplianceReport.findById(reportId);
    if (!report) {
      return res.status(404).json({ error: 'Report not found' });
    }

    // Generate CSV export
    const csvHeaders = [
      'Title',
      'Artist',
      'ISRC',
      'Play Count',
      'Duration',
      'Recording Rights Holders',
      'SAMPRA Numbers'
    ];

    const csvRows = report.trackData.map(track => [
      track.title,
      track.artist,
      track.isrcCode || '',
      track.playCount,
      track.duration,
      '', // Would be populated from track.recordingRightsSplits
      ''  // Would be populated from track.sampraArtistNumbers
    ]);

    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.map(field => `"${field}"`).join(','))
    ].join('\n');

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="sampra-report-${report.reportId}.csv"`);
    res.send(csvContent);
  } catch (error) {
    console.error('Error exporting SAMPRA data:', error);
    res.status(500).json({ error: 'Failed to export SAMPRA data' });
  }
};

// Calculate SAMPRA needletime royalties
exports.calculateNeedletimeRoyalties = async (req, res) => {
  try {
    const { storeId, startDate, endDate, category = 'retail' } = req.body;
    
    // Get applicable SAMPRA rates
    const rates = await SAMPRARates.getCurrentRates(category);
    if (!rates.length) {
      return res.status(404).json({ error: 'No applicable SAMPRA rates found' });
    }
    
    const rate = rates[0]; // Use the most recent rate
    
    // Mock calculation - in real implementation, this would use actual play data
    const mockUsageData = {
      plays: 800,
      duration: 240, // average track duration in seconds
      revenue: 40000, // ZAR
      audienceSize: 500,
      timeOfDay: '14:30',
      dayOfWeek: 'tuesday'
    };
    
    const royaltyCalculation = rate.calculateNeedletimeRoyalty(mockUsageData);
    
    res.json({
      period: { startDate, endDate },
      storeId,
      rateUsed: {
        rateId: rate.rateId,
        category: rate.category,
        artistRate: rate.rates.artistRate,
        producerRate: rate.rates.producerRate,
        labelRate: rate.rates.labelRate
      },
      usageData: mockUsageData,
      calculation: royaltyCalculation
    });
  } catch (error) {
    console.error('Error calculating SAMPRA royalties:', error);
    res.status(500).json({ error: 'Failed to calculate SAMPRA needletime royalties' });
  }
};

// Get SAMPRA compliance alerts
exports.getSAMPRAAlerts = async (req, res) => {
  try {
    const { status, severity, limit = 20 } = req.query;
    
    const query = {
      category: 'SAMPRA',
      isActive: true
    };
    
    if (status) query.status = status;
    if (severity) query.severity = severity;
    
    const alerts = await ComplianceAlert.find(query)
      .sort({ triggeredAt: -1 })
      .limit(parseInt(limit))
      .populate('assignedTo', 'username email')
      .populate('affectedEntities.tracks', 'title artist')
      .populate('affectedEntities.stores', 'name location');
    
    res.json({
      alerts,
      total: alerts.length
    });
  } catch (error) {
    console.error('Error fetching SAMPRA alerts:', error);
    res.status(500).json({ error: 'Failed to fetch SAMPRA alerts' });
  }
};
