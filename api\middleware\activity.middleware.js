const User = require('../models/User.model');

// Middleware to track user activity for monitoring
const trackActivity = async (req, res, next) => {
  try {
    // Only track activity for authenticated users
    if (req.user && req.user.id) {
      // Update user's last activity timestamp
      await User.findByIdAndUpdate(
        req.user.id,
        { 
          lastActivity: new Date(),
          // Also track IP address for security monitoring
          lastIpAddress: req.ip || req.connection.remoteAddress
        },
        { new: true }
      );
    }
  } catch (error) {
    // Don't fail the request if activity tracking fails
    console.error('Failed to track user activity:', error);
  }
  
  next();
};

// Middleware to log API requests for monitoring
const logApiRequest = (req, res, next) => {
  const startTime = Date.now();
  
  // Log the request
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.originalUrl} - ${req.ip}`);
  
  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function(...args) {
    const responseTime = Date.now() - startTime;
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.originalUrl} - ${res.statusCode} - ${responseTime}ms`);
    
    // Store metrics for monitoring (in a real app, you'd use a proper metrics store)
    if (!global.apiMetrics) {
      global.apiMetrics = {
        requests: [],
        errors: []
      };
    }
    
    const requestData = {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      responseTime: responseTime,
      timestamp: new Date(),
      userAgent: req.get('User-Agent'),
      ip: req.ip
    };
    
    // Keep only last 1000 requests to prevent memory issues
    global.apiMetrics.requests.push(requestData);
    if (global.apiMetrics.requests.length > 1000) {
      global.apiMetrics.requests.shift();
    }
    
    // Track errors separately
    if (res.statusCode >= 400) {
      global.apiMetrics.errors.push(requestData);
      if (global.apiMetrics.errors.length > 100) {
        global.apiMetrics.errors.shift();
      }
    }
    
    originalEnd.apply(this, args);
  };
  
  next();
};

// Function to get API metrics for monitoring
const getApiMetrics = () => {
  if (!global.apiMetrics) {
    return {
      totalRequests: 0,
      totalErrors: 0,
      averageResponseTime: 0,
      errorRate: 0,
      recentRequests: [],
      recentErrors: []
    };
  }
  
  const { requests, errors } = global.apiMetrics;
  const totalRequests = requests.length;
  const totalErrors = errors.length;
  
  // Calculate average response time
  const averageResponseTime = totalRequests > 0 
    ? Math.round(requests.reduce((sum, req) => sum + req.responseTime, 0) / totalRequests)
    : 0;
  
  // Calculate error rate
  const errorRate = totalRequests > 0 
    ? Math.round((totalErrors / totalRequests) * 100 * 10) / 10
    : 0;
  
  return {
    totalRequests,
    totalErrors,
    averageResponseTime,
    errorRate,
    recentRequests: requests.slice(-10), // Last 10 requests
    recentErrors: errors.slice(-10) // Last 10 errors
  };
};

module.exports = {
  trackActivity,
  logApiRequest,
  getApiMetrics
};
