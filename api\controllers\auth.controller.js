const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const User = require('../models/User.model');
const { auditActions } = require('../middleware/audit.middleware');

exports.login = async (req, res) => {
  const { username, password } = req.body;
  const user = await User.findOne({ username }).populate('storeId');

  if (!user || !(await bcrypt.compare(password, user.password))) {
    return res.status(401).json({ error: 'Invalid credentials' });
  }

  // Check if email is verified for store users
  if (user.role === 'store' && !user.emailVerified) {
    return res.status(401).json({
      error: 'Email not verified. Please verify your email before logging in.',
      requiresVerification: true,
      email: user.email
    });
  }

  // Check account status
  if (user.accountStatus === 'pending_verification') {
    return res.status(401).json({
      error: 'Account pending verification. Please check your email for verification instructions.',
      requiresVerification: true,
      email: user.email
    });
  }

  if (user.accountStatus === 'suspended') {
    return res.status(401).json({
      error: 'Account suspended. Please contact support for assistance.'
    });
  }

  if (user.accountStatus === 'inactive' || !user.isActive) {
    return res.status(401).json({
      error: 'Account inactive. Please contact support for assistance.'
    });
  }

  const token = jwt.sign(
    {
      id: user._id,
      role: user.role,
      storeId: user.storeId?._id,
      organization: user.organization,
      permissions: user.permissions
    },
    process.env.JWT_SECRET,
    { expiresIn: '1d' }
  );

  // Update last login and IP
  user.lastLogin = new Date();
  user.lastIpAddress = req.ip || req.connection.remoteAddress;
  await user.save();

  // Log the login action for audit trail
  try {
    await auditActions.userLogin(
      user._id,
      req.ip || req.connection.remoteAddress || 'unknown',
      req.get('User-Agent') || 'unknown',
      user.organization || 'ADMIN'
    );
  } catch (auditError) {
    console.error('Failed to log login audit:', auditError);
    // Don't fail the login if audit logging fails
  }

  res.json({
    token,
    user: {
      id: user._id,
      username: user.username,
      email: user.email,
      role: user.role,
      store: user.storeId,
      organization: user.organization,
      permissions: user.permissions,
      emailVerified: user.emailVerified,
      accountStatus: user.accountStatus
    }
  });
};

// Logout endpoint (for audit logging)
exports.logout = async (req, res) => {
  try {
    // Log the logout action for audit trail
    if (req.user) {
      await auditActions.userLogout(
        req.user.id,
        req.ip || req.connection.remoteAddress || 'unknown',
        req.get('User-Agent') || 'unknown',
        req.user.organization || 'ADMIN'
      );
    }

    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error('Error during logout:', error);
    res.status(500).json({ error: 'Logout failed' });
  }
};