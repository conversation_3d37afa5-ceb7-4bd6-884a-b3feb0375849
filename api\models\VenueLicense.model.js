const mongoose = require('mongoose');

const venueLicenseSchema = new mongoose.Schema({
  licenseId: { type: String, unique: true, required: true },
  
  // Venue information
  venue: {
    storeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Store', required: true },
    name: { type: String, required: true },
    address: {
      street: String,
      city: String,
      province: String,
      postalCode: String,
      country: { type: String, default: 'South Africa' }
    },
    coordinates: {
      latitude: Number,
      longitude: Number
    },
    venueType: {
      type: String,
      enum: ['retail', 'restaurant', 'hotel', 'gym', 'office', 'radio_station', 'streaming_platform', 'public_venue', 'other'],
      required: true
    },
    capacity: Number, // Maximum occupancy
    operatingHours: {
      monday: { open: String, close: String },
      tuesday: { open: String, close: String },
      wednesday: { open: String, close: String },
      thursday: { open: String, close: String },
      friday: { open: String, close: String },
      saturday: { open: String, close: String },
      sunday: { open: String, close: String }
    }
  },
  
  // License details
  licenseType: {
    type: String,
    enum: ['samro_only', 'sampra_only', 'combined', 'risa', 'international'],
    required: true
  },
  
  // SAMRO license information
  samroLicense: {
    licenseNumber: String,
    tariffCode: String,
    category: String, // e.g., 'A1', 'B2', 'C3'
    isActive: { type: Boolean, default: false },
    issueDate: Date,
    expiryDate: Date,
    annualFee: Number,
    paymentStatus: {
      type: String,
      enum: ['paid', 'pending', 'overdue', 'exempt'],
      default: 'pending'
    },
    lastPaymentDate: Date,
    nextPaymentDue: Date
  },
  
  // SAMPRA license information
  sampraLicense: {
    licenseNumber: String,
    tariffCode: String,
    category: String,
    isActive: { type: Boolean, default: false },
    issueDate: Date,
    expiryDate: Date,
    annualFee: Number,
    paymentStatus: {
      type: String,
      enum: ['paid', 'pending', 'overdue', 'exempt'],
      default: 'pending'
    },
    lastPaymentDate: Date,
    nextPaymentDue: Date
  },
  
  // RISA compliance
  risaCompliance: {
    isCompliant: { type: Boolean, default: true },
    registrationNumber: String,
    lastAuditDate: Date,
    nextAuditDue: Date,
    complianceScore: { type: Number, min: 0, max: 100 }
  },
  
  // Contact information
  contactPerson: {
    name: String,
    email: String,
    phone: String,
    position: String
  },
  
  // Business information
  businessDetails: {
    registrationNumber: String,
    vatNumber: String,
    taxNumber: String,
    businessType: {
      type: String,
      enum: ['sole_proprietor', 'partnership', 'company', 'trust', 'npo', 'government']
    },
    annualRevenue: Number,
    employeeCount: Number
  },
  
  // License status and alerts
  status: {
    type: String,
    enum: ['active', 'expired', 'suspended', 'pending_renewal', 'cancelled'],
    default: 'pending_renewal'
  },
  
  alerts: [{
    type: {
      type: String,
      enum: ['expiry_warning', 'payment_overdue', 'compliance_issue', 'renewal_required', 'audit_due']
    },
    message: String,
    severity: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium'
    },
    isActive: { type: Boolean, default: true },
    createdAt: { type: Date, default: Date.now },
    resolvedAt: Date,
    resolvedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
  }],
  
  // Usage tracking
  usageTracking: {
    monthlyPlayCount: Number,
    averageDailyPlays: Number,
    peakUsageHours: [String],
    lastUsageDate: Date,
    totalRoyaltiesPaid: Number,
    lastRoyaltyPayment: Date
  },
  
  // Documents and attachments
  documents: [{
    type: {
      type: String,
      enum: ['license_certificate', 'payment_receipt', 'compliance_report', 'audit_report', 'correspondence']
    },
    filename: String,
    filePath: String,
    uploadedAt: { type: Date, default: Date.now },
    uploadedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    description: String
  }],
  
  // Audit trail
  auditTrail: [{
    action: String,
    performedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    timestamp: { type: Date, default: Date.now },
    details: String,
    oldValues: mongoose.Schema.Types.Mixed,
    newValues: mongoose.Schema.Types.Mixed
  }],
  
  // Metadata
  notes: String,
  tags: [String],
  
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Indexes
venueLicenseSchema.index({ 'venue.storeId': 1 });
venueLicenseSchema.index({ licenseType: 1, status: 1 });
venueLicenseSchema.index({ 'samroLicense.expiryDate': 1 });
venueLicenseSchema.index({ 'sampraLicense.expiryDate': 1 });
venueLicenseSchema.index({ 'venue.venueType': 1 });

// Pre-save middleware
venueLicenseSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Update status based on license expiry dates
  const now = new Date();
  const samroExpired = this.samroLicense.expiryDate && this.samroLicense.expiryDate < now;
  const sampraExpired = this.sampraLicense.expiryDate && this.sampraLicense.expiryDate < now;
  
  if (samroExpired || sampraExpired) {
    this.status = 'expired';
  }
  
  next();
});

// Static method to find licenses expiring soon
venueLicenseSchema.statics.findExpiringLicenses = function(daysAhead = 30) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + daysAhead);
  
  return this.find({
    $or: [
      { 'samroLicense.expiryDate': { $lte: futureDate, $gte: new Date() } },
      { 'sampraLicense.expiryDate': { $lte: futureDate, $gte: new Date() } }
    ],
    status: { $in: ['active', 'pending_renewal'] }
  });
};

// Instance method to check compliance status
venueLicenseSchema.methods.checkComplianceStatus = function() {
  const now = new Date();
  const issues = [];
  
  // Check SAMRO license
  if (this.licenseType === 'samro_only' || this.licenseType === 'combined') {
    if (!this.samroLicense.isActive) {
      issues.push('SAMRO license is not active');
    }
    if (this.samroLicense.expiryDate && this.samroLicense.expiryDate < now) {
      issues.push('SAMRO license has expired');
    }
    if (this.samroLicense.paymentStatus === 'overdue') {
      issues.push('SAMRO license payment is overdue');
    }
  }
  
  // Check SAMPRA license
  if (this.licenseType === 'sampra_only' || this.licenseType === 'combined') {
    if (!this.sampraLicense.isActive) {
      issues.push('SAMPRA license is not active');
    }
    if (this.sampraLicense.expiryDate && this.sampraLicense.expiryDate < now) {
      issues.push('SAMPRA license has expired');
    }
    if (this.sampraLicense.paymentStatus === 'overdue') {
      issues.push('SAMPRA license payment is overdue');
    }
  }
  
  return {
    isCompliant: issues.length === 0,
    issues,
    complianceScore: Math.max(0, 100 - (issues.length * 25))
  };
};

// Instance method to generate renewal reminder
venueLicenseSchema.methods.generateRenewalReminder = function() {
  const reminders = [];
  const now = new Date();
  
  // Check SAMRO license expiry
  if (this.samroLicense.expiryDate) {
    const daysUntilExpiry = Math.ceil((this.samroLicense.expiryDate - now) / (1000 * 60 * 60 * 24));
    if (daysUntilExpiry <= 30 && daysUntilExpiry > 0) {
      reminders.push({
        type: 'samro_renewal',
        message: `SAMRO license expires in ${daysUntilExpiry} days`,
        urgency: daysUntilExpiry <= 7 ? 'high' : 'medium'
      });
    }
  }
  
  // Check SAMPRA license expiry
  if (this.sampraLicense.expiryDate) {
    const daysUntilExpiry = Math.ceil((this.sampraLicense.expiryDate - now) / (1000 * 60 * 60 * 24));
    if (daysUntilExpiry <= 30 && daysUntilExpiry > 0) {
      reminders.push({
        type: 'sampra_renewal',
        message: `SAMPRA license expires in ${daysUntilExpiry} days`,
        urgency: daysUntilExpiry <= 7 ? 'high' : 'medium'
      });
    }
  }
  
  return reminders;
};

module.exports = mongoose.model('VenueLicense', venueLicenseSchema);
