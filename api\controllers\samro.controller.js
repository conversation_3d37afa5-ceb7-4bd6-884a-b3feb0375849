const SAMRORates = require('../models/SAMRORates.model');
const Track = require('../models/Track.model');
const ComplianceReport = require('../models/ComplianceReport.model');
const ComplianceAlert = require('../models/ComplianceAlert.model');
const { subDays, subMonths, format } = require('date-fns');

// Get SAMRO dashboard overview
exports.getSAMRODashboard = async (req, res) => {
  try {
    const { organization } = req.user;
    const today = new Date();
    const lastMonth = subMonths(today, 1);
    const lastWeek = subDays(today, 7);

    // Get SAMRO-specific statistics
    const [
      totalTracks,
      samroRegisteredTracks,
      pendingVerification,
      recentReports,
      activeAlerts
    ] = await Promise.all([
      Track.countDocuments(),
      Track.countDocuments({ 'compliance.samroRegistered': true }),
      Track.countDocuments({
        'complianceChecks.samroDataComplete': false,
        'compliance.verificationStatus': 'pending'
      }),
      ComplianceReport.find({
        organization: { $in: ['SAMRO', 'All'] },
        createdAt: { $gte: lastMonth }
      }).sort({ createdAt: -1 }).limit(5),
      ComplianceAlert.find({
        category: 'SAMRO',
        isActive: true,
        status: { $in: ['active', 'acknowledged'] }
      }).limit(10)
    ]);

    // Calculate compliance metrics
    const complianceRate = totalTracks > 0 ? (samroRegisteredTracks / totalTracks) * 100 : 0;
    
    // Get recent activity
    const recentActivity = await Track.find({
      'compliance.samroRegistered': true,
      updatedAt: { $gte: lastWeek }
    })
    .sort({ updatedAt: -1 })
    .limit(10)
    .select('title artist updatedAt compliance');

    res.json({
      overview: {
        totalTracks,
        samroRegisteredTracks,
        complianceRate: Math.round(complianceRate * 100) / 100,
        pendingVerification,
        activeAlerts: activeAlerts.length
      },
      recentReports,
      activeAlerts,
      recentActivity
    });
  } catch (error) {
    console.error('Error fetching SAMRO dashboard:', error);
    res.status(500).json({ error: 'Failed to fetch SAMRO dashboard data' });
  }
};

// Get current SAMRO rates
exports.getSAMRORates = async (req, res) => {
  try {
    const { category, subcategory, territory = 'ZA' } = req.query;
    
    let query = {
      isActive: true,
      effectiveDate: { $lte: new Date() },
      $or: [
        { expiryDate: { $exists: false } },
        { expiryDate: null },
        { expiryDate: { $gte: new Date() } }
      ]
    };

    if (category) query.category = category;
    if (subcategory) query.subcategory = subcategory;
    if (territory) query.territory = territory;

    const rates = await SAMRORates.find(query)
      .sort({ category: 1, subcategory: 1, effectiveDate: -1 })
      .populate('createdBy', 'username')
      .populate('approvedBy', 'username');

    res.json({
      rates,
      total: rates.length
    });
  } catch (error) {
    console.error('Error fetching SAMRO rates:', error);
    res.status(500).json({ error: 'Failed to fetch SAMRO rates' });
  }
};

// Create new SAMRO rate
exports.createSAMRORate = async (req, res) => {
  try {
    const rateData = {
      ...req.body,
      createdBy: req.user.id,
      rateId: `SAMRO-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`.toUpperCase()
    };

    const rate = new SAMRORates(rateData);
    await rate.save();

    res.status(201).json({
      message: 'SAMRO rate created successfully',
      rate
    });
  } catch (error) {
    console.error('Error creating SAMRO rate:', error);
    res.status(500).json({ error: 'Failed to create SAMRO rate' });
  }
};

// Update SAMRO rate
exports.updateSAMRORate = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      updatedBy: req.user.id
    };

    const rate = await SAMRORates.findByIdAndUpdate(id, updateData, { new: true });
    if (!rate) {
      return res.status(404).json({ error: 'SAMRO rate not found' });
    }

    res.json({
      message: 'SAMRO rate updated successfully',
      rate
    });
  } catch (error) {
    console.error('Error updating SAMRO rate:', error);
    res.status(500).json({ error: 'Failed to update SAMRO rate' });
  }
};

// Verify SAMRO work registration
exports.verifyWorkRegistration = async (req, res) => {
  try {
    const { workNumber, composerIPI, title } = req.body;

    // Simulate SAMRO database lookup
    // In a real implementation, this would call SAMRO's API
    const mockVerification = {
      workNumber,
      isValid: true,
      title: title || 'Sample Track Title',
      composers: [
        {
          name: 'Sample Composer',
          ipiNumber: composerIPI || '00000000001',
          role: 'composer',
          share: 100
        }
      ],
      publishers: [
        {
          name: 'Sample Publisher',
          publisherCode: 'SP001',
          share: 50
        }
      ],
      registrationDate: new Date(),
      status: 'active'
    };

    // Create compliance alert if verification fails
    if (!mockVerification.isValid) {
      const alert = new ComplianceAlert({
        type: 'verification_required',
        category: 'SAMRO',
        severity: 'medium',
        title: 'SAMRO Work Verification Failed',
        message: `Work number ${workNumber} could not be verified`,
        contextData: {
          metadata: { workNumber, composerIPI, title }
        },
        createdBy: req.user.id
      });
      await alert.save();
    }

    res.json({
      verification: mockVerification,
      message: mockVerification.isValid ? 'Work verified successfully' : 'Work verification failed'
    });
  } catch (error) {
    console.error('Error verifying SAMRO work:', error);
    res.status(500).json({ error: 'Failed to verify SAMRO work registration' });
  }
};

// Generate SAMRO-specific report
exports.generateSAMROReport = async (req, res) => {
  try {
    const { type, startDate, endDate, storeIds } = req.body;
    
    // Build query for SAMRO-specific data
    const query = {
      'compliance.samroRegistered': true,
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };

    if (storeIds && storeIds.length > 0) {
      // This would need to be adjusted based on how tracks are linked to stores
      // For now, we'll include all tracks
    }

    const tracks = await Track.find(query)
      .populate('addedBy', 'username')
      .sort({ createdAt: -1 });

    // Calculate SAMRO-specific metrics
    const samroData = {
      totalTracks: tracks.length,
      totalComposers: [...new Set(tracks.flatMap(t => t.composers.map(c => c.name)))].length,
      totalPublishers: [...new Set(tracks.flatMap(t => t.publishers.map(p => p.name)))].length,
      complianceRate: tracks.filter(t => t.complianceChecks?.samroDataComplete).length / tracks.length * 100,
      
      // Rights holder breakdown
      rightsHolders: tracks.reduce((acc, track) => {
        track.performanceRightsSplits?.forEach(split => {
          if (!acc[split.rightsHolderName]) {
            acc[split.rightsHolderName] = {
              name: split.rightsHolderName,
              ipiNumber: split.ipiNumber,
              trackCount: 0,
              totalPercentage: 0
            };
          }
          acc[split.rightsHolderName].trackCount++;
          acc[split.rightsHolderName].totalPercentage += split.percentage;
        });
        return acc;
      }, {}),
      
      // Track details for export
      trackDetails: tracks.map(track => ({
        title: track.title,
        artist: track.artist,
        isrcCode: track.isrcCode,
        samroWorkNumber: track.samroWorkNumber,
        composers: track.composers,
        publishers: track.publishers,
        performanceRightsSplits: track.performanceRightsSplits,
        complianceScore: track.complianceChecks?.complianceScore || 0
      }))
    };

    res.json({
      reportType: type,
      period: { startDate, endDate },
      generatedAt: new Date(),
      data: samroData,
      summary: {
        totalTracks: samroData.totalTracks,
        complianceRate: Math.round(samroData.complianceRate * 100) / 100,
        rightsHoldersCount: Object.keys(samroData.rightsHolders).length
      }
    });
  } catch (error) {
    console.error('Error generating SAMRO report:', error);
    res.status(500).json({ error: 'Failed to generate SAMRO report' });
  }
};

// Export SAMRO data in XML format
exports.exportSAMROData = async (req, res) => {
  try {
    // Support both GET and POST requests
    const params = req.method === 'GET' ? req.query : req.body;
    const { storeName = 'Unknown_Store', startDate, endDate } = params;
    
    // Generate filename with store name and date range
    const formattedStartDate = startDate ? new Date(startDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0];
    const formattedEndDate = endDate ? new Date(endDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0];
    
    // In a real implementation, you would fetch the actual report data here
    // For now, we'll create a sample XML response
    const xmlData = `<?xml version="1.0" encoding="UTF-8"?>
<SAMROReport>
  <Header>
    <ReportId>SAMRO_${storeName.replace(/\s+/g, '_')}_${formattedStartDate}_to_${formattedEndDate}</ReportId>
    <GeneratedDate>${new Date().toISOString()}</GeneratedDate>
    <Period>
      <StartDate>${formattedStartDate}</StartDate>
      <EndDate>${formattedEndDate}</EndDate>
    </Period>
  </Header>
  <Summary>
    <StoreName>${storeName}</StoreName>
    <TotalTracks>0</TotalTracks>
    <TotalPlays>0</TotalPlays>
    <TotalDuration>0</TotalDuration>
  </Summary>
  <Tracks>
    <!-- Sample track data would go here -->
  </Tracks>
</SAMROReport>`;

    // Set headers for file download
    const filename = `SAMRO_monthly_${storeName.replace(/\s+/g, '_')}_${formattedStartDate}_to_${formattedEndDate}.xml`;
    res.setHeader('Content-Type', 'application/xml');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.send(xmlData);
  } catch (error) {
    console.error('Error exporting SAMRO data:', error);
    res.status(500).json({ error: 'Failed to export SAMRO data' });
  }
};

// Calculate SAMRO royalties for a venue
exports.calculateSAMRORoyalties = async (req, res) => {
  try {
    const { storeId, startDate, endDate, category = 'retail' } = req.body;
    
    // Get applicable SAMRO rates
    const rates = await SAMRORates.getCurrentRates(category);
    if (!rates.length) {
      return res.status(404).json({ error: 'No applicable SAMRO rates found' });
    }
    
    const rate = rates[0]; // Use the most recent rate
    
    // Mock calculation - in real implementation, this would use actual play data
    const mockUsageData = {
      plays: 1000,
      duration: 180000, // 50 hours in seconds
      revenue: 50000 // ZAR
    };
    
    const royaltyCalculation = rate.calculateRoyalty(mockUsageData);
    
    res.json({
      period: { startDate, endDate },
      storeId,
      rateUsed: {
        rateId: rate.rateId,
        category: rate.category,
        baseRate: rate.rates.baseRate
      },
      usageData: mockUsageData,
      calculation: royaltyCalculation
    });
  } catch (error) {
    console.error('Error calculating SAMRO royalties:', error);
    res.status(500).json({ error: 'Failed to calculate SAMRO royalties' });
  }
};

// Get SAMRO compliance alerts
exports.getSAMROAlerts = async (req, res) => {
  try {
    const { status, severity, limit = 20 } = req.query;
    
    const query = {
      category: 'SAMRO',
      isActive: true
    };
    
    if (status) query.status = status;
    if (severity) query.severity = severity;
    
    const alerts = await ComplianceAlert.find(query)
      .sort({ triggeredAt: -1 })
      .limit(parseInt(limit))
      .populate('assignedTo', 'username email')
      .populate('affectedEntities.tracks', 'title artist')
      .populate('affectedEntities.stores', 'name location');
    
    res.json({
      alerts,
      total: alerts.length
    });
  } catch (error) {
    console.error('Error fetching SAMRO alerts:', error);
    res.status(500).json({ error: 'Failed to fetch SAMRO alerts' });
  }
};
