const Event = require('../models/Event.model');
const Store = require('../models/Store.model');

// Get all events with filtering and pagination
exports.getAllEvents = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      status, 
      type, 
      priority,
      storeId,
      startDate,
      endDate,
      search
    } = req.query;

    // Build query
    const query = { isActive: true };
    
    if (status) query.status = status;
    if (type) query.type = type;
    if (priority) query.priority = priority;
    
    if (storeId) {
      query.$or = [
        { targetAudience: 'all_stores' },
        { 'stores.storeId': storeId }
      ];
    }
    
    if (startDate || endDate) {
      query.scheduledDate = {};
      if (startDate) query.scheduledDate.$gte = new Date(startDate);
      if (endDate) query.scheduledDate.$lte = new Date(endDate);
    }
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const events = await Event.find(query)
      .sort({ scheduledDate: 1, scheduledTime: 1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('stores.storeId', 'name')
      .populate('createdBy', 'username role')
      .populate('updatedBy', 'username role')
      .lean();

    const total = await Event.countDocuments(query);

    res.json({
      success: true,
      data: events,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching events:', error);
    res.status(500).json({ error: 'Failed to fetch events' });
  }
};

// Get a single event by ID
exports.getEventById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const event = await Event.findById(id)
      .populate('stores.storeId', 'name location')
      .populate('createdBy', 'username role')
      .populate('updatedBy', 'username role')
      .populate('eventData.playlistId', 'name tracks');

    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    res.json({
      success: true,
      data: event
    });
  } catch (error) {
    console.error('Error fetching event:', error);
    res.status(500).json({ error: 'Failed to fetch event' });
  }
};

// Create a new event
exports.createEvent = async (req, res) => {
  try {
    const {
      title,
      description,
      type,
      priority,
      scheduledDate,
      scheduledTime,
      duration,
      stores,
      targetAudience,
      eventData,
      recurrence,
      notifications
    } = req.body;

    // Validate stores if targetAudience is specific_stores
    if (targetAudience === 'specific_stores' && (!stores || stores.length === 0)) {
      return res.status(400).json({ error: 'Stores must be specified for specific store events' });
    }

    const event = new Event({
      title,
      description,
      type,
      priority,
      scheduledDate,
      scheduledTime,
      duration,
      stores,
      targetAudience,
      eventData,
      recurrence,
      notifications,
      createdBy: req.user.id
    });

    await event.save();
    await event.populate('stores.storeId', 'name');
    await event.populate('createdBy', 'username role');

    res.status(201).json({
      success: true,
      data: event
    });
  } catch (error) {
    console.error('Error creating event:', error);
    res.status(500).json({ error: 'Failed to create event' });
  }
};

// Update an event
exports.updateEvent = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = { ...req.body, updatedBy: req.user.id };

    const event = await Event.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    )
      .populate('stores.storeId', 'name')
      .populate('createdBy', 'username role')
      .populate('updatedBy', 'username role');

    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    res.json({
      success: true,
      data: event
    });
  } catch (error) {
    console.error('Error updating event:', error);
    res.status(500).json({ error: 'Failed to update event' });
  }
};

// Delete an event (soft delete)
exports.deleteEvent = async (req, res) => {
  try {
    const { id } = req.params;

    const event = await Event.findByIdAndUpdate(
      id,
      { isActive: false, updatedBy: req.user.id },
      { new: true }
    );

    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    res.json({
      success: true,
      message: 'Event deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting event:', error);
    res.status(500).json({ error: 'Failed to delete event' });
  }
};

// Start an event
exports.startEvent = async (req, res) => {
  try {
    const { id } = req.params;
    
    const event = await Event.findById(id);
    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    if (event.status !== 'scheduled') {
      return res.status(400).json({ error: 'Event cannot be started' });
    }

    await event.markAsStarted(req.user.id);

    res.json({
      success: true,
      data: event
    });
  } catch (error) {
    console.error('Error starting event:', error);
    res.status(500).json({ error: 'Failed to start event' });
  }
};

// Complete an event
exports.completeEvent = async (req, res) => {
  try {
    const { id } = req.params;
    const { notes } = req.body;
    
    const event = await Event.findById(id);
    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    if (event.status !== 'active') {
      return res.status(400).json({ error: 'Event cannot be completed' });
    }

    await event.markAsCompleted(req.user.id, notes);

    res.json({
      success: true,
      data: event
    });
  } catch (error) {
    console.error('Error completing event:', error);
    res.status(500).json({ error: 'Failed to complete event' });
  }
};

// Get upcoming events
exports.getUpcomingEvents = async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const events = await Event.getUpcoming(parseInt(limit));

    res.json({
      success: true,
      data: events
    });
  } catch (error) {
    console.error('Error fetching upcoming events:', error);
    res.status(500).json({ error: 'Failed to fetch upcoming events' });
  }
};

// Get events for a specific store
exports.getStoreEvents = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { startDate, endDate, status, type } = req.query;
    
    const events = await Event.getForStore(storeId, {
      startDate,
      endDate,
      status,
      type
    });

    res.json({
      success: true,
      data: events
    });
  } catch (error) {
    console.error('Error fetching store events:', error);
    res.status(500).json({ error: 'Failed to fetch store events' });
  }
};
