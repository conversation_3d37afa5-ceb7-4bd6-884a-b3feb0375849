const mongoose = require('mongoose');

const eventSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  type: {
    type: String,
    enum: ['playlist', 'maintenance', 'special', 'promotion', 'announcement'],
    default: 'playlist'
  },
  status: {
    type: String,
    enum: ['scheduled', 'active', 'completed', 'cancelled', 'draft'],
    default: 'scheduled'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  scheduledDate: {
    type: Date,
    required: true
  },
  scheduledTime: {
    type: String,
    required: true,
    match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ // HH:mm format
  },
  duration: {
    type: Number, // Duration in minutes
    default: 60
  },
  stores: [{
    storeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Store'
    },
    storeName: String
  }],
  targetAudience: {
    type: String,
    enum: ['all_stores', 'specific_stores', 'store_group'],
    default: 'all_stores'
  },
  // Event-specific data
  eventData: {
    // For playlist events
    playlistId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Playlist'
    },
    playlistName: String,
    
    // For maintenance events
    maintenanceType: {
      type: String,
      enum: ['system_update', 'server_maintenance', 'database_maintenance', 'network_maintenance']
    },
    expectedDowntime: Number, // in minutes
    
    // For special events
    specialEventType: {
      type: String,
      enum: ['holiday', 'promotion', 'announcement', 'emergency']
    },
    
    // Additional metadata
    metadata: mongoose.Schema.Types.Mixed
  },
  // Execution tracking
  execution: {
    startedAt: Date,
    completedAt: Date,
    executedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    executionStatus: {
      type: String,
      enum: ['pending', 'in_progress', 'completed', 'failed', 'cancelled'],
      default: 'pending'
    },
    executionNotes: String,
    errorMessage: String
  },
  // Recurrence settings
  recurrence: {
    isRecurring: {
      type: Boolean,
      default: false
    },
    pattern: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'yearly', 'custom']
    },
    interval: Number, // Every X days/weeks/months
    daysOfWeek: [Number], // 0-6 (Sunday-Saturday)
    endDate: Date,
    maxOccurrences: Number
  },
  // Notifications
  notifications: {
    sendNotifications: {
      type: Boolean,
      default: true
    },
    notifyBefore: {
      type: Number,
      default: 30 // minutes before event
    },
    notificationRecipients: [{
      type: String,
      enum: ['admin', 'store', 'samro_staff', 'sampra_staff', 'compliance_admin']
    }]
  },
  // Audit information
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  tags: [String]
}, {
  timestamps: true,
  indexes: [
    { scheduledDate: 1, scheduledTime: 1 },
    { status: 1, scheduledDate: 1 },
    { type: 1, status: 1 },
    { 'stores.storeId': 1 },
    { createdBy: 1 },
    { 'recurrence.isRecurring': 1 },
    { isActive: 1, scheduledDate: 1 }
  ]
});

// Virtual for checking if event is overdue
eventSchema.virtual('isOverdue').get(function() {
  if (this.status === 'completed' || this.status === 'cancelled') {
    return false;
  }
  const eventDateTime = new Date(this.scheduledDate);
  const [hours, minutes] = this.scheduledTime.split(':');
  eventDateTime.setHours(parseInt(hours), parseInt(minutes));
  return eventDateTime < new Date();
});

// Virtual for getting full event datetime
eventSchema.virtual('eventDateTime').get(function() {
  const eventDateTime = new Date(this.scheduledDate);
  const [hours, minutes] = this.scheduledTime.split(':');
  eventDateTime.setHours(parseInt(hours), parseInt(minutes));
  return eventDateTime;
});

// Method to mark event as started
eventSchema.methods.markAsStarted = function(userId) {
  this.status = 'active';
  this.execution.startedAt = new Date();
  this.execution.executedBy = userId;
  this.execution.executionStatus = 'in_progress';
  return this.save();
};

// Method to mark event as completed
eventSchema.methods.markAsCompleted = function(userId, notes) {
  this.status = 'completed';
  this.execution.completedAt = new Date();
  this.execution.executedBy = userId;
  this.execution.executionStatus = 'completed';
  if (notes) this.execution.executionNotes = notes;
  return this.save();
};

// Static method to get upcoming events
eventSchema.statics.getUpcoming = function(limit = 10) {
  return this.find({
    status: { $in: ['scheduled', 'active'] },
    isActive: true,
    scheduledDate: { $gte: new Date() }
  })
  .sort({ scheduledDate: 1, scheduledTime: 1 })
  .limit(limit)
  .populate('stores.storeId', 'name')
  .populate('createdBy', 'username role');
};

// Static method to get events for a specific store
eventSchema.statics.getForStore = function(storeId, options = {}) {
  const { startDate, endDate, status, type } = options;
  
  const query = {
    $or: [
      { targetAudience: 'all_stores' },
      { 'stores.storeId': storeId }
    ],
    isActive: true
  };
  
  if (startDate || endDate) {
    query.scheduledDate = {};
    if (startDate) query.scheduledDate.$gte = new Date(startDate);
    if (endDate) query.scheduledDate.$lte = new Date(endDate);
  }
  
  if (status) query.status = status;
  if (type) query.type = type;
  
  return this.find(query)
    .sort({ scheduledDate: 1, scheduledTime: 1 })
    .populate('stores.storeId', 'name')
    .populate('createdBy', 'username role');
};

module.exports = mongoose.model('Event', eventSchema);
