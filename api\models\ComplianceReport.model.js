const mongoose = require('mongoose');

const complianceReportSchema = new mongoose.Schema({
  reportId: { type: String, unique: true, required: true },
  reportType: { 
    type: String, 
    enum: ['daily', 'weekly', 'monthly', 'quarterly', 'annual', 'custom'],
    required: true 
  },
  organization: { 
    type: String, 
    enum: ['SAMRO', 'SAMPRA', 'RISA', 'All'], 
    required: true 
  },
  dateRange: {
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true }
  },
  stores: [{
    storeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Store' },
    storeName: String,
    location: String
  }],
  trackData: [{
    trackId: { type: mongoose.Schema.Types.ObjectId, ref: 'Track' },
    title: String,
    artist: String,
    album: String,
    genre: String,
    duration: Number,
    isrcCode: String,
    iswcCode: String,
    composers: [String],
    publishers: [String],
    publisherInfo: {
      publisher: String,
      publisherCode: String,
      copyrightOwner: String
    },
    playCount: Number,
    totalDuration: Number,
    playbackLogs: [{
      playedAt: Date,
      startTime: Date,
      endTime: Date,
      durationPlayed: Number,
      completionPercentage: Number,
      storeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Store' },
      storeName: String,
      location: {
        city: String,
        province: String,
        country: String
      },
      deviceId: String,
      sessionId: String,
      // New fields for SAMRO/SAMPRA compliance
      playbackSource: {
        type: String,
        enum: ['music_player', 'radio_stream', 'silence', 'unknown'],
        default: 'unknown'
      },
      systemUsed: {
        type: String,
        enum: ['manual', 'automated', 'scheduled', 'unknown'],
        default: 'unknown'
      },
      triggerType: {
        type: String,
        enum: ['user_action', 'playlist_auto', 'schedule_auto', 'system_auto', 'unknown'],
        default: 'unknown'
      }
    }],
    compliance: {
      samroRegistered: Boolean,
      sampraRegistered: Boolean,
      risaCompliant: Boolean,
      verificationStatus: String
    }
  }],
  summary: {
    totalTracks: Number,
    totalPlays: Number,
    totalDuration: Number,
    uniqueArtists: Number
  },
  compliance: {
    risaCompliant: { type: Boolean, default: true },
    samroCompliant: { type: Boolean, default: true },
    sampraCompliant: { type: Boolean, default: true },
    issues: [String],
    verificationStatus: { 
      type: String, 
      enum: ['pending', 'verified', 'rejected', 'requires_review'],
      default: 'pending'
    }
  },
  generatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  verifiedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  exportedFormats: [{
    format: { type: String, enum: ['csv', 'pdf', 'excel', 'xml'] },
    filePath: String,
    exportedAt: Date,
    exportedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
  }],
  auditTrail: [{
    action: String,
    performedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    timestamp: { type: Date, default: Date.now },
    details: String
  }],
  status: { 
    type: String, 
    enum: ['draft', 'pending_review', 'approved', 'submitted', 'archived'],
    default: 'draft'
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Generate unique report ID before saving
complianceReportSchema.pre('save', function(next) {
  if (!this.reportId) {
    const date = new Date();
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
    const randomStr = Math.random().toString(36).substring(2, 8).toUpperCase();
    this.reportId = `${this.organization}-${dateStr}-${randomStr}`;
  }
  this.updatedAt = Date.now();
  next();
});

// Index for efficient querying
complianceReportSchema.index({ organization: 1, 'dateRange.startDate': 1 });
complianceReportSchema.index({ reportType: 1, status: 1 });
complianceReportSchema.index({ 'compliance.verificationStatus': 1 });

module.exports = mongoose.model('ComplianceReport', complianceReportSchema);
