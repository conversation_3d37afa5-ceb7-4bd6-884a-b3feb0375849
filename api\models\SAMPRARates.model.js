const mongoose = require('mongoose');

const sampraRatesSchema = new mongoose.Schema({
  rateId: { type: String, unique: true, required: true },
  category: {
    type: String,
    enum: ['radio', 'television', 'streaming', 'retail', 'restaurant', 'hotel', 'gym', 'public_venue', 'digital_platform'],
    required: true
  },
  subcategory: String, // e.g., 'commercial_radio', 'community_radio', 'music_streaming', 'video_streaming'
  
  // Rate structure for needletime royalties
  rateStructure: {
    type: String,
    enum: ['flat_rate', 'percentage', 'per_play', 'per_minute', 'revenue_share', 'audience_based'],
    required: true
  },
  
  // Rate values for recording artists and producers
  rates: {
    artistRate: { type: Number, required: true }, // Rate for performing artists
    producerRate: { type: Number, required: true }, // Rate for producers
    labelRate: { type: Number, required: true }, // Rate for record labels
    currency: { type: String, default: 'ZAR' },
    unit: { type: String, enum: ['per_play', 'per_minute', 'per_month', 'per_year', 'percentage'], required: true },
    
    // Split percentages (must total 100%)
    splits: {
      artistPercentage: { type: Number, default: 50, min: 0, max: 100 },
      producerPercentage: { type: Number, default: 25, min: 0, max: 100 },
      labelPercentage: { type: Number, default: 25, min: 0, max: 100 }
    },
    
    // Minimum and maximum fees
    minimumFee: Number,
    maximumFee: Number,
    administrationFee: Number,
    vatRate: { type: Number, default: 15 } // VAT percentage
  },
  
  // Venue/platform size brackets
  venueBracket: {
    type: String,
    enum: ['micro', 'small', 'medium', 'large', 'enterprise'],
    description: String
  },
  
  // Geographic scope
  territory: {
    type: String,
    default: 'ZA',
    enum: ['ZA', 'SADC', 'AFRICA', 'GLOBAL']
  },
  regions: [String],
  
  // Validity period
  effectiveDate: { type: Date, required: true },
  expiryDate: Date,
  isActive: { type: Boolean, default: true },
  
  // Usage and audience parameters
  audienceFactors: {
    baseAudienceSize: Number,
    peakTimeMultiplier: { type: Number, default: 1.5 },
    weekendMultiplier: { type: Number, default: 1.2 },
    holidayMultiplier: { type: Number, default: 1.3 }
  },
  
  // Time-based restrictions
  timeRestrictions: [{
    startTime: String,
    endTime: String,
    daysOfWeek: [{ type: String, enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] }],
    multiplier: { type: Number, default: 1 }
  }],
  
  // Content restrictions
  contentRestrictions: {
    minDuration: Number, // Minimum track duration in seconds
    maxDuration: Number, // Maximum track duration in seconds
    genreRestrictions: [String],
    languageRestrictions: [String],
    explicitContentAllowed: { type: Boolean, default: true }
  },
  
  // Calculation method
  calculationMethod: {
    type: String,
    enum: ['simple', 'weighted', 'duration_based', 'audience_weighted', 'revenue_share'],
    default: 'duration_based'
  },
  
  // Metadata
  description: String,
  notes: String,
  legalReference: String, // Reference to SAMPRA tariff document
  
  // Audit trail
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  approvedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  approvalDate: Date,
  
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Indexes
sampraRatesSchema.index({ category: 1, subcategory: 1, effectiveDate: -1 });
sampraRatesSchema.index({ territory: 1, isActive: 1 });
sampraRatesSchema.index({ effectiveDate: 1, expiryDate: 1 });

// Pre-save middleware
sampraRatesSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Validate that split percentages total 100%
  const totalSplit = this.rates.splits.artistPercentage + 
                    this.rates.splits.producerPercentage + 
                    this.rates.splits.labelPercentage;
  
  if (Math.abs(totalSplit - 100) > 0.01) {
    return next(new Error('Split percentages must total 100%'));
  }
  
  next();
});

// Static method to get current active rates
sampraRatesSchema.statics.getCurrentRates = function(category, subcategory = null, territory = 'ZA') {
  const query = {
    category,
    territory,
    isActive: true,
    effectiveDate: { $lte: new Date() },
    $or: [
      { expiryDate: { $exists: false } },
      { expiryDate: null },
      { expiryDate: { $gte: new Date() } }
    ]
  };
  
  if (subcategory) {
    query.subcategory = subcategory;
  }
  
  return this.find(query).sort({ effectiveDate: -1 });
};

// Instance method to calculate needletime royalty
sampraRatesSchema.methods.calculateNeedletimeRoyalty = function(params) {
  const { plays, duration, revenue, audienceSize, timeOfDay, dayOfWeek } = params;
  let baseRoyalty = 0;
  
  // Calculate base royalty based on rate structure
  switch (this.rateStructure) {
    case 'flat_rate':
      baseRoyalty = this.rates.artistRate + this.rates.producerRate + this.rates.labelRate;
      break;
      
    case 'per_play':
      baseRoyalty = plays * (this.rates.artistRate + this.rates.producerRate + this.rates.labelRate);
      break;
      
    case 'per_minute':
      const totalMinutes = (plays * duration) / 60;
      baseRoyalty = totalMinutes * (this.rates.artistRate + this.rates.producerRate + this.rates.labelRate);
      break;
      
    case 'percentage':
      baseRoyalty = revenue * ((this.rates.artistRate + this.rates.producerRate + this.rates.labelRate) / 100);
      break;
      
    case 'audience_based':
      const audienceFactor = audienceSize / (this.audienceFactors.baseAudienceSize || 1000);
      baseRoyalty = plays * (this.rates.artistRate + this.rates.producerRate + this.rates.labelRate) * audienceFactor;
      break;
  }
  
  // Apply time-based multipliers
  let timeMultiplier = 1;
  if (timeOfDay && dayOfWeek) {
    const restriction = this.timeRestrictions.find(r => 
      r.daysOfWeek.includes(dayOfWeek.toLowerCase()) &&
      timeOfDay >= r.startTime && timeOfDay <= r.endTime
    );
    if (restriction) {
      timeMultiplier = restriction.multiplier;
    }
  }
  
  baseRoyalty *= timeMultiplier;
  
  // Apply minimum and maximum fees
  if (this.rates.minimumFee && baseRoyalty < this.rates.minimumFee) {
    baseRoyalty = this.rates.minimumFee;
  }
  if (this.rates.maximumFee && baseRoyalty > this.rates.maximumFee) {
    baseRoyalty = this.rates.maximumFee;
  }
  
  // Calculate splits
  const artistRoyalty = baseRoyalty * (this.rates.splits.artistPercentage / 100);
  const producerRoyalty = baseRoyalty * (this.rates.splits.producerPercentage / 100);
  const labelRoyalty = baseRoyalty * (this.rates.splits.labelPercentage / 100);
  
  // Add administration fee
  const adminFee = this.rates.administrationFee || 0;
  const totalBeforeVAT = baseRoyalty + adminFee;
  
  // Calculate VAT
  const vat = totalBeforeVAT * (this.rates.vatRate / 100);
  
  return {
    baseRoyalty,
    splits: {
      artist: artistRoyalty,
      producer: producerRoyalty,
      label: labelRoyalty
    },
    administrationFee: adminFee,
    vat,
    totalRoyalty: totalBeforeVAT + vat,
    currency: this.rates.currency,
    timeMultiplier
  };
};

module.exports = mongoose.model('SAMPRARates', sampraRatesSchema);
