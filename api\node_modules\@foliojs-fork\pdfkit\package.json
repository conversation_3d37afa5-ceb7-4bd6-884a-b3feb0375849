{"name": "@foliojs-fork/pdfkit", "description": "A PDF generation library for Node.js", "keywords": ["pdf", "pdf writer", "pdf generator", "graphics", "document", "vector"], "version": "0.15.3", "homepage": "http://pdfkit.org/", "author": {"name": "Devon Govett", "email": "<EMAIL>", "url": "http://badassjs.com/"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/foliojs-fork/pdfkit.git"}, "bugs": "https://github.com/foliojs-fork/pdfkit/issues", "devDependencies": {"@babel/core": "^7.11.6", "@babel/plugin-external-helpers": "^7.10.4", "@babel/preset-env": "^7.11.5", "babel-jest": "^26.3.0", "blob-stream": "^0.1.2", "brace": "^0.11.1", "brfs": "~2.0.2", "browserify": "^16.5.0", "canvas": "^3.0.0", "codemirror": "~5.49.2", "eslint": "^7.8.1", "gh-pages": "^3.1.0", "iconv-lite": "^0.5.0", "jest": "^29.4.3", "jest-environment-jsdom": "^29.4.3", "jest-image-snapshot": "^6.1.0", "markdown": "~0.5.0", "pdfjs-dist": "^2.4.456", "prettier": "1.19.1", "pug": "^2.0.4", "rollup": "^1.27.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-cpy": "^2.0.1"}, "dependencies": {"crypto-js": "^4.2.0", "@foliojs-fork/fontkit": "^1.9.2", "@foliojs-fork/linebreak": "^1.1.1", "jpeg-exif": "^1.1.4", "png-js": "^1.0.0"}, "scripts": {"prepublishOnly": "npm run build", "build": "rollup -c", "build-standalone": "browserify --standalone PDFDocument --ignore crypto --ignore iconv-lite js/pdfkit.js > js/pdfkit.standalone.js", "browserify-example": "browserify examples/browserify/browser.js > examples/browserify/bundle.js", "pdf-guide": "node docs/generate.js", "website": "node docs/generate_website.js", "publish-website": "node docs/publish_website.js", "docs": "npm run pdf-guide && npm run website && npm run browserify-example", "lint": "eslint {lib,tests}/**/*.js", "prettier": "prettier {lib,tests,examples,docs}/**/*.js", "test": "jest -i", "test:visual": "jest visual/ -i", "test:unit": "jest unit/"}, "main": "js/pdfkit.js", "module": "js/pdfkit.es.js", "browserify": {"transform": ["brfs"]}, "engine": ["node >= v18.0.0"], "jest": {"testEnvironment": "jest-environment-jsdom", "testPathIgnorePatterns": ["/node_modules/", "<rootDir>/examples/"], "setupFilesAfterEnv": ["<rootDir>/tests/unit/setupTests.js"]}}