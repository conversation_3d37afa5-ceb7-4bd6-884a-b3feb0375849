{"version": 3, "file": "base.js", "sources": ["src/base.js"], "sourcesContent": ["import r from '@foliojs-fork/restructure';\r\nconst fs = require('fs');\r\n\r\nvar fontkit = {};\r\nexport default fontkit;\r\n\r\nfontkit.logErrors = false;\r\n\r\nlet formats = [];\r\nfontkit.registerFormat = function(format) {\r\n  formats.push(format);\r\n};\r\n\r\nfontkit.openSync = function(filename, postscriptName) {\r\n  let buffer = fs.readFileSync(filename);\r\n  return fontkit.create(buffer, postscriptName);\r\n};\r\n\r\nfontkit.open = function(filename, postscriptName, callback) {\r\n  if (typeof postscriptName === 'function') {\r\n    callback = postscriptName;\r\n    postscriptName = null;\r\n  }\r\n\r\n  fs.readFile(filename, function(err, buffer) {\r\n    if (err) { return callback(err); }\r\n\r\n    try {\r\n      var font = fontkit.create(buffer, postscriptName);\r\n    } catch (e) {\r\n      return callback(e);\r\n    }\r\n\r\n    return callback(null, font);\r\n  });\r\n\r\n  return;\r\n};\r\n\r\nfontkit.create = function(buffer, postscriptName) {\r\n  for (let i = 0; i < formats.length; i++) {\r\n    let format = formats[i];\r\n    if (format.probe(buffer)) {\r\n      let font = new format(new r.DecodeStream(buffer));\r\n      if (postscriptName) {\r\n        return font.getFont(postscriptName);\r\n      }\r\n\r\n      return font;\r\n    }\r\n  }\r\n\r\n  throw new Error('Unknown font format');\r\n};\r\n\r\nfontkit.defaultLanguage = 'en';\r\nfontkit.setDefaultLanguage = function(lang = 'en') {\r\n  fontkit.defaultLanguage = lang;\r\n};"], "names": ["fs", "require", "fontkit", "logErrors", "formats", "registerFormat", "format", "push", "openSync", "filename", "postscriptName", "buffer", "readFileSync", "create", "open", "callback", "readFile", "err", "font", "e", "i", "length", "probe", "r", "DecodeStream", "getFont", "Error", "defaultLanguage", "setDefaultLanguage", "lang"], "mappings": ";;;;;;AACA,IAAMA,EAAE,GAAGC,OAAO,CAAC,IAAD,CAAlB;;AAEA,IAAIC,OAAO,GAAG,EAAd;AAGAA,OAAO,CAACC,SAAR,GAAoB,KAApB;AAEA,IAAIC,OAAO,GAAG,EAAd;;AACAF,OAAO,CAACG,cAAR,GAAyB,UAASC,MAAT,EAAiB;AACxCF,EAAAA,OAAO,CAACG,IAAR,CAAaD,MAAb;AACD,CAFD;;AAIAJ,OAAO,CAACM,QAAR,GAAmB,UAASC,QAAT,EAAmBC,cAAnB,EAAmC;AACpD,MAAIC,MAAM,GAAGX,EAAE,CAACY,YAAH,CAAgBH,QAAhB,CAAb;AACA,SAAOP,OAAO,CAACW,MAAR,CAAeF,MAAf,EAAuBD,cAAvB,CAAP;AACD,CAHD;;AAKAR,OAAO,CAACY,IAAR,GAAe,UAASL,QAAT,EAAmBC,cAAnB,EAAmCK,QAAnC,EAA6C;AAC1D,MAAI,OAAOL,cAAP,KAA0B,UAA9B,EAA0C;AACxCK,IAAAA,QAAQ,GAAGL,cAAX;AACAA,IAAAA,cAAc,GAAG,IAAjB;AACD;;AAEDV,EAAAA,EAAE,CAACgB,QAAH,CAAYP,QAAZ,EAAsB,UAASQ,GAAT,EAAcN,MAAd,EAAsB;AAC1C,QAAIM,GAAJ,EAAS;AAAE,aAAOF,QAAQ,CAACE,GAAD,CAAf;AAAuB;;AAElC,QAAI;AACF,UAAIC,IAAI,GAAGhB,OAAO,CAACW,MAAR,CAAeF,MAAf,EAAuBD,cAAvB,CAAX;AACD,KAFD,CAEE,OAAOS,CAAP,EAAU;AACV,aAAOJ,QAAQ,CAACI,CAAD,CAAf;AACD;;AAED,WAAOJ,QAAQ,CAAC,IAAD,EAAOG,IAAP,CAAf;AACD,GAVD;AAYA;AACD,CAnBD;;AAqBAhB,OAAO,CAACW,MAAR,GAAiB,UAASF,MAAT,EAAiBD,cAAjB,EAAiC;AAChD,OAAK,IAAIU,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGhB,OAAO,CAACiB,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACvC,QAAId,MAAM,GAAGF,OAAO,CAACgB,CAAD,CAApB;;AACA,QAAId,MAAM,CAACgB,KAAP,CAAaX,MAAb,CAAJ,EAA0B;AACxB,UAAIO,IAAI,GAAG,IAAIZ,MAAJ,CAAW,IAAIiB,CAAC,CAACC,YAAN,CAAmBb,MAAnB,CAAX,CAAX;;AACA,UAAID,cAAJ,EAAoB;AAClB,eAAOQ,IAAI,CAACO,OAAL,CAAaf,cAAb,CAAP;AACD;;AAED,aAAOQ,IAAP;AACD;AACF;;AAED,QAAM,IAAIQ,KAAJ,CAAU,qBAAV,CAAN;AACD,CAdD;;AAgBAxB,OAAO,CAACyB,eAAR,GAA0B,IAA1B;;AACAzB,OAAO,CAAC0B,kBAAR,GAA6B,UAASC,IAAI,GAAG,IAAhB,EAAsB;AACjD3B,EAAAA,OAAO,CAACyB,eAAR,GAA0BE,IAA1B;AACD,CAFD;;;;"}