const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  message: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  type: {
    type: String,
    enum: ['info', 'warning', 'error', 'success'],
    default: 'info'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  recipients: [{
    type: String,
    enum: ['all', 'admin', 'store', 'samro_staff', 'sampra_staff', 'compliance_admin']
  }],
  read: {
    type: Boolean,
    default: false
  },
  readBy: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    readAt: {
      type: Date,
      default: Date.now
    }
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
  },
  isActive: {
    type: Boolean,
    default: true
  },
  metadata: {
    source: String, // system, manual, automated
    category: String, // maintenance, alert, announcement, etc.
    relatedEntity: {
      type: String, // store, track, playlist, etc.
      id: mongoose.Schema.Types.ObjectId
    },
    actionRequired: Boolean,
    actionUrl: String
  }
}, {
  timestamps: true,
  indexes: [
    { createdAt: -1 },
    { type: 1, priority: 1 },
    { read: 1, createdAt: -1 },
    { recipients: 1, isActive: 1 },
    { expiresAt: 1 }, // For TTL index
    { 'metadata.category': 1 }
  ]
});

// Index for automatic expiration
notificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Virtual for checking if notification is expired
notificationSchema.virtual('isExpired').get(function() {
  return this.expiresAt && this.expiresAt < new Date();
});

// Method to mark as read by a specific user
notificationSchema.methods.markAsReadBy = function(userId) {
  if (!this.readBy.some(r => r.userId.toString() === userId.toString())) {
    this.readBy.push({ userId, readAt: new Date() });
  }
  return this.save();
};

// Static method to get unread count for a user
notificationSchema.statics.getUnreadCount = function(userRole) {
  return this.countDocuments({
    recipients: { $in: ['all', userRole] },
    isActive: true,
    read: false,
    expiresAt: { $gt: new Date() }
  });
};

// Static method to get notifications for a specific user role
notificationSchema.statics.getForUser = function(userRole, options = {}) {
  const { page = 1, limit = 20, type, priority } = options;
  
  const query = {
    recipients: { $in: ['all', userRole] },
    isActive: true,
    expiresAt: { $gt: new Date() }
  };
  
  if (type) query.type = type;
  if (priority) query.priority = priority;
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .populate('createdBy', 'username role');
};

module.exports = mongoose.model('Notification', notificationSchema);
