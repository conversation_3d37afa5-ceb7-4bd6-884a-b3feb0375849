const mongoose = require('mongoose');

const settingsSchema = new mongoose.Schema({
  // General Settings
  systemName: {
    type: String,
    default: 'TrakSong Music System',
    required: true,
    trim: true
  },
  timezone: {
    type: String,
    default: 'Africa/Johannesburg',
    required: true
  },
  language: {
    type: String,
    default: 'en',
    enum: ['en', 'af', 'zu', 'xh'],
    required: true
  },
  dateFormat: {
    type: String,
    default: 'DD/MM/YYYY',
    enum: ['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD'],
    required: true
  },
  timeFormat: {
    type: String,
    default: '24h',
    enum: ['12h', '24h'],
    required: true
  },

  // Audio Settings
  defaultBitrate: {
    type: Number,
    default: 320,
    min: 128,
    max: 320,
    required: true
  },
  audioFormat: {
    type: String,
    default: 'mp3',
    enum: ['mp3', 'wav', 'flac', 'aac'],
    required: true
  },
  volumeLimit: {
    type: Number,
    default: 85,
    min: 50,
    max: 100,
    required: true
  },
  crossfadeEnabled: {
    type: Boolean,
    default: true
  },
  crossfadeDuration: {
    type: Number,
    default: 3,
    min: 1,
    max: 10
  },

  // Security Settings
  sessionTimeout: {
    type: Number,
    default: 30,
    min: 5,
    max: 480,
    required: true
  },
  passwordMinLength: {
    type: Number,
    default: 8,
    min: 6,
    max: 32,
    required: true
  },
  requireTwoFactor: {
    type: Boolean,
    default: false
  },
  allowRemoteAccess: {
    type: Boolean,
    default: true
  },
  encryptionEnabled: {
    type: Boolean,
    default: true
  },

  // Notification Settings
  emailNotifications: {
    type: Boolean,
    default: true
  },
  smsNotifications: {
    type: Boolean,
    default: false
  },
  pushNotifications: {
    type: Boolean,
    default: true
  },
  systemAlerts: {
    type: Boolean,
    default: true
  },
  maintenanceAlerts: {
    type: Boolean,
    default: true
  },

  // Storage Settings
  maxFileSize: {
    type: Number,
    default: 50,
    min: 1,
    max: 500,
    required: true
  },
  autoCleanup: {
    type: Boolean,
    default: true
  },
  cleanupDays: {
    type: Number,
    default: 30,
    min: 1,
    max: 365
  },
  compressionEnabled: {
    type: Boolean,
    default: true
  },

  // Backup Settings
  autoBackup: {
    type: Boolean,
    default: true
  },
  backupFrequency: {
    type: String,
    default: 'daily',
    enum: ['hourly', 'daily', 'weekly', 'monthly']
  },
  backupRetention: {
    type: Number,
    default: 7,
    min: 1,
    max: 365
  },
  cloudBackup: {
    type: Boolean,
    default: false
  },

  // Audit Fields
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  version: {
    type: Number,
    default: 1
  }
}, {
  timestamps: true,
  collection: 'settings'
});

// Ensure only one settings document exists
settingsSchema.index({}, { unique: true });

// Pre-save middleware to increment version
settingsSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.version += 1;
  }
  next();
});

// Static method to get or create settings
settingsSchema.statics.getSettings = async function() {
  let settings = await this.findOne();
  if (!settings) {
    settings = await this.create({});
  }
  return settings;
};

// Static method to update settings
settingsSchema.statics.updateSettings = async function(updates, userId) {
  const settings = await this.getSettings();
  Object.assign(settings, updates);
  settings.lastModifiedBy = userId;
  return await settings.save();
};

module.exports = mongoose.model('Settings', settingsSchema);
