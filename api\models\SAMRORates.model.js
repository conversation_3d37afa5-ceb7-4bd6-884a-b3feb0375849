const mongoose = require('mongoose');

const samroRatesSchema = new mongoose.Schema({
  rateId: { type: String, unique: true, required: true },
  category: {
    type: String,
    enum: ['radio', 'retail', 'restaurant', 'hotel', 'gym', 'office', 'public_venue', 'streaming', 'broadcast'],
    required: true
  },
  subcategory: String, // e.g., 'commercial_radio', 'community_radio', 'small_retail', 'large_retail'
  
  // Rate structure
  rateStructure: {
    type: String,
    enum: ['flat_rate', 'percentage', 'per_play', 'tiered', 'revenue_based'],
    required: true
  },
  
  // Rate values
  rates: {
    baseRate: { type: Number, required: true }, // Base rate amount
    currency: { type: String, default: 'ZAR' },
    unit: { type: String, enum: ['per_play', 'per_month', 'per_year', 'percentage', 'per_minute'], required: true },
    
    // Tiered rates (if applicable)
    tiers: [{
      minValue: Number, // Minimum threshold for this tier
      maxValue: Number, // Maximum threshold for this tier
      rate: Number,
      description: String
    }],
    
    // Additional fees
    minimumFee: Number,
    maximumFee: Number,
    administrationFee: Number,
    vatRate: { type: Number, default: 15 } // VAT percentage
  },
  
  // Venue size/revenue brackets
  venueBracket: {
    type: String,
    enum: ['micro', 'small', 'medium', 'large', 'enterprise'],
    description: String
  },
  
  // Geographic scope
  territory: {
    type: String,
    default: 'ZA',
    enum: ['ZA', 'SADC', 'AFRICA', 'GLOBAL']
  },
  regions: [String], // Specific provinces or regions
  
  // Validity period
  effectiveDate: { type: Date, required: true },
  expiryDate: Date,
  isActive: { type: Boolean, default: true },
  
  // Usage restrictions
  restrictions: {
    maxPlaysPerDay: Number,
    maxPlaysPerMonth: Number,
    timeRestrictions: [{
      startTime: String, // e.g., "09:00"
      endTime: String,   // e.g., "17:00"
      daysOfWeek: [{ type: String, enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] }]
    }],
    genreRestrictions: [String],
    languageRestrictions: [String]
  },
  
  // Calculation parameters
  calculationMethod: {
    type: String,
    enum: ['simple', 'weighted', 'duration_based', 'audience_based'],
    default: 'simple'
  },
  
  // Metadata
  description: String,
  notes: String,
  legalReference: String, // Reference to SAMRO tariff document
  
  // Audit trail
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  approvedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  approvalDate: Date,
  
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Indexes for efficient querying
samroRatesSchema.index({ category: 1, subcategory: 1, effectiveDate: -1 });
samroRatesSchema.index({ territory: 1, isActive: 1 });
samroRatesSchema.index({ effectiveDate: 1, expiryDate: 1 });

// Pre-save middleware to update timestamps
samroRatesSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Static method to get current active rates for a category
samroRatesSchema.statics.getCurrentRates = function(category, subcategory = null, territory = 'ZA') {
  const query = {
    category,
    territory,
    isActive: true,
    effectiveDate: { $lte: new Date() },
    $or: [
      { expiryDate: { $exists: false } },
      { expiryDate: null },
      { expiryDate: { $gte: new Date() } }
    ]
  };
  
  if (subcategory) {
    query.subcategory = subcategory;
  }
  
  return this.find(query).sort({ effectiveDate: -1 });
};

// Instance method to calculate royalty for given parameters
samroRatesSchema.methods.calculateRoyalty = function(params) {
  const { plays, duration, revenue, audienceSize } = params;
  let royalty = 0;
  
  switch (this.rateStructure) {
    case 'flat_rate':
      royalty = this.rates.baseRate;
      break;
      
    case 'per_play':
      royalty = plays * this.rates.baseRate;
      break;
      
    case 'percentage':
      royalty = revenue * (this.rates.baseRate / 100);
      break;
      
    case 'tiered':
      // Calculate based on tier structure
      const applicableTier = this.rates.tiers.find(tier => 
        (!tier.minValue || plays >= tier.minValue) && 
        (!tier.maxValue || plays <= tier.maxValue)
      );
      if (applicableTier) {
        royalty = plays * applicableTier.rate;
      }
      break;
      
    case 'revenue_based':
      royalty = revenue * (this.rates.baseRate / 100);
      break;
  }
  
  // Apply minimum and maximum fees
  if (this.rates.minimumFee && royalty < this.rates.minimumFee) {
    royalty = this.rates.minimumFee;
  }
  if (this.rates.maximumFee && royalty > this.rates.maximumFee) {
    royalty = this.rates.maximumFee;
  }
  
  // Add administration fee
  if (this.rates.administrationFee) {
    royalty += this.rates.administrationFee;
  }
  
  // Calculate VAT
  const vat = royalty * (this.rates.vatRate / 100);
  
  return {
    baseRoyalty: royalty,
    vat,
    totalRoyalty: royalty + vat,
    currency: this.rates.currency
  };
};

module.exports = mongoose.model('SAMRORates', samroRatesSchema);
